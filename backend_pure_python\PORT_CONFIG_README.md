# 暨阳湖大酒店传菜管理系统 - 端口配置指南

## 📋 概述

本系统支持灵活的端口配置，用户可以通过多种方式指定系统运行端口，有效解决端口冲突问题。

## 🔧 端口配置方法

### 1. 命令行参数（优先级最高）

```bash
# 指定端口启动
python main.py --port 8002

# 指定主机和端口
python main.py --host 127.0.0.1 --port 8002

# 使用启动脚本指定端口
# Windows
start_system.bat 8002

# Linux/macOS
./start_system.sh 8002
```

### 2. 环境变量（优先级中等）

```bash
# 设置环境变量
export HOTEL_SYSTEM_PORT=8002
export HOTEL_SYSTEM_HOST=0.0.0.0

# Windows
set HOTEL_SYSTEM_PORT=8002
set HOTEL_SYSTEM_HOST=0.0.0.0

# 然后启动系统
python main.py
```

### 3. 配置文件（优先级较低）

创建 `system_config.ini` 文件：

```ini
[server]
port = 8002
host = 0.0.0.0
debug = false
```

### 4. 环境变量文件（.env）

创建 `.env` 文件：

```env
HOTEL_SYSTEM_PORT=8002
HOTEL_SYSTEM_HOST=0.0.0.0
HOTEL_SYSTEM_DEBUG=false
```

## 🛠️ 配置管理工具

### 创建默认配置文件

```bash
# 创建默认配置文件
python main.py --create-config

# 或使用配置管理工具
python config_manager.py create
```

### 查看当前配置

```bash
python config_manager.py show
```

### 设置配置

```bash
# 设置端口
python config_manager.py set --port 8002

# 设置主机地址
python config_manager.py set --host 127.0.0.1

# 设置调试模式
python config_manager.py set --debug true
```

### 验证配置

```bash
python config_manager.py validate
```

### 重置配置

```bash
python config_manager.py reset
```

## 🔍 端口管理工具

### 检查端口占用

```bash
# 检查特定端口
python port_manager.py --check 8001

# 获取端口详细信息
python port_manager.py --info 8001
```

### 清理端口

```bash
# 杀死占用端口的进程
python port_manager.py --kill 8001
```

### 查找可用端口

```bash
# 从指定端口开始查找可用端口
python port_manager.py --find 8001
```

### 扫描端口范围

```bash
# 扫描端口范围
python port_manager.py --scan 8000 8010
```

## 🚀 启动脚本功能

### Windows (start_system.bat)

- ✅ 自动检测端口冲突
- ✅ 提供端口冲突处理选项
- ✅ 支持命令行参数指定端口
- ✅ 支持环境变量配置

```cmd
# 默认端口启动
start_system.bat

# 指定端口启动
start_system.bat 8002

# 设置环境变量后启动
set HOTEL_SYSTEM_PORT=8002
start_system.bat
```

### Linux/macOS (start_system.sh)

- ✅ 自动检测端口冲突
- ✅ 提供端口冲突处理选项
- ✅ 支持命令行参数指定端口
- ✅ 支持环境变量配置
- ✅ 依赖包检查

```bash
# 默认端口启动
./start_system.sh

# 指定端口启动
./start_system.sh 8002

# 设置环境变量后启动
export HOTEL_SYSTEM_PORT=8002
./start_system.sh

# 后台运行
nohup ./start_system.sh > system.log 2>&1 &
```

## ⚠️ 端口冲突处理

当检测到端口冲突时，启动脚本会提供以下选项：

1. **自动杀死占用进程** - 自动清理端口并继续启动
2. **使用其他端口** - 自动选择下一个可用端口
3. **手动处理** - 退出脚本，用户手动处理冲突

## 📊 配置优先级

配置的优先级从高到低：

1. **命令行参数** (`--port`, `--host`)
2. **环境变量** (`HOTEL_SYSTEM_PORT`, `HOTEL_SYSTEM_HOST`)
3. **配置文件** (`system_config.ini`)
4. **默认值** (端口: 5109, 主机: 0.0.0.0)

## 🔒 端口有效性验证

- 端口范围：1024-65535
- 自动检测端口占用
- 提供端口冲突解决方案

## 💡 使用建议

### 开发环境

```bash
# 使用配置文件
python config_manager.py create
python config_manager.py set --port 8001 --debug true
```

### 生产环境

```bash
# 使用环境变量
export HOTEL_SYSTEM_PORT=5109
export HOTEL_SYSTEM_HOST=0.0.0.0
export HOTEL_SYSTEM_DEBUG=false
```

### 多实例部署

```bash
# 实例1
python main.py --port 5109

# 实例2
python main.py --port 5110

# 实例3
python main.py --port 5111
```

## 🐛 故障排除

### 端口被占用

```bash
# 查看占用进程
python port_manager.py --info 8001

# 清理端口
python port_manager.py --kill 8001

# 查找可用端口
python port_manager.py --find 8001
```

### 配置问题

```bash
# 验证配置
python config_manager.py validate

# 查看当前配置
python config_manager.py show

# 重置配置
python config_manager.py reset
```

### 权限问题

- Linux/macOS: 使用1024以上端口避免需要root权限
- Windows: 以管理员身份运行可能需要的操作

## 📞 技术支持

如遇到端口配置问题，请：

1. 运行 `python config_manager.py validate` 检查配置
2. 运行 `python port_manager.py --info <端口>` 检查端口状态
3. 查看启动日志中的错误信息
4. 联系技术支持团队
