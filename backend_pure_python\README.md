# 暨阳湖大酒店传菜管理系统

## 系统简介

暨阳湖大酒店传菜管理系统是一套专为酒店餐饮部门设计的数字化管理解决方案，实现了从订单创建到菜品上桌的全流程数字化管理。系统采用Web技术架构，支持多用户角色协同工作，提高传菜效率、优化厨房协调、增强服务质量。

## 主要特性

- 🏨 **完整的餐饮管理流程** - 订单创建、厨房制作、传菜上桌全流程管理
- 👥 **多角色权限控制** - 支持管理员、经理、厨师长、服务员等6种角色
- 📱 **响应式设计** - 支持PC端和移动端访问
- 🔄 **实时数据同步** - 基于WebSocket的实时状态更新
- 🎵 **语音播报功能** - 厨房菜品完成语音提醒
- 🌐 **完全本地化** - 无需外部网络依赖，所有资源本地化
- 🛡️ **安全可靠** - 完整的权限控制和数据保护机制

## 系统架构

- **后端框架**: FastAPI (Python)
- **数据库**: SQLite
- **前端技术**: HTML5 + CSS3 + JavaScript + Bootstrap 5
- **实时通信**: WebSocket
- **身份认证**: Session + Cookie

## 快速开始

### 系统要求

- **操作系统**: Windows Server 2008 R2 或更高版本
- **Python版本**: 3.8.x 或 3.9.x（推荐 3.8.10）
- **内存**: 4GB 以上（推荐 8GB）
- **硬盘**: 20GB 以上可用空间

### 安装步骤

1. **下载系统文件**
   ```
   将系统文件解压到目标目录，如 C:\HotelSystem\
   ```

2. **安装Python环境**
   - 下载并安装 Python 3.8.10
   - 确保选择"Add Python to PATH"选项

3. **安装依赖包**
   ```cmd
   cd C:\HotelSystem
   pip install -r requirements.txt
   ```

4. **启动系统**
   ```cmd
   双击运行 start_system.bat
   ```
   或者
   ```cmd
   python main.py
   ```

### 首次使用

1. **访问系统**
   - 打开浏览器访问：http://localhost:8001

2. **默认账户登录**
   - 系统管理员：admin / admin123
   - 餐饮经理：manager01 / manager123
   - 厨师长：chef01 / chef123

3. **系统配置**
   - 登录后可在系统设置中调整各项配置
   - 建议首次使用时修改默认密码

## 用户角色说明

### 系统管理员 (admin)
- 系统最高权限
- 用户管理、系统配置、数据维护
- 强制结束用餐、系统重置等特殊操作

### 餐饮经理 (manager)
- 餐饮部门管理权限
- 用户管理（除系统管理员外）
- 服务员授权、订单管理、数据统计

### 厨师长 (chef_manager)
- 厨房管理权限
- 厨房显示、菜品管理、订单编辑
- 语音播报控制、厨房工作协调

### 厨房打荷 (kitchen_helper)
- 厨房辅助权限
- 厨房显示、菜品操作、传菜协调
- 服务员指令处理

### 服务员 (waiter)
- 包厢服务权限
- 菜单查看、菜品确认、服务指令
- 用餐开始和结束控制

### 商务中心 (business_center)
- 订单创建权限
- 为客人创建订单、菜单录入

## 主要功能

### 订单管理
- 订单创建和编辑
- 菜品状态跟踪
- 用餐流程控制
- 订单历史查询

### 厨房管理
- 厨房大屏显示
- 菜品制作管理
- 语音播报系统
- 实时状态同步

### 服务员操作
- 移动端菜单界面
- 菜品确认上桌
- 服务指令发送
- 多包厢同时服务

### 包厢管理
- 包厢状态管理
- 服务员分配
- 用餐时间控制
- 资源调度优化

### 权限管理
- 基于角色的访问控制
- 用户账户管理
- 权限分配和撤销
- 操作日志记录

## 文档说明

- `docs/系统功能说明.md` - 详细的系统功能说明
- `docs/API接口文档.md` - 完整的API接口文档
- `docs/数据库结构说明.md` - 数据库结构详细说明
- `docs/Windows Server 2008部署指南.md` - 部署指南
- `docs/系统清理完成报告.md` - 系统优化报告

## 常见问题

### Q: 系统启动失败怎么办？
A: 请检查：
1. Python版本是否正确（推荐3.8.10）
2. 依赖包是否安装完整
3. 端口8001是否被占用
4. 防火墙是否阻止访问

### Q: 忘记密码怎么办？
A: 可以通过以下方式重置：
1. 使用系统管理员账户重置其他用户密码
2. 运行数据库初始化脚本重置所有账户

### Q: 如何备份数据？
A: 定期备份以下文件：
1. `paocai.db` - 主数据库文件
2. `static/uploads/` - 上传文件目录（如有）

### Q: 系统支持多少并发用户？
A: 建议同时在线用户不超过20人，具体取决于服务器配置。

## 技术支持

### 联系方式
- 技术支持邮箱：<EMAIL>
- 在线文档：http://docs.hotel-system.com

### 故障排除
1. 查看系统日志文件
2. 检查网络连接状态
3. 验证用户权限配置
4. 重启系统服务

### 系统维护
- 建议每日备份数据库
- 定期清理过期数据
- 监控系统性能状态
- 及时更新系统版本

## 版本信息

- **当前版本**: v1.0 Production Ready
- **发布日期**: 2025年6月
- **兼容性**: Windows Server 2008 R2+
- **Python版本**: 3.8+ / 3.9+

## 许可证

本系统为暨阳湖大酒店专用系统，版权所有。

---

**开发团队**: Augment Agent  
**最后更新**: 2025年6月25日  
**文档版本**: v1.0
