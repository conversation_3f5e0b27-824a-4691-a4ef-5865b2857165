#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
暨阳湖大酒店传菜管理系统 - 配置管理模块
"""

import os
from typing import Optional

class ServerConfig:
    """服务器配置类"""
    
    # 默认配置
    DEFAULT_PORT = 5109
    DEFAULT_HOST = "0.0.0.0"
    DEFAULT_DEBUG = False
    
    @classmethod
    def get_port(cls) -> int:
        """获取服务端口号
        
        优先级：环境变量 > 配置文件 > 默认值
        """
        # 从环境变量获取
        port_str = os.getenv('HOTEL_SYSTEM_PORT')
        if port_str:
            try:
                port = int(port_str)
                if cls.validate_port(port):
                    return port
            except ValueError:
                pass
        
        # 从配置文件获取
        config_port = cls._load_config_file().get('port')
        if config_port:
            try:
                port = int(config_port)
                if cls.validate_port(port):
                    return port
            except ValueError:
                pass
        
        # 返回默认值
        return cls.DEFAULT_PORT
    
    @classmethod
    def get_host(cls) -> str:
        """获取服务主机地址"""
        # 从环境变量获取
        host = os.getenv('HOTEL_SYSTEM_HOST')
        if host:
            return host
        
        # 从配置文件获取
        config_host = cls._load_config_file().get('host')
        if config_host:
            return config_host
        
        # 返回默认值
        return cls.DEFAULT_HOST
    
    @classmethod
    def get_debug(cls) -> bool:
        """获取调试模式设置"""
        # 从环境变量获取
        debug_str = os.getenv('HOTEL_SYSTEM_DEBUG', '').lower()
        if debug_str in ('true', '1', 'yes', 'on'):
            return True
        elif debug_str in ('false', '0', 'no', 'off'):
            return False
        
        # 从配置文件获取
        config_debug = cls._load_config_file().get('debug')
        if config_debug is not None:
            return bool(config_debug)
        
        # 返回默认值
        return cls.DEFAULT_DEBUG
    
    @classmethod
    def validate_port(cls, port: int) -> bool:
        """验证端口号是否有效"""
        return 1024 <= port <= 65535
    
    @classmethod
    def _load_config_file(cls) -> dict:
        """从配置文件加载配置"""
        config = {}
        config_file = 'system_config.ini'
        
        if os.path.exists(config_file):
            try:
                import configparser
                parser = configparser.ConfigParser()
                parser.read(config_file, encoding='utf-8')
                
                if 'server' in parser:
                    server_section = parser['server']
                    if 'port' in server_section:
                        config['port'] = server_section.getint('port')
                    if 'host' in server_section:
                        config['host'] = server_section.get('host')
                    if 'debug' in server_section:
                        config['debug'] = server_section.getboolean('debug')
                        
            except Exception as e:
                print(f"⚠️ 读取配置文件失败: {e}")
        
        return config
    
    @classmethod
    def save_config_file(cls, port: Optional[int] = None, host: Optional[str] = None, debug: Optional[bool] = None):
        """保存配置到文件"""
        config_file = 'system_config.ini'
        
        try:
            import configparser
            parser = configparser.ConfigParser()
            
            # 如果文件存在，先读取现有配置
            if os.path.exists(config_file):
                parser.read(config_file, encoding='utf-8')
            
            # 确保有server节
            if 'server' not in parser:
                parser.add_section('server')
            
            # 更新配置
            if port is not None:
                parser.set('server', 'port', str(port))
            if host is not None:
                parser.set('server', 'host', host)
            if debug is not None:
                parser.set('server', 'debug', str(debug))
            
            # 写入文件
            with open(config_file, 'w', encoding='utf-8') as f:
                parser.write(f)
                
            print(f"✅ 配置已保存到 {config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存配置文件失败: {e}")
            return False
    
    @classmethod
    def create_default_config(cls):
        """创建默认配置文件"""
        config_content = f"""[server]
# 服务器端口号 (1024-65535)
port = {cls.DEFAULT_PORT}

# 服务器主机地址 (0.0.0.0 表示监听所有网络接口)
host = {cls.DEFAULT_HOST}

# 调试模式 (true/false)
debug = {str(cls.DEFAULT_DEBUG).lower()}

[database]
# 数据库文件路径
db_path = paocai.db

# 数据库连接池大小
pool_size = 10

[security]
# JWT密钥过期时间（天）
token_expire_days = 30

# 会话超时时间（秒）
session_timeout = 3600

[system]
# 系统名称
system_name = 暨阳湖大酒店传菜管理系统

# 酒店名称
hotel_name = 暨阳湖大酒店

# 时区
timezone = Asia/Shanghai
"""
        
        config_file = 'system_config.ini'
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            print(f"✅ 默认配置文件已创建: {config_file}")
            return True
        except Exception as e:
            print(f"❌ 创建配置文件失败: {e}")
            return False

def load_env_file():
    """加载.env文件中的环境变量"""
    env_file = '.env'
    if os.path.exists(env_file):
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        key = key.strip()
                        value = value.strip().strip('"').strip("'")
                        os.environ[key] = value
            print(f"✅ 已加载环境变量文件: {env_file}")
        except Exception as e:
            print(f"⚠️ 加载环境变量文件失败: {e}")

def create_default_env():
    """创建默认.env文件"""
    env_content = f"""# 暨阳湖大酒店传菜管理系统环境变量配置

# 服务器配置
HOTEL_SYSTEM_PORT={ServerConfig.DEFAULT_PORT}
HOTEL_SYSTEM_HOST={ServerConfig.DEFAULT_HOST}
HOTEL_SYSTEM_DEBUG={str(ServerConfig.DEFAULT_DEBUG).lower()}

# 数据库配置
DATABASE_URL=sqlite:///./paocai.db

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_EXPIRE_DAYS=30

# 系统配置
SYSTEM_NAME=暨阳湖大酒店传菜管理系统
HOTEL_NAME=暨阳湖大酒店
TIMEZONE=Asia/Shanghai
"""
    
    env_file = '.env'
    try:
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print(f"✅ 默认环境变量文件已创建: {env_file}")
        return True
    except Exception as e:
        print(f"❌ 创建环境变量文件失败: {e}")
        return False

# 注释掉自动加载，避免与现有配置系统冲突
# load_env_file()
