#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
暨阳湖大酒店传菜管理系统 - 配置管理工具
"""

import os
import sys
import argparse
from config import ServerConfig, create_default_env

def show_current_config():
    """显示当前配置"""
    print("📋 当前系统配置:")
    print(f"   端口号: {ServerConfig.get_port()}")
    print(f"   主机地址: {ServerConfig.get_host()}")
    print(f"   调试模式: {ServerConfig.get_debug()}")
    print()
    
    # 显示配置来源
    print("🔍 配置来源:")
    
    # 检查环境变量
    env_port = os.getenv('HOTEL_SYSTEM_PORT')
    env_host = os.getenv('HOTEL_SYSTEM_HOST')
    env_debug = os.getenv('HOTEL_SYSTEM_DEBUG')
    
    if env_port or env_host or env_debug:
        print("   环境变量:")
        if env_port:
            print(f"      HOTEL_SYSTEM_PORT = {env_port}")
        if env_host:
            print(f"      HOTEL_SYSTEM_HOST = {env_host}")
        if env_debug:
            print(f"      HOTEL_SYSTEM_DEBUG = {env_debug}")
    
    # 检查配置文件
    config_data = ServerConfig._load_config_file()
    if config_data:
        print("   配置文件 (system_config.ini):")
        for key, value in config_data.items():
            print(f"      {key} = {value}")
    
    # 检查.env文件
    if os.path.exists('.env'):
        print("   环境变量文件 (.env): 存在")
    else:
        print("   环境变量文件 (.env): 不存在")

def set_config(port=None, host=None, debug=None):
    """设置配置"""
    if port is not None:
        if not ServerConfig.validate_port(port):
            print(f"❌ 错误：端口号 {port} 无效")
            return False

    success = ServerConfig.save_config_file(port=port, host=host, debug=debug)
    if success:
        print("✅ 配置保存成功")
        show_current_config()
    return success

def create_configs():
    """创建默认配置文件"""
    print("📝 创建默认配置文件...")
    
    # 创建配置文件
    if ServerConfig.create_default_config():
        print("✅ system_config.ini 创建成功")
    else:
        print("❌ system_config.ini 创建失败")
    
    # 创建环境变量文件
    if create_default_env():
        print("✅ .env 创建成功")
    else:
        print("❌ .env 创建失败")

def validate_config():
    """验证配置"""
    print("🔍 验证系统配置...")
    
    issues = []
    
    # 验证端口
    port = ServerConfig.get_port()
    if not ServerConfig.validate_port(port):
        issues.append(f"端口号 {port} 无效（应在 1024-65535 范围内）")

    # 验证主机地址
    host = ServerConfig.get_host()
    if not host:
        issues.append("主机地址不能为空")
    
    # 检查端口是否被占用
    try:
        from port_manager import PortManager
        if PortManager.is_port_in_use(port):
            processes = PortManager.find_process_using_port(port)
            if processes:
                process_info = ", ".join([f"PID:{pid}({name})" for pid, name in processes])
                issues.append(f"端口 {port} 已被占用 ({process_info})")
            else:
                issues.append(f"端口 {port} 已被占用")
    except ImportError:
        pass
    
    if issues:
        print("❌ 发现配置问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 配置验证通过")
        return True

def reset_config():
    """重置配置"""
    print("🔄 重置配置...")
    
    # 删除配置文件
    config_files = ['system_config.ini', '.env']
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                os.remove(config_file)
                print(f"✅ 已删除 {config_file}")
            except Exception as e:
                print(f"❌ 删除 {config_file} 失败: {e}")
    
    # 清除环境变量
    env_vars = ['HOTEL_SYSTEM_PORT', 'HOTEL_SYSTEM_HOST', 'HOTEL_SYSTEM_DEBUG']
    for var in env_vars:
        if var in os.environ:
            del os.environ[var]
            print(f"✅ 已清除环境变量 {var}")
    
    print("🔄 重置完成，将使用默认配置")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='暨阳湖大酒店传菜管理系统 - 配置管理工具')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 显示配置
    subparsers.add_parser('show', help='显示当前配置')
    
    # 设置配置
    set_parser = subparsers.add_parser('set', help='设置配置')
    set_parser.add_argument('--port', '-p', type=int, help='设置端口号')
    set_parser.add_argument('--host', type=str, help='设置主机地址')
    set_parser.add_argument('--debug', type=bool, help='设置调试模式')
    
    # 创建配置文件
    subparsers.add_parser('create', help='创建默认配置文件')
    
    # 验证配置
    subparsers.add_parser('validate', help='验证配置')
    
    # 重置配置
    subparsers.add_parser('reset', help='重置配置')
    
    args = parser.parse_args()
    
    if args.command == 'show':
        show_current_config()
    
    elif args.command == 'set':
        if not any([args.port, args.host, args.debug is not None]):
            print("❌ 请指定要设置的配置项")
            return 1
        
        if not set_config(port=args.port, host=args.host, debug=args.debug):
            return 1
    
    elif args.command == 'create':
        create_configs()
    
    elif args.command == 'validate':
        if not validate_config():
            return 1
    
    elif args.command == 'reset':
        confirm = input("⚠️ 确定要重置所有配置吗？(y/N): ")
        if confirm.lower() in ('y', 'yes'):
            reset_config()
        else:
            print("👋 操作已取消")
    
    else:
        # 默认显示配置
        show_current_config()
        print()
        print("💡 使用帮助:")
        print("   python config_manager.py show          # 显示当前配置")
        print("   python config_manager.py set --port 8002  # 设置端口")
        print("   python config_manager.py create        # 创建配置文件")
        print("   python config_manager.py validate      # 验证配置")
        print("   python config_manager.py reset         # 重置配置")
    
    return 0

if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)
