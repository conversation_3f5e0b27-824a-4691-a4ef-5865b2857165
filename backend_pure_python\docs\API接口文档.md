# 暨阳湖大酒店传菜管理系统 - API接口文档

## 接口概述

本文档描述了暨阳湖大酒店传菜管理系统的所有API接口，包括认证、用户管理、订单管理、厨房操作等核心功能的接口规范。

### 基础信息
- **基础URL**: `http://localhost:8001`
- **认证方式**: Session + Cookie
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
    "success": true,
    "message": "操作成功",
    "data": {}
}
```

## 认证接口

### 1. 用户登录
**接口**: `POST /login`  
**描述**: 用户登录认证  
**权限**: 无需认证

**请求参数**:
```json
{
    "username": "admin",
    "password": "admin123"
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "登录成功",
    "redirect": "/dashboard"
}
```

### 2. 用户登出
**接口**: `POST /logout`  
**描述**: 用户登出  
**权限**: 需要登录

**响应示例**:
```json
{
    "success": true,
    "message": "登出成功"
}
```

## 用户管理接口

### 1. 获取用户列表
**接口**: `GET /api/users`  
**描述**: 获取用户列表  
**权限**: `user.manage`

**查询参数**:
- `role`: 用户角色过滤（可选）

**响应示例**:
```json
{
    "success": true,
    "users": [
        {
            "id": 1,
            "username": "admin",
            "full_name": "系统管理员",
            "role": "admin",
            "status": "active",
            "is_active": true,
            "created_at": "2025-06-25T10:00:00"
        }
    ]
}
```

### 2. 获取单个用户信息
**接口**: `GET /api/users/{user_id}`  
**描述**: 获取指定用户详细信息  
**权限**: `user.manage`

**响应示例**:
```json
{
    "success": true,
    "user": {
        "id": 1,
        "username": "admin",
        "full_name": "系统管理员",
        "role": "admin",
        "status": "active",
        "employee_id": "ADMIN001",
        "department": "信息技术部"
    }
}
```

### 3. 创建用户
**接口**: `POST /users/create`  
**描述**: 创建新用户  
**权限**: `user.manage`

**请求参数**:
```json
{
    "username": "waiter01",
    "password": "waiter123",
    "full_name": "服务员01",
    "role": "waiter"
}
```

### 4. 更新用户信息
**接口**: `POST /users/{user_id}/update`  
**描述**: 更新用户信息  
**权限**: `user.manage`

**请求参数**:
```json
{
    "username": "waiter01",
    "full_name": "服务员01",
    "role": "waiter",
    "status": "ACTIVE",
    "password": "new_password"
}
```

### 5. 删除用户
**接口**: `POST /users/{user_id}/delete`  
**描述**: 删除用户  
**权限**: `user.manage`

**响应示例**:
```json
{
    "success": true,
    "message": "用户删除成功"
}
```

## 订单管理接口

### 1. 获取订单列表
**接口**: `GET /api/orders`  
**描述**: 获取订单列表  
**权限**: `order.view`

**查询参数**:
- `status`: 订单状态过滤
- `table_id`: 包厢ID过滤
- `page`: 页码
- `size`: 每页数量

**响应示例**:
```json
{
    "success": true,
    "orders": [
        {
            "id": 1,
            "order_number": "ORD20250625001",
            "table_number": "1",
            "customer_name": "张先生",
            "guest_count": 8,
            "status": "serving",
            "total_amount": 680.0,
            "created_at": "2025-06-25T10:00:00"
        }
    ],
    "total": 1,
    "page": 1,
    "size": 10
}
```

### 2. 获取订单详情
**接口**: `GET /api/orders/{order_id}`  
**描述**: 获取订单详细信息  
**权限**: `order.view`

**响应示例**:
```json
{
    "success": true,
    "order": {
        "id": 1,
        "order_number": "ORD20250625001",
        "table_number": "1",
        "customer_name": "张先生",
        "guest_count": 8,
        "status": "serving",
        "menu_content": "手工面筋竹林鸡\n野茭白烧河虾",
        "items": [
            {
                "id": 1,
                "dish_name": "手工面筋竹林鸡",
                "status": "ready",
                "created_at": "2025-06-25T10:00:00"
            }
        ]
    }
}
```

### 3. 创建订单
**接口**: `POST /orders/create`  
**描述**: 创建新订单  
**权限**: `order.create`

**请求参数**:
```json
{
    "table_id": 1,
    "customer_name": "张先生",
    "guest_count": 8,
    "menu_content": "手工面筋竹林鸡\n野茭白烧河虾",
    "special_requests": "不要辣"
}
```

### 4. 更新订单
**接口**: `POST /orders/{order_id}/update`  
**描述**: 更新订单信息  
**权限**: `order.manage`

### 5. 开始用餐
**接口**: `POST /orders/{order_id}/start-dining`  
**描述**: 开始用餐  
**权限**: `waiter.serve`

### 6. 结束用餐
**接口**: `POST /orders/{order_id}/finish`  
**描述**: 结束用餐  
**权限**: `waiter.serve`

## 厨房管理接口

### 1. 获取厨房显示数据
**接口**: `GET /api/kitchen-display`  
**描述**: 获取厨房大屏显示数据  
**权限**: `kitchen.view`

**响应示例**:
```json
{
    "success": true,
    "rooms": [
        {
            "table_number": "1",
            "customer_name": "张先生",
            "guest_count": 8,
            "dining_start_time": "2025-06-25T10:00:00",
            "dishes": [
                {
                    "id": 1,
                    "name": "手工面筋竹林鸡",
                    "status": "cooking",
                    "created_at": "2025-06-25T10:00:00"
                }
            ]
        }
    ]
}
```

### 2. 标记菜品完成
**接口**: `POST /api/dishes/{dish_id}/mark-done`  
**描述**: 标记菜品制作完成  
**权限**: `dish.mark_done`

**响应示例**:
```json
{
    "success": true,
    "message": "菜品已标记完成"
}
```

### 3. 获取服务员指令
**接口**: `GET /api/waiter-actions/latest`  
**描述**: 获取最新服务员指令  
**权限**: `kitchen.view`

**响应示例**:
```json
{
    "success": true,
    "actions": [
        {
            "id": 1,
            "room_number": "1",
            "action_type": "rush_order",
            "action_content": "催菜：手工面筋竹林鸡",
            "waiter_name": "服务员01",
            "created_at": "2025-06-25T10:00:00"
        }
    ]
}
```

## 服务员操作接口

### 1. 获取服务员菜单
**接口**: `GET /waiter/menu/{room_number}`  
**描述**: 获取指定包厢的菜单信息  
**权限**: `waiter.view_menu`

**响应示例**:
```json
{
    "success": true,
    "room_info": {
        "table_number": "1",
        "customer_name": "张先生",
        "guest_count": 8,
        "status": "serving"
    },
    "dishes": [
        {
            "id": 1,
            "name": "手工面筋竹林鸡",
            "status": "ready",
            "can_confirm": true
        }
    ]
}
```

### 2. 确认菜品上桌
**接口**: `POST /waiter/confirm-dish/{dish_id}`  
**描述**: 确认菜品已上桌  
**权限**: `waiter.confirm_dish`

### 3. 发送服务指令
**接口**: `POST /waiter/send-action`  
**描述**: 发送服务指令到厨房  
**权限**: `waiter.rush`

**请求参数**:
```json
{
    "room_number": "1",
    "action_type": "rush_order",
    "action_content": "催菜：手工面筋竹林鸡"
}
```

## 包厢管理接口

### 1. 获取包厢列表
**接口**: `GET /api/tables`  
**描述**: 获取包厢列表  
**权限**: `table.view`

**响应示例**:
```json
{
    "success": true,
    "tables": [
        {
            "id": 1,
            "number": "1",
            "name": "1号包厢",
            "status": "occupied",
            "capacity": 8,
            "current_guests": 8,
            "assigned_waiter": "服务员01"
        }
    ]
}
```

### 2. 更新包厢状态
**接口**: `POST /api/tables/{table_id}/status`  
**描述**: 更新包厢状态  
**权限**: `table.manage`

## 系统配置接口

### 1. 获取系统配置
**接口**: `GET /api/system-config`  
**描述**: 获取系统配置信息  
**权限**: `system.config`

### 2. 更新系统配置
**接口**: `POST /api/system-config`  
**描述**: 更新系统配置  
**权限**: `system.config`

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 接口调用示例

### JavaScript调用示例
```javascript
// 获取订单列表
fetch('/api/orders', {
    method: 'GET',
    credentials: 'include',
    headers: {
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('订单列表:', data.orders);
    }
});

// 创建订单
fetch('/orders/create', {
    method: 'POST',
    credentials: 'include',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        table_id: 1,
        customer_name: '张先生',
        guest_count: 8,
        menu_content: '手工面筋竹林鸡\n野茭白烧河虾'
    })
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('订单创建成功');
    }
});
```

---

*本文档版本: v1.0*  
*最后更新: 2025年6月*  
*适用系统: 暨阳湖大酒店传菜管理系统*
