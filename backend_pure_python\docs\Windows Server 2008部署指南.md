# 暨阳湖大酒店传菜管理系统 - Windows Server 2008部署指南

## 部署概述

本指南详细说明如何在Windows Server 2008环境中部署暨阳湖大酒店传菜管理系统。由于Windows Server 2008的特殊性和Python版本兼容性问题，本指南提供了多种部署方案。

## 系统环境要求

### 硬件要求
- **CPU**: 双核2.0GHz以上
- **内存**: 4GB以上（推荐8GB）
- **硬盘**: 20GB以上可用空间
- **网络**: 支持局域网连接

### 软件要求
- **操作系统**: Windows Server 2008 R2 SP1或更高版本
- **Python版本**: 3.8.x 或 3.9.x（推荐3.8.10）
- **浏览器**: IE 11、Chrome、Firefox等现代浏览器

## Python版本兼容性问题

### 问题说明
Windows Server 2008对Python版本支持有限：
- Python 3.10+：不支持（需要Windows 10/Server 2016+）
- Python 3.9：部分支持（需要特定版本）
- Python 3.8：完全支持（推荐）

### 推荐解决方案

#### 方案一：使用Python 3.8（推荐）
1. **下载Python 3.8.10**
   - 访问：https://www.python.org/downloads/release/python-3810/
   - 下载：Windows x86-64 executable installer

2. **安装注意事项**
   - 选择"Add Python to PATH"
   - 选择"Install for all users"
   - 自定义安装路径：`C:\Python38`

#### 方案二：虚拟化Linux环境
如果Python兼容性问题无法解决，可以考虑在Windows Server 2008上运行Linux虚拟机：

1. **VMware vSphere/ESXi**
   - 创建CentOS 7虚拟机
   - 分配2GB内存，20GB硬盘
   - 安装Python 3.8+环境

2. **VirtualBox**
   - 下载VirtualBox 6.0（支持Windows Server 2008）
   - 创建Ubuntu 18.04 LTS虚拟机
   - 配置网络桥接模式

## 部署步骤

### 第一步：准备部署环境

#### 1. 创建部署目录
```cmd
mkdir C:\HotelSystem
cd C:\HotelSystem
```

#### 2. 下载系统文件
将系统文件复制到 `C:\HotelSystem\` 目录

#### 3. 检查文件结构
```
C:\HotelSystem\
├── main.py                 # 主程序文件
├── requirements.txt        # 依赖包列表
├── init_production_db.py   # 数据库初始化脚本
├── core/                   # 核心模块
├── models/                 # 数据模型
├── templates/              # 页面模板
├── static/                 # 静态资源
└── docs/                   # 文档目录
```

### 第二步：安装Python环境

#### 1. 安装Python 3.8.10
```cmd
# 下载并运行安装程序
python-3.8.10-amd64.exe

# 验证安装
python --version
pip --version
```

#### 2. 升级pip（如果需要）
```cmd
python -m pip install --upgrade pip
```

### 第三步：安装依赖包

#### 1. 安装系统依赖
```cmd
cd C:\HotelSystem
pip install -r requirements.txt
```

#### 2. 处理可能的依赖问题
如果遇到依赖安装失败，可以尝试：
```cmd
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者逐个安装核心依赖
pip install fastapi==0.104.1
pip install uvicorn==0.24.0
pip install sqlalchemy==2.0.23
pip install jinja2==3.1.2
pip install python-multipart==0.0.6
pip install passlib==1.7.4
pip install bcrypt==4.0.1
```

### 第四步：初始化数据库

#### 1. 运行数据库初始化脚本
```cmd
cd C:\HotelSystem
python init_production_db.py
```

#### 2. 验证数据库创建
检查是否生成了 `paocai.db` 文件

### 第五步：配置系统服务

#### 1. 创建启动脚本
创建 `start_system.bat` 文件：
```batch
@echo off
cd /d C:\HotelSystem
echo 启动暨阳湖大酒店传菜管理系统...
python main.py
pause
```

#### 2. 创建Windows服务（可选）
创建 `install_service.py` 文件：
```python
import win32serviceutil
import win32service
import win32event
import subprocess
import os

class HotelSystemService(win32serviceutil.ServiceFramework):
    _svc_name_ = "HotelSystem"
    _svc_display_name_ = "暨阳湖大酒店传菜管理系统"
    _svc_description_ = "酒店传菜管理系统服务"

    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)

    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)

    def SvcDoRun(self):
        os.chdir(r'C:\HotelSystem')
        subprocess.call(['python', 'main.py'])

if __name__ == '__main__':
    win32serviceutil.HandleCommandLine(HotelSystemService)
```

### 第六步：网络和防火墙配置

#### 1. 配置Windows防火墙
```cmd
# 打开防火墙端口8001
netsh advfirewall firewall add rule name="Hotel System" dir=in action=allow protocol=TCP localport=8001
```

#### 2. 配置IIS反向代理（可选）
如果需要通过80端口访问，可以配置IIS反向代理：

1. 安装IIS和URL Rewrite模块
2. 创建网站配置
3. 配置反向代理规则

### 第七步：启动系统

#### 1. 手动启动
```cmd
cd C:\HotelSystem
python main.py
```

#### 2. 验证启动
- 打开浏览器访问：http://localhost:8001
- 使用默认账户登录：admin / admin123

### 第八步：系统测试

#### 1. 功能测试清单
- [ ] 用户登录功能
- [ ] 订单创建功能
- [ ] 厨房显示功能
- [ ] 服务员操作功能
- [ ] 权限控制功能

#### 2. 性能测试
- 并发用户测试（建议10-20用户）
- 响应时间测试
- 内存使用监控

## 故障排除

### 常见问题及解决方案

#### 1. Python版本不兼容
**问题**: 提示Python版本不支持
**解决**: 
- 卸载当前Python版本
- 安装Python 3.8.10
- 重新安装依赖包

#### 2. 依赖包安装失败
**问题**: pip install失败
**解决**:
```cmd
# 清理pip缓存
pip cache purge

# 使用离线安装包
pip install --find-links ./wheels -r requirements.txt

# 降级依赖版本
pip install fastapi==0.95.0
```

#### 3. 数据库权限问题
**问题**: 无法创建或访问数据库
**解决**:
- 检查目录权限
- 以管理员身份运行
- 修改数据库文件路径

#### 4. 端口占用问题
**问题**: 端口8001被占用
**解决**:
```cmd
# 查看端口占用
netstat -ano | findstr :8001

# 结束占用进程
taskkill /PID <进程ID> /F

# 或修改配置使用其他端口
```

#### 5. 防火墙阻止访问
**问题**: 局域网无法访问
**解决**:
- 检查Windows防火墙设置
- 添加端口例外规则
- 检查网络连接

## 性能优化建议

### 1. 系统优化
- 关闭不必要的Windows服务
- 调整虚拟内存设置
- 定期清理临时文件

### 2. 数据库优化
- 定期备份数据库
- 清理过期日志数据
- 优化数据库索引

### 3. 网络优化
- 使用有线网络连接
- 配置静态IP地址
- 优化网络MTU设置

## 维护指南

### 1. 日常维护
- 每日检查系统运行状态
- 每周备份数据库文件
- 每月清理日志文件

### 2. 更新升级
- 定期检查系统更新
- 测试环境验证更新
- 生产环境谨慎升级

### 3. 监控告警
- 配置系统监控
- 设置告警阈值
- 建立应急响应流程

## 备份恢复

### 1. 数据备份
```cmd
# 创建备份目录
mkdir C:\HotelSystem\backup

# 备份数据库
copy paocai.db backup\paocai_%date%.db

# 备份配置文件
xcopy /s static backup\static\
```

### 2. 系统恢复
```cmd
# 停止系统服务
# 恢复数据库文件
copy backup\paocai_20250625.db paocai.db

# 重启系统服务
```

## 技术支持

### 联系方式
- 技术支持邮箱：<EMAIL>
- 技术支持电话：400-xxx-xxxx
- 在线文档：http://docs.hotel-system.com

### 远程支持
- 支持TeamViewer远程协助
- 支持QQ远程协助
- 支持电话技术指导

---

*本文档版本: v1.0*  
*最后更新: 2025年6月*  
*适用系统: Windows Server 2008 R2*
