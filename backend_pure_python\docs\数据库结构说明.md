# 暨阳湖大酒店传菜管理系统 - 数据库结构说明

## 数据库概述

本系统使用SQLite数据库，文件名为`paocai.db`。数据库设计遵循第三范式，确保数据一致性和完整性。

## 数据库表结构

### 1. 用户表 (users)

**表名**: `users`  
**描述**: 存储系统用户信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 用户ID（主键） |
| username | VARCHAR | 50 | NOT NULL | - | 用户名（唯一） |
| hashed_password | VARCHAR | 255 | NOT NULL | - | 加密密码 |
| full_name | VARCHAR | 100 | NOT NULL | - | 用户全名 |
| role | ENUM | - | NOT NULL | - | 用户角色 |
| status | ENUM | - | NOT NULL | ACTIVE | 用户状态 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否激活 |
| is_superuser | BOOLEAN | - | NOT NULL | FALSE | 是否超级用户 |
| employee_id | VARCHAR | 20 | NULL | - | 员工编号 |
| department | VARCHAR | 50 | NULL | - | 部门 |
| position | VARCHAR | 50 | NULL | - | 职位 |
| phone | VARCHAR | 20 | NULL | - | 电话号码 |
| email | VARCHAR | 100 | NULL | - | 邮箱地址 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |
| last_login | DATETIME | - | NULL | - | 最后登录时间 |

**角色枚举值**:
- `admin`: 系统管理员
- `manager`: 餐饮经理
- `chef_manager`: 厨师长
- `waiter`: 服务员
- `kitchen_helper`: 厨房打荷
- `business_center`: 商务中心

**状态枚举值**:
- `ACTIVE`: 激活
- `INACTIVE`: 停用

### 2. 包厢表 (tables)

**表名**: `tables`  
**描述**: 存储酒店包厢信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 包厢ID（主键） |
| number | VARCHAR | 10 | NOT NULL | - | 包厢号码（唯一） |
| name | VARCHAR | 50 | NOT NULL | - | 包厢名称 |
| table_type | ENUM | - | NOT NULL | - | 包厢类型 |
| status | ENUM | - | NOT NULL | AVAILABLE | 包厢状态 |
| capacity | INTEGER | - | NOT NULL | - | 最大容量 |
| min_capacity | INTEGER | - | NOT NULL | 1 | 最小容量 |
| floor | VARCHAR | 20 | NULL | - | 楼层 |
| area | VARCHAR | 50 | NULL | - | 区域 |
| location_description | TEXT | - | NULL | - | 位置描述 |
| facilities_list | JSON | - | NULL | - | 设施列表 |
| minimum_charge | DECIMAL | 10,2 | NULL | - | 最低消费 |
| service_charge_rate | DECIMAL | 5,4 | NULL | - | 服务费率 |
| is_vip_only | BOOLEAN | - | NOT NULL | FALSE | 是否VIP专用 |
| requires_reservation | BOOLEAN | - | NOT NULL | FALSE | 是否需要预订 |
| current_guests | INTEGER | - | NOT NULL | 0 | 当前客人数 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

**包厢类型枚举值**:
- `PRIVATE_ROOM`: 包厢
- `HALL_TABLE`: 大厅桌位
- `VIP_ROOM`: VIP包厢

**包厢状态枚举值**:
- `AVAILABLE`: 空闲
- `RESERVED`: 已预订
- `OCCUPIED`: 使用中
- `CLEANING`: 清洁中
- `MAINTENANCE`: 维护中

### 3. 订单表 (orders)

**表名**: `orders`  
**描述**: 存储客人订单信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 订单ID（主键） |
| order_number | VARCHAR | 20 | NOT NULL | - | 订单号（唯一） |
| table_id | INTEGER | - | NOT NULL | - | 包厢ID（外键） |
| customer_name | VARCHAR | 100 | NOT NULL | - | 客人姓名 |
| guest_count | INTEGER | - | NOT NULL | - | 客人数量 |
| status | ENUM | - | NOT NULL | RESERVED | 订单状态 |
| menu_content | TEXT | - | NULL | - | 菜单内容 |
| special_requests | TEXT | - | NULL | - | 特殊要求 |
| total_amount | DECIMAL | 10,2 | NULL | 0.00 | 总金额 |
| service_charge | DECIMAL | 10,2 | NULL | 0.00 | 服务费 |
| discount_amount | DECIMAL | 10,2 | NULL | 0.00 | 折扣金额 |
| final_amount | DECIMAL | 10,2 | NULL | 0.00 | 最终金额 |
| dining_start_time | DATETIME | - | NULL | - | 用餐开始时间 |
| dining_end_time | DATETIME | - | NULL | - | 用餐结束时间 |
| created_by | INTEGER | - | NOT NULL | - | 创建人ID（外键） |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

**订单状态枚举值**:
- `RESERVED`: 已预订
- `SERVING`: 用餐中
- `COMPLETED`: 已完成
- `CANCELLED`: 已取消

### 4. 菜品表 (dishes)

**表名**: `dishes`  
**描述**: 存储订单中的菜品信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 菜品ID（主键） |
| order_id | INTEGER | - | NOT NULL | - | 订单ID（外键） |
| dish_name | VARCHAR | 100 | NOT NULL | - | 菜品名称 |
| status | ENUM | - | NOT NULL | PENDING | 菜品状态 |
| quantity | INTEGER | - | NOT NULL | 1 | 数量 |
| unit_price | DECIMAL | 10,2 | NULL | 0.00 | 单价 |
| total_price | DECIMAL | 10,2 | NULL | 0.00 | 总价 |
| cooking_notes | TEXT | - | NULL | - | 制作备注 |
| completed_at | DATETIME | - | NULL | - | 完成时间 |
| confirmed_at | DATETIME | - | NULL | - | 确认时间 |
| confirmed_by | INTEGER | - | NULL | - | 确认人ID（外键） |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

**菜品状态枚举值**:
- `PENDING`: 待制作
- `COOKING`: 制作中
- `READY`: 制作完成
- `SERVED`: 已上桌

### 5. 服务员授权表 (waiter_authorizations)

**表名**: `waiter_authorizations`  
**描述**: 存储服务员包厢授权信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 授权ID（主键） |
| waiter_id | INTEGER | - | NOT NULL | - | 服务员ID（外键） |
| table_id | INTEGER | - | NOT NULL | - | 包厢ID（外键） |
| authorized_by | INTEGER | - | NOT NULL | - | 授权人ID（外键） |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否有效 |
| authorized_at | DATETIME | - | NOT NULL | NOW() | 授权时间 |
| revoked_at | DATETIME | - | NULL | - | 撤销时间 |
| revoked_by | INTEGER | - | NULL | - | 撤销人ID（外键） |

### 6. 服务员指令表 (waiter_actions)

**表名**: `waiter_actions`  
**描述**: 存储服务员发送的指令信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 指令ID（主键） |
| waiter_id | INTEGER | - | NOT NULL | - | 服务员ID（外键） |
| room_number | VARCHAR | 10 | NOT NULL | - | 包厢号码 |
| action_type | VARCHAR | 50 | NOT NULL | - | 指令类型 |
| action_content | TEXT | - | NOT NULL | - | 指令内容 |
| is_processed | BOOLEAN | - | NOT NULL | FALSE | 是否已处理 |
| processed_at | DATETIME | - | NULL | - | 处理时间 |
| processed_by | INTEGER | - | NULL | - | 处理人ID（外键） |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |

**指令类型**:
- `rush_order`: 催菜
- `waiter_call`: 叫服务员
- `add_staple`: 加主食
- `dining_start`: 开始用餐
- `dining_end`: 结束用餐
- `force_end`: 强制结束

### 7. 系统配置表 (system_configs)

**表名**: `system_configs`  
**描述**: 存储系统配置信息

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 配置ID（主键） |
| key | VARCHAR | 100 | NOT NULL | - | 配置键（唯一） |
| value | TEXT | - | NULL | - | 配置值 |
| description | VARCHAR | 255 | NULL | - | 配置描述 |
| category | VARCHAR | 50 | NULL | - | 配置分类 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

### 8. 语音配置表 (voice_configs)

**表名**: `voice_configs`  
**描述**: 存储语音播报配置

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 配置ID（主键） |
| action_type | VARCHAR | 50 | NOT NULL | - | 动作类型（唯一） |
| voice_text | VARCHAR | 255 | NOT NULL | - | 播报文本 |
| repetitions | INTEGER | - | NOT NULL | 1 | 重复次数 |
| interval | INTEGER | - | NOT NULL | 0 | 间隔时间（秒） |
| is_enabled | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

### 9. 指令模板表 (command_templates)

**表名**: `command_templates`  
**描述**: 存储常用指令模板

| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 描述 |
|--------|------|------|----------|--------|------|
| id | INTEGER | - | NOT NULL | AUTO | 模板ID（主键） |
| name | VARCHAR | 100 | NOT NULL | - | 模板名称 |
| content | TEXT | - | NOT NULL | - | 模板内容 |
| category | VARCHAR | 50 | NULL | - | 模板分类 |
| usage_count | INTEGER | - | NOT NULL | 0 | 使用次数 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

## 数据库关系

### 主要外键关系

1. **orders.table_id** → **tables.id**
   - 订单关联包厢

2. **orders.created_by** → **users.id**
   - 订单创建人

3. **dishes.order_id** → **orders.id**
   - 菜品关联订单

4. **dishes.confirmed_by** → **users.id**
   - 菜品确认人

5. **waiter_authorizations.waiter_id** → **users.id**
   - 服务员授权关联用户

6. **waiter_authorizations.table_id** → **tables.id**
   - 服务员授权关联包厢

7. **waiter_actions.waiter_id** → **users.id**
   - 服务员指令关联用户

## 索引设计

### 主要索引

1. **用户表索引**
   - `idx_users_username`: username（唯一索引）
   - `idx_users_role`: role
   - `idx_users_status`: status

2. **包厢表索引**
   - `idx_tables_number`: number（唯一索引）
   - `idx_tables_status`: status
   - `idx_tables_type`: table_type

3. **订单表索引**
   - `idx_orders_number`: order_number（唯一索引）
   - `idx_orders_table_id`: table_id
   - `idx_orders_status`: status
   - `idx_orders_created_at`: created_at

4. **菜品表索引**
   - `idx_dishes_order_id`: order_id
   - `idx_dishes_status`: status

## 数据完整性约束

### 检查约束

1. **用户表**
   - username长度 >= 3
   - role在枚举值范围内
   - status在枚举值范围内

2. **包厢表**
   - capacity > 0
   - min_capacity > 0
   - min_capacity <= capacity

3. **订单表**
   - guest_count > 0
   - total_amount >= 0
   - final_amount >= 0

4. **菜品表**
   - quantity > 0
   - unit_price >= 0
   - total_price >= 0

### 触发器

1. **更新时间触发器**
   - 自动更新所有表的updated_at字段

2. **订单号生成触发器**
   - 自动生成唯一订单号

3. **金额计算触发器**
   - 自动计算订单总金额

## 数据备份策略

### 备份方案

1. **全量备份**
   - 每日凌晨2点执行
   - 保留最近30天的备份

2. **增量备份**
   - 每小时执行一次
   - 备份变更的数据

3. **关键数据实时备份**
   - 订单数据实时同步
   - 用户操作日志实时记录

### 恢复策略

1. **完整恢复**
   - 从最近的全量备份恢复
   - 应用增量备份

2. **点时间恢复**
   - 恢复到指定时间点
   - 基于事务日志恢复

---

*本文档版本: v1.0*  
*最后更新: 2025年6月*  
*适用系统: 暨阳湖大酒店传菜管理系统*
