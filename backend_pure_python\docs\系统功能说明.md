# 暨阳湖大酒店传菜管理系统 - 功能说明文档

## 系统概述

暨阳湖大酒店传菜管理系统是一套专为酒店餐饮部门设计的数字化管理解决方案，旨在提高传菜效率、优化厨房协调、增强服务质量。系统采用Web技术架构，支持多用户角色协同工作，实现了从订单创建到菜品上桌的全流程数字化管理。

## 系统架构

### 技术架构
- **后端框架**: FastAPI (Python)
- **数据库**: SQLite
- **前端技术**: HTML5 + CSS3 + JavaScript + Bootstrap 5
- **实时通信**: WebSocket
- **身份认证**: Session + Cookie
- **部署方式**: 单机部署，支持局域网访问

### 系统特点
- **完全本地化**: 无需外部网络依赖，所有资源本地化
- **轻量级部署**: 基于SQLite数据库，部署简单
- **响应式设计**: 支持PC端和移动端访问
- **实时更新**: 基于WebSocket的实时数据同步
- **权限控制**: 基于角色的访问控制系统

## 用户角色和权限

### 1. 系统管理员 (admin)
**权限范围**: 系统最高权限
- 用户管理：创建、编辑、删除所有用户
- 系统配置：修改系统参数和设置
- 数据管理：访问所有数据和功能
- 权限管理：分配和修改用户权限
- 系统维护：系统备份、恢复等操作

### 2. 餐饮经理 (manager)
**权限范围**: 餐饮部门管理权限
- 用户管理：管理除系统管理员外的所有用户
- 服务员授权：为服务员分配包厢权限
- 订单管理：查看和管理所有订单
- 包厢管理：管理包厢状态和分配
- 厨房监控：查看厨房操作状态
- 数据统计：查看营业数据和报表

### 3. 厨师长 (chef_manager)
**权限范围**: 厨房管理权限
- 厨房显示：查看厨房大屏显示
- 菜品管理：标记菜品完成状态
- 订单管理：查看和编辑订单（开始用餐后）
- 语音播报：控制厨房语音系统
- 厨房协调：管理厨房工作流程

### 4. 厨房打荷 (kitchen_helper)
**权限范围**: 厨房辅助权限
- 厨房显示：查看厨房大屏显示
- 菜品操作：标记菜品完成、配菜等
- 传菜协调：处理传菜指令
- 服务员指令：查看和处理服务员指令

### 5. 服务员 (waiter)
**权限范围**: 包厢服务权限
- 菜单查看：查看分配包厢的菜单
- 菜品确认：确认菜品上桌
- 服务指令：发送催菜、叫服务员等指令
- 用餐控制：开始和结束用餐

### 6. 商务中心 (business_center)
**权限范围**: 订单创建权限
- 订单创建：为客人创建新订单
- 订单查看：查看订单状态
- 菜单录入：录入客人点菜信息

## 核心功能模块

### 1. 用户管理模块
**功能描述**: 管理系统用户账户和权限
- 用户创建：支持创建不同角色的用户
- 权限分配：基于角色的权限控制
- 服务员授权：为服务员分配包厢服务权限
- 账户安全：密码管理、登录控制

### 2. 订单管理模块
**功能描述**: 管理客人订单的全生命周期
- 订单创建：支持菜单输入和菜品选择两种方式
- 订单编辑：用餐开始前可编辑订单内容
- 状态跟踪：实时跟踪订单状态变化
- 用餐控制：管理用餐开始和结束流程

### 3. 厨房管理模块
**功能描述**: 厨房操作的数字化管理
- 厨房大屏：实时显示所有包厢菜品状态
- 菜品管理：标记菜品制作完成状态
- 语音播报：自动播报菜品完成信息
- 工作协调：厨师长和打荷员协同工作

### 4. 包厢管理模块
**功能描述**: 管理酒店包厢资源
- 包厢状态：实时显示包厢使用状态
- 服务员分配：为包厢分配专属服务员
- 用餐管理：控制包厢用餐流程
- 资源调度：优化包厢使用效率

### 5. 服务员操作模块
**功能描述**: 服务员移动端操作界面
- 菜单显示：显示负责包厢的菜品清单
- 服务指令：发送各类服务指令到厨房
- 菜品确认：确认菜品已上桌
- 多包厢支持：同时服务多个包厢

### 6. 实时通信模块
**功能描述**: 系统各模块间的实时数据同步
- WebSocket通信：实现实时数据推送
- 状态同步：各终端状态实时同步
- 指令传递：服务指令实时传递
- 通知系统：重要事件实时通知

## 业务流程

### 1. 订单创建流程
1. 商务中心或餐饮经理创建新订单
2. 录入客人信息和菜品内容
3. 选择包厢并分配服务员
4. 订单进入待开始状态

### 2. 用餐服务流程
1. 服务员确认客人到达，点击"开始用餐"
2. 订单状态变更为"进行中"
3. 厨房大屏显示菜品制作任务
4. 厨师制作完成后标记菜品状态
5. 打荷员配菜并通知服务员
6. 服务员确认菜品上桌
7. 所有菜品完成后结束用餐

### 3. 厨房协调流程
1. 厨房大屏实时显示所有包厢菜品
2. 厨师长分配制作任务
3. 厨师完成菜品制作并标记
4. 打荷员进行配菜和传菜协调
5. 语音系统自动播报完成信息

### 4. 服务指令流程
1. 服务员在移动端发送服务指令
2. 指令实时传递到厨房显示屏
3. 厨房人员查看并处理指令
4. 处理完成后确认指令状态

## 系统配置

### 1. 基础配置
- 系统名称和酒店信息
- 时区设置（默认：Asia/Shanghai）
- 会话超时时间
- 自动刷新间隔

### 2. 语音配置
- 语音播报开关
- 播报内容模板
- 播报次数和间隔
- 音量控制

### 3. 界面配置
- 主题设置（浅色主题）
- 厨房显示配置
- 移动端适配
- 响应式布局

### 4. 权限配置
- 角色权限定义
- 功能访问控制
- 数据访问范围
- 操作权限限制

## 数据安全

### 1. 身份认证
- 基于Session的用户认证
- 密码加密存储（bcrypt）
- 登录失败次数限制
- 自动登出机制

### 2. 权限控制
- 基于角色的访问控制（RBAC）
- 功能级权限验证
- 数据级权限隔离
- API接口权限保护

### 3. 数据保护
- SQLite数据库文件保护
- 敏感信息加密
- 操作日志记录
- 数据备份机制

## 系统监控

### 1. 性能监控
- 系统响应时间
- 数据库查询性能
- 内存使用情况
- 并发用户数量

### 2. 业务监控
- 订单处理状态
- 厨房操作效率
- 服务响应时间
- 用户操作行为

### 3. 错误监控
- 系统错误日志
- 用户操作错误
- 网络连接状态
- 数据同步状态

## 扩展性设计

### 1. 模块化架构
- 功能模块独立
- 接口标准化
- 易于扩展和维护
- 支持功能定制

### 2. 数据库设计
- 规范化数据结构
- 支持数据迁移
- 预留扩展字段
- 支持多数据库

### 3. 接口设计
- RESTful API设计
- 标准化响应格式
- 版本控制支持
- 文档完整

## 维护指南

### 1. 日常维护
- 数据库备份
- 日志文件清理
- 系统性能检查
- 用户账户管理

### 2. 故障处理
- 常见问题诊断
- 错误日志分析
- 系统恢复流程
- 紧急处理预案

### 3. 升级维护
- 系统版本升级
- 数据库结构升级
- 功能模块更新
- 配置文件迁移

---

*本文档版本: v1.0*
*最后更新: 2025年6月*
*适用系统: 暨阳湖大酒店传菜管理系统*
