#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
暨阳湖大酒店传菜管理系统 - 生产环境数据库初始化脚本
用于清理测试数据并初始化生产环境所需的基础数据
"""

import os
import sys
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.database import Base, get_db
from core.security import get_password_hash
from models.user import User, UserRole, UserStatus
from models.table import Table, TableType, TableStatus
from models.system_config import SystemConfig
from models.voice_config import VoiceConfig
from models.command_template import CommandTemplate

def init_production_database():
    """初始化生产环境数据库"""
    print("🚀 开始初始化暨阳湖大酒店传菜管理系统生产环境数据库...")
    
    # 创建数据库引擎
    DATABASE_URL = "sqlite:///./paocai.db"
    engine = create_engine(DATABASE_URL, echo=False)
    
    # 删除所有表并重新创建
    print("📋 重新创建数据库表结构...")
    Base.metadata.drop_all(bind=engine)
    Base.metadata.create_all(bind=engine)
    
    # 创建会话
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 1. 创建默认用户账户
        print("👤 创建默认用户账户...")
        
        # 系统管理员
        admin_user = User(
            username="admin",
            hashed_password=get_password_hash("admin123"),
            full_name="系统管理员",
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            is_active=True,
            is_superuser=True,
            employee_id="ADMIN001",
            department="信息技术部",
            position="系统管理员"
        )
        db.add(admin_user)
        
        # 餐饮经理
        manager_user = User(
            username="manager01",
            hashed_password=get_password_hash("manager123"),
            full_name="餐饮经理",
            role=UserRole.MANAGER,
            status=UserStatus.ACTIVE,
            is_active=True,
            employee_id="MGR001",
            department="餐饮部",
            position="餐饮经理"
        )
        db.add(manager_user)
        
        # 厨师长
        chef_manager = User(
            username="chef01",
            hashed_password=get_password_hash("chef123"),
            full_name="厨师长",
            role=UserRole.CHEF_MANAGER,
            status=UserStatus.ACTIVE,
            is_active=True,
            employee_id="CHEF001",
            department="厨房部",
            position="厨师长"
        )
        db.add(chef_manager)
        
        # 打荷员
        kitchen_helper = User(
            username="helper01",
            hashed_password=get_password_hash("helper123"),
            full_name="打荷员",
            role=UserRole.KITCHEN_HELPER,
            status=UserStatus.ACTIVE,
            is_active=True,
            employee_id="HELP001",
            department="厨房部",
            position="打荷员"
        )
        db.add(kitchen_helper)
        
        # 商务中心
        business_user = User(
            username="business01",
            hashed_password=get_password_hash("business123"),
            full_name="商务中心",
            role=UserRole.BUSINESS_CENTER,
            status=UserStatus.ACTIVE,
            is_active=True,
            employee_id="BUS001",
            department="商务中心",
            position="接待员"
        )
        db.add(business_user)
        
        # 2. 创建默认包厢
        print("🏠 创建默认包厢...")
        
        # 创建10个包厢
        for i in range(1, 11):
            table = Table(
                number=str(i),
                name=f"{i}号包厢",
                table_type=TableType.PRIVATE_ROOM,
                status=TableStatus.AVAILABLE,
                capacity=8,
                min_capacity=2,
                floor="2楼",
                area="包厢区",
                location_description=f"2楼{i}号包厢",
                minimum_charge=200.0,
                service_charge_rate=0.1,
                is_vip_only=False,
                requires_reservation=True,
                current_guests=0,
                is_active=True,
                # 设置设施
                has_tv=True,
                has_air_conditioning=True,
                has_wifi=True,
                has_karaoke=False
            )
            db.add(table)
        
        # 3. 创建系统配置
        print("⚙️ 创建系统配置...")
        
        system_configs = [
            SystemConfig(config_key="system_name", config_value="暨阳湖大酒店传菜管理系统", description="系统名称"),
            SystemConfig(config_key="hotel_name", config_value="暨阳湖大酒店", description="酒店名称"),
            SystemConfig(config_key="timezone", config_value="Asia/Shanghai", description="系统时区"),
            SystemConfig(config_key="session_timeout", config_value="3600", description="会话超时时间（秒）"),
            SystemConfig(config_key="max_login_attempts", config_value="5", description="最大登录尝试次数"),
            SystemConfig(config_key="auto_logout_time", config_value="28800", description="自动退出时间（秒）"),
            SystemConfig(config_key="kitchen_display_refresh", config_value="3", description="厨房显示刷新间隔（秒）"),
            SystemConfig(config_key="voice_broadcast_enabled", config_value="true", description="是否启用语音播报"),
            SystemConfig(config_key="notification_sound_enabled", config_value="true", description="是否启用通知声音"),
            SystemConfig(config_key="theme", config_value="light", description="系统主题"),
        ]
        
        for config in system_configs:
            db.add(config)
        
        # 4. 创建语音配置
        print("🔊 创建语音配置...")
        
        voice_configs = [
            VoiceConfig(config_key="voice_enabled", config_value="true", description="是否启用语音播报"),
            VoiceConfig(config_key="voice_repeat_count", config_value="2", description="语音播报重复次数"),
            VoiceConfig(config_key="voice_repeat_interval", config_value="3", description="语音播报间隔时间（秒）"),
            VoiceConfig(config_key="voice_rate", config_value="0.8", description="语音播报语速"),
            VoiceConfig(config_key="voice_volume", config_value="1.0", description="语音播报音量"),
            VoiceConfig(config_key="voice_pitch", config_value="1.0", description="语音播报音调"),
        ]
        
        for voice_config in voice_configs:
            db.add(voice_config)
        
        # 5. 创建指令模板
        print("📝 创建指令模板...")
        
        command_templates = [
            CommandTemplate(name="叫服务员", code="waiter_call", description="叫服务员", voice_text="叫服务员", category="服务"),
            CommandTemplate(name="催菜", code="rush_order", description="催菜", voice_text="催菜", category="厨房"),
            CommandTemplate(name="加主食", code="add_staple", description="加主食", voice_text="加主食", category="厨房"),
            CommandTemplate(name="结账", code="checkout", description="结账", voice_text="结账", category="服务"),
            CommandTemplate(name="换餐具", code="change_tableware", description="换餐具", voice_text="换餐具", category="服务"),
            CommandTemplate(name="加茶水", code="add_tea", description="加茶水", voice_text="加茶水", category="服务"),
        ]
        
        for template in command_templates:
            db.add(template)
        
        # 提交所有更改
        db.commit()
        
        print("✅ 生产环境数据库初始化完成！")
        print("\n📋 默认账户信息：")
        print("   系统管理员: admin / admin123")
        print("   餐饮经理: manager01 / manager123")
        print("   厨师长: chef01 / chef123")
        print("   打荷员: helper01 / helper123")
        print("   商务中心: business01 / business123")
        print("\n🏠 已创建10个默认包厢（1-10号包厢）")
        print("⚙️ 已配置系统基础设置")
        print("🔊 已配置语音播报设置")
        print("📝 已创建指令模板")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 数据库初始化失败: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    init_production_database()
