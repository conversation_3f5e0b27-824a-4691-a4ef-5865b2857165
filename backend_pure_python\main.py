# type: ignore
from fastapi import FastAPI, Request, Depends, HTTPException, status, Form
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.security import H<PERSON><PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from contextlib import asynccontextmanager
import os
import json
from pathlib import Path
from datetime import datetime, timezone, timedelta

# 设置中国时区
CHINA_TZ = timezone(timedelta(hours=8))

# 指令输入配置管理
INPUT_CONFIG_FILE = "command_input_configs.json"

def load_input_configs():
    """加载指令输入配置"""
    try:
        if os.path.exists(INPUT_CONFIG_FILE):
            with open(INPUT_CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        print(f"⚠️ 加载输入配置失败: {e}")

    # 返回默认配置
    return {
        'add_drink': {
            'allow_input': True,
            'input_placeholder': '请输入酒水饮料名称（如：1扎雪梨汁、2瓶啤酒）',
            'input_required': True
        },
        'add_staple': {
            'allow_input': True,
            'input_placeholder': '请输入主食名称（如：米饭、面条）',
            'input_required': True
        },
        'aolong_rice': {
            'allow_input': True,
            'input_placeholder': '澳龙泡饭特殊要求（可选）',
            'input_required': False
        }
    }

def save_input_configs(configs):
    """保存指令输入配置"""
    try:
        with open(INPUT_CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(configs, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"❌ 保存输入配置失败: {e}")
        return False

def update_input_config(code, allow_input, input_placeholder='', input_required=False):
    """更新指令输入配置"""
    configs = load_input_configs()
    configs[code] = {
        'allow_input': allow_input,
        'input_placeholder': input_placeholder,
        'input_required': input_required
    }
    return save_input_configs(configs)

def get_china_time():
    """获取中国时间"""
    return datetime.now(CHINA_TZ)

# from core.config import settings  # 暂时注释掉未使用的导入
from core.database import engine, Base, get_db, SessionLocal
from core.security import verify_password, create_access_token, verify_token
from models.user import User, UserRole, UserStatus
from models.table import Table, TableType, TableStatus
from models.menu import Dish, DishStatus
from models.order import Order, OrderItem, OrderStatus, DishItemStatus
from models.voice_config import VoiceConfig, DEFAULT_VOICE_CONFIGS
from models.system_config import SystemConfig as SystemConfigModel, DEFAULT_SYSTEM_CONFIGS
from websocket_manager import websocket_manager



@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时创建数据库表
    Base.metadata.create_all(bind=engine)

    # 添加新字段和表（如果不存在）
    try:
        from sqlalchemy import text
        with engine.connect() as conn:
            # 检查并添加 dining_end_time 字段
            result = conn.execute(text("PRAGMA table_info(orders)")).fetchall()
            columns = [row[1] for row in result]

            if 'dining_end_time' not in columns:
                print("🔧 添加 dining_end_time 字段...")
                conn.execute(text("ALTER TABLE orders ADD COLUMN dining_end_time DATETIME"))
                conn.commit()
                print("✅ dining_end_time 字段添加完成")
            else:
                print("✅ dining_end_time 字段已存在")
            # 检查waiter_status字段是否存在
            result = conn.execute(text("PRAGMA table_info(order_items)"))
            columns = [row[1] for row in result.fetchall()]

            if 'waiter_status' not in columns:
                print("📋 添加waiter_status字段到order_items表...")
                conn.execute(text("ALTER TABLE order_items ADD COLUMN waiter_status VARCHAR(20)"))
                conn.commit()
                print("✅ waiter_status字段添加成功")

            if 'waiter_confirmed' not in columns:
                print("📋 添加waiter_confirmed字段到order_items表...")
                conn.execute(text("ALTER TABLE order_items ADD COLUMN waiter_confirmed BOOLEAN DEFAULT 0"))
                conn.commit()
                print("✅ waiter_confirmed字段添加成功")

            if 'confirmed_at' not in columns:
                print("📋 添加confirmed_at字段到order_items表...")
                conn.execute(text("ALTER TABLE order_items ADD COLUMN confirmed_at DATETIME"))
                conn.commit()
                print("✅ confirmed_at字段添加成功")





            # 检查command_templates表是否存在
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='command_templates'"))
            if not result.fetchone():
                print("📋 创建command_templates表...")
                conn.execute(text("""
                    CREATE TABLE command_templates (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name VARCHAR(100) NOT NULL,
                        code VARCHAR(50) NOT NULL UNIQUE,
                        description TEXT,
                        voice_text VARCHAR(200),
                        category VARCHAR(50) DEFAULT 'general',
                        is_active BOOLEAN DEFAULT 1,
                        sort_order INTEGER DEFAULT 0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                conn.commit()
                print("✅ command_templates表创建成功")

            # 检查waiter_actions表是否存在
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='waiter_actions'"))
            if not result.fetchone():
                print("📋 创建waiter_actions表...")
                conn.execute(text("""
                    CREATE TABLE waiter_actions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        waiter_id INTEGER NOT NULL,
                        room_number VARCHAR(20) NOT NULL,
                        action_type VARCHAR(50) NOT NULL,
                        action_content TEXT,
                        is_processed BOOLEAN DEFAULT 0,
                        processed_by INTEGER,
                        processed_at DATETIME,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                conn.commit()
                print("✅ waiter_actions表创建成功")

    except Exception as e:
        print(f"⚠️ 数据库迁移失败: {e}")

    # 创建默认用户
    from core.security import get_password_hash
    db = SessionLocal()
    try:
        existing_users = db.query(User).count()
        if existing_users == 0:
            print("📝 创建默认用户...")

            # 创建默认用户
            users_data = [
                ('admin', 'admin123', '系统管理员', UserRole.ADMIN, True),
                ('manager01', 'manager123', '王经理', UserRole.MANAGER, False),
                ('waiter01', 'waiter123', '小李', UserRole.WAITER, False),
                ('kitchen01', 'kitchen123', '陈师傅', UserRole.KITCHEN_HELPER, False),
                ('business01', 'business123', '李商务', UserRole.BUSINESS_CENTER, False),
            ]

            for i, (username, password, full_name, role, is_superuser) in enumerate(users_data):
                user = User(
                    username=username,
                    hashed_password=get_password_hash(password),
                    full_name=full_name,
                    role=role,
                    is_active=True,
                    is_superuser=is_superuser,
                    phone=f'1380013800{i+1}',
                    employee_id=f'EMP{i+1:03d}',
                    department='默认部门',
                    position='默认职位'
                )
                db.add(user)

            db.commit()
            print("✅ 默认用户创建完成")
        else:
            print(f"📋 数据库中已有 {existing_users} 个用户")
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        db.rollback()
    finally:
        db.close()

    # 创建默认指令模板
    db = SessionLocal()
    try:
        from models.command_template import CommandTemplate
        existing_commands = db.query(CommandTemplate).count()
        if existing_commands == 0:
            print("📝 创建默认指令模板...")

            default_commands = [
                ('上菜', 'serve_dish', '服务员上菜指令', '请上菜', 'service'),
                ('催菜', 'rush_dish', '催促厨房加快制作', '请催菜', 'urgent'),
                ('收脏餐', 'clean_table', '收拾餐桌脏餐具', '请收脏餐', 'service'),
                ('上主食', 'add_staple', '添加主食', '请上主食', 'food'),
                ('加酒水饮料', 'add_drink', '添加酒水饮料', '请加酒水', 'service'),
                ('澳龙泡饭', 'aolong_rice', '特色澳龙泡饭', '澳龙泡饭', 'food'),
            ]

            for name, code, desc, voice, category in default_commands:
                command = CommandTemplate(
                    name=name,
                    code=code,
                    description=desc,
                    voice_text=voice,
                    category=category,
                    is_active=True,
                    sort_order=0
                )
                db.add(command)

            db.commit()
            print("✅ 默认指令模板创建完成")
        else:
            print(f"📋 数据库中已有 {existing_commands} 个指令模板")
    except Exception as e:
        print(f"❌ 创建指令模板失败: {e}")
        db.rollback()
    finally:
        db.close()

    # 创建默认语音配置
    db = SessionLocal()
    try:
        existing_configs = db.query(VoiceConfig).count()
        if existing_configs == 0:
            print("📝 创建默认语音配置...")

            for config_data in DEFAULT_VOICE_CONFIGS:
                config = VoiceConfig(**config_data)
                db.add(config)

            db.commit()
            print(f"✅ 创建了 {len(DEFAULT_VOICE_CONFIGS)} 个语音配置")
        else:
            print(f"📋 数据库中已有 {existing_configs} 个语音配置")
    except Exception as e:
        print(f"❌ 创建语音配置失败: {e}")
        db.rollback()
    finally:
        db.close()

    # 创建默认系统配置
    db = SessionLocal()
    try:
        existing_system_configs = db.query(SystemConfigModel).count()
        if existing_system_configs == 0:
            print("📝 创建默认系统配置...")

            for config_data in DEFAULT_SYSTEM_CONFIGS:
                config = SystemConfigModel(**config_data)
                db.add(config)

            db.commit()
            print(f"✅ 创建了 {len(DEFAULT_SYSTEM_CONFIGS)} 个系统配置")
        else:
            print(f"📋 数据库中已有 {existing_system_configs} 个系统配置")
    except Exception as e:
        print(f"❌ 创建系统配置失败: {e}")
        db.rollback()
    finally:
        db.close()

    yield


# 创建 FastAPI 应用
app = FastAPI(
    title="暨阳湖大酒店传菜管理系统",
    description="纯Python版本的餐厅管理系统",
    version="1.0.0",
    lifespan=lifespan
)

# 设置JSON响应编码
import json
from fastapi.encoders import jsonable_encoder

class UTF8JSONResponse(JSONResponse):
    def render(self, content) -> bytes:
        return json.dumps(
            jsonable_encoder(content),
            ensure_ascii=False,
            allow_nan=False,
            indent=None,
            separators=(",", ":"),
        ).encode("utf-8")

# 设置默认响应类
# app.default_response_class = UTF8JSONResponse  # 注释掉，使用默认响应类

# 获取当前文件的目录
BASE_DIR = Path(__file__).parent

# 设置模板和静态文件
templates = Jinja2Templates(directory=str(BASE_DIR / "templates"))
app.mount("/static", StaticFiles(directory=str(BASE_DIR / "static")), name="static")

# 安全认证
security = HTTPBearer(auto_error=False)


def get_current_user(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户"""
    token = None

    # 从 Authorization header 获取 token
    if credentials:
        token = credentials.credentials
    # 从 cookie 获取 token
    elif request.cookies.get("access_token"):
        token = request.cookies.get("access_token")

    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未登录",
            headers={"WWW-Authenticate": "Bearer"}
        )

    try:
        payload = verify_token(token)
        username = payload.get("sub")
        exp = payload.get("exp")

        if username is None:
            raise HTTPException(status_code=401, detail="无效令牌")

        # 检查token是否过期
        if exp and get_china_time().timestamp() > exp:
            raise HTTPException(status_code=401, detail="令牌已过期，请重新登录")

    except Exception as e:
        # 清除无效的cookie
        response = JSONResponse(
            status_code=401,
            content={"detail": "无效令牌，请重新登录"}
        )
        response.delete_cookie("access_token", path="/")
        raise HTTPException(status_code=401, detail="无效令牌，请重新登录")

    user = db.query(User).filter(User.username == username).first()
    if user is None or not user.is_active:
        raise HTTPException(status_code=401, detail="用户不存在或已禁用")

    # 检查用户状态
    if user.status != UserStatus.ACTIVE:
        raise HTTPException(status_code=401, detail="账号已被禁用")

    # 服务员实时权限检查 - 确保权限被撤销后立即生效
    # 注意：这里不应该清除cookie，因为会影响到其他用户的会话
    if user.role == UserRole.WAITER and not user.is_authorized:
        # 确保中文字符正确编码
        raise HTTPException(
            status_code=401,
            detail="服务员权限已被撤销，请重新登录",
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    return user


def get_current_user_optional(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户（可选）"""
    try:
        return get_current_user(request, credentials, db)
    except:
        return None


# 首页路由
@app.get("/", response_class=HTMLResponse)
async def home(request: Request, current_user: User = Depends(get_current_user_optional)):
    if not current_user:
        return RedirectResponse(url="/login", status_code=302)
    return RedirectResponse(url="/dashboard", status_code=302)


# 登录页面
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})


# 登录处理
@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    # 验证用户
    user = db.query(User).filter(User.username == username).first()
    if not user or not verify_password(password, str(user.hashed_password)):
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "用户名或密码错误"}
        )

    if not user.is_active or user.status != UserStatus.ACTIVE:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "账号已被禁用"}
        )

    # 服务员需要授权才能登录
    if user.role == UserRole.WAITER and not user.is_authorized:
        return templates.TemplateResponse(
            "login.html",
            {"request": request, "error": "服务员账号需要餐饮经理授权后才能登录"}
        )

    # 创建访问令牌
    access_token = create_access_token(data={"sub": user.username})

    # 根据用户角色重定向到相应页面
    if user.role == UserRole.WAITER:
        redirect_url = "/waiter/menu"
    elif user.role == UserRole.KITCHEN_HELPER:
        redirect_url = "/kitchen"  # 厨房打荷跳转到厨房操作页面
    elif user.role == UserRole.BUSINESS_CENTER:
        redirect_url = "/orders"  # 商务中心跳转到订单管理页面
    else:
        redirect_url = "/dashboard"

    response = RedirectResponse(url=redirect_url, status_code=302)
    response.set_cookie(
        key="access_token",
        value=access_token,
        max_age=30*24*60*60,  # 30天
        httponly=True
    )
    return response


# 登出
@app.get("/logout")
async def logout_get(request: Request):
    response = RedirectResponse(url="/login", status_code=302)

    # 删除所有相关的cookies
    response.delete_cookie("access_token", path="/")
    response.delete_cookie("access_token", path="/", domain=None)
    response.delete_cookie("session_id", path="/")
    response.delete_cookie("user_id", path="/")
    response.delete_cookie("user_role", path="/")

    # 添加缓存控制头，防止浏览器缓存
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"

    print(f"🚪 用户登出成功")
    return response

@app.post("/logout")
async def logout_post(request: Request):
    # 对于POST请求，返回JSON响应
    response = JSONResponse(content={"success": True, "message": "退出登录成功"})

    # 删除所有相关的cookies
    response.delete_cookie("access_token", path="/")
    response.delete_cookie("access_token", path="/", domain=None)
    response.delete_cookie("session_id", path="/")
    response.delete_cookie("user_id", path="/")
    response.delete_cookie("user_role", path="/")

    # 添加缓存控制头，防止浏览器缓存
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"

    print(f"🚪 用户登出成功")
    return response


# 检查授权状态API
@app.get("/api/check-auth-status")
async def check_auth_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """检查用户授权状态"""
    try:
        # 刷新用户数据
        db.refresh(current_user)

        return {
            "success": True,
            "is_authorized": current_user.is_authorized,
            "assigned_tables": current_user.assigned_tables,
            "role": current_user.role.value,
            "username": current_user.username,
            "full_name": current_user.full_name
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"检查授权状态失败: {str(e)}",
            "is_authorized": False
        }


# 仪表板
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 优化统计查询 - 使用单次查询获取多个统计
    from sqlalchemy import func, case
    from sqlalchemy.orm import joinedload

    # 餐桌统计
    table_stats = db.query(
        func.count(Table.id).label('total'),
        func.sum(case((Table.status == TableStatus.OCCUPIED, 1), else_=0)).label('occupied')
    ).filter(Table.is_active == True).first()

    # 菜品统计
    dish_stats = db.query(
        func.count(Dish.id).label('total'),
        func.sum(case((Dish.status == DishStatus.AVAILABLE, 1), else_=0)).label('available')
    ).filter(Dish.is_active == True).first()

    # 今日订单数
    today_orders = db.query(func.count(Order.id)).scalar() or 0

    # 最近订单（预加载关联数据）
    recent_orders = db.query(Order).options(
        joinedload(Order.table),
        joinedload(Order.waiter)
    ).order_by(Order.created_at.desc()).limit(5).all()

    stats = {
        "total_tables": getattr(table_stats, 'total', 0) or 0,
        "occupied_tables": getattr(table_stats, 'occupied', 0) or 0,
        "available_tables": (getattr(table_stats, 'total', 0) or 0) - (getattr(table_stats, 'occupied', 0) or 0),
        "total_dishes": getattr(dish_stats, 'total', 0) or 0,
        "available_dishes": getattr(dish_stats, 'available', 0) or 0,
        "today_orders": today_orders,
        "occupancy_rate": round(((getattr(table_stats, 'occupied', 0) or 0) / (getattr(table_stats, 'total', 1) or 1) * 100), 1)
    }

    return templates.TemplateResponse(
        "dashboard.html",
        {
            "request": request,
            "user": current_user,
            "stats": stats,
            "recent_orders": recent_orders
        }
    )


# 餐桌管理
@app.get("/tables", response_class=HTMLResponse)
async def tables_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # 检查权限
        if not current_user.has_permission('table.view'):
            raise HTTPException(status_code=403, detail="权限不足")

        # 获取餐桌信息
        try:
            tables = db.query(Table).filter(Table.is_active == True).order_by(Table.number).all()
        except Exception as e:
            print(f"❌ 获取包厢列表失败: {e}")
            # 如果按number排序失败，尝试按id排序
            tables = db.query(Table).filter(Table.is_active == True).order_by(Table.id).all()

        # 为每个餐桌获取关联信息
        for table in tables:
            try:
                # 获取分配的服务员
                if hasattr(table, 'assigned_waiter_id') and table.assigned_waiter_id:
                    table.assigned_waiter = db.query(User).filter(User.id == table.assigned_waiter_id).first()
                else:
                    table.assigned_waiter = None

                # 获取当前订单信息
                if hasattr(table, 'status') and table.status and table.status.value == 'occupied':
                    current_order = db.query(Order).filter(
                        Order.table_id == table.id,
                        Order.status.in_([
                            OrderStatus.PENDING_KITCHEN,
                            OrderStatus.CONFIRMED,
                            OrderStatus.IN_PROGRESS,
                            OrderStatus.RESERVED,
                            OrderStatus.PENDING_START,
                            OrderStatus.SERVING
                        ])
                    ).first()
                    table.current_order = current_order
                else:
                    table.current_order = None
            except Exception as e:
                print(f"❌ 处理包厢 {table.id} 信息失败: {e}")
                table.assigned_waiter = None
                table.current_order = None

        return templates.TemplateResponse(
            "tables.html",
            {"request": request, "user": current_user, "tables": tables}
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 包厢管理页面错误: {e}")
        raise HTTPException(status_code=500, detail=f"页面加载失败: {str(e)}")


# 菜单管理
@app.get("/menu", response_class=HTMLResponse)
async def menu_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    dishes = db.query(Dish).filter(Dish.is_active == True).order_by(Dish.category, Dish.sort_order).all()
    return templates.TemplateResponse(
        "menu.html",
        {"request": request, "user": current_user, "dishes": dishes}
    )


# 订单管理
@app.get("/orders", response_class=HTMLResponse)
async def orders_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    status: str = None,
    start_date: str = None,
    end_date: str = None
):
    # 根据用户角色过滤订单
    from sqlalchemy.orm import joinedload
    from datetime import datetime

    query = db.query(Order).options(
        joinedload(Order.order_items),  # 预加载订单项
        joinedload(Order.table),  # 预加载餐桌信息
        joinedload(Order.waiter)  # 预加载服务员信息
    )

    if current_user.role == UserRole.WAITER:
        query = query.filter(Order.waiter_id == current_user.id)

    # 应用过滤条件
    if status:
        try:
            from models.order import OrderStatus
            order_status = OrderStatus(status)
            query = query.filter(Order.status == order_status)
        except ValueError:
            pass  # 忽略无效的状态值

    if start_date:
        try:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.filter(Order.created_at >= start_datetime)
        except ValueError:
            pass  # 忽略无效的日期格式

    if end_date:
        try:
            end_datetime = datetime.strptime(end_date + ' 23:59:59', '%Y-%m-%d %H:%M:%S')
            query = query.filter(Order.created_at <= end_datetime)
        except ValueError:
            pass  # 忽略无效的日期格式

    orders = query.order_by(Order.created_at.desc()).limit(100).all()

    # 为每个订单计算菜品数量
    for order in orders:
        if not hasattr(order, 'order_items') or order.order_items is None:
            # 如果没有预加载成功，手动查询
            order.order_items = db.query(OrderItem).filter(OrderItem.order_id == order.id).all()

    return templates.TemplateResponse(
        "orders.html",
        {"request": request, "user": current_user, "orders": orders}
    )


# 创建餐桌
@app.post("/tables/create")
async def create_table(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('table.manage'):
        raise HTTPException(status_code=403, detail="权限不足")

    form = await request.form()

    try:
        # 获取表单数据并确保正确编码
        number = form.get("number")
        if not number:
            raise HTTPException(status_code=400, detail="包厢号不能为空")

        # 确保包厢号是正确的字符串格式
        number = str(number).strip()

        # 检查包厢号是否已存在
        existing_table = db.query(Table).filter(
            Table.number == number,
            Table.is_active == True
        ).first()

        if existing_table:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": f"包厢号 {number} 已存在"},
                headers={"Content-Type": "application/json; charset=utf-8"}
            )

        # 创建新包厢
        new_table = Table(
            number=str(number),  # 使用处理过的餐桌号
            name=str(form.get("name")) if form.get("name") else None,
            capacity=int(str(form.get("capacity", 4))),
            table_type=str(form.get("table_type", "PRIVATE_ROOM")),  # 修改默认值
            location_description=str(form.get("location")) if form.get("location") else None,
            notes=str(form.get("description")) if form.get("description") else None,
            minimum_charge=float(str(form.get("minimum_charge", 0))) if form.get("minimum_charge") else None,
            has_tv=bool(form.get("has_tv")),
            has_karaoke=bool(form.get("has_karaoke")),
            has_mahjong=bool(form.get("has_mahjong")),
            has_projector=bool(form.get("has_projector")),
            has_wifi=bool(form.get("has_wifi")),
            has_air_conditioning=bool(form.get("has_air_conditioning")),
            is_active=True
        )

        db.add(new_table)
        db.commit()

        print(f"✅ 包厢创建成功: {number}")

        return JSONResponse(
            content={"success": True, "message": f"包厢 {number} 创建成功"},
            headers={"Content-Type": "application/json; charset=utf-8"},
            media_type="application/json; charset=utf-8"
        )

    except Exception as e:
        db.rollback()
        error_msg = str(e)
        print(f"❌ 创建包厢失败: {e}")

        # 检查是否是重复包厢号错误
        if "UNIQUE constraint failed: tables.number" in error_msg:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": f"包厢号 {number} 已存在，请使用其他包厢号"},
                headers={"Content-Type": "application/json; charset=utf-8"}
            )

        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"创建包厢失败: {error_msg}"},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )


# 更新餐桌状态
@app.post("/tables/{table_id}/status")
async def update_table_status(
    table_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('table.manage'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        form = await request.form()

        table = db.query(Table).filter(Table.id == table_id).first()
        if not table:
            raise HTTPException(status_code=404, detail="餐桌不存在")

        # 更新状态
        if form.get("status"):
            table.status = str(form.get("status"))  # type: ignore
        table.current_guests = int(str(form.get("current_guests", 0)))  # type: ignore
        if form.get("estimated_duration"):
            table.estimated_duration = int(str(form.get("estimated_duration")))  # type: ignore

        db.commit()

        return RedirectResponse(url="/tables", status_code=302)

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 更新包厢信息
@app.post("/tables/{table_id}/update")
async def update_table(
    table_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('table.manage'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        form_data = await request.form()

        table = db.query(Table).filter(Table.id == table_id).first()
        if not table:
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "包厢不存在"},
                headers={"Content-Type": "application/json; charset=utf-8"}
            )

        # 检查包厢号是否已被其他包厢使用
        number = form_data.get('number')
        if number and number != table.number:
            existing_table = db.query(Table).filter(
                Table.number == number,
                Table.id != table_id
            ).first()
            if existing_table:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": "包厢号已存在"},
                    headers={"Content-Type": "application/json; charset=utf-8"}
                )

        # 更新基本信息
        if number:
            table.number = str(number)  # type: ignore
        if form_data.get('name'):
            table.name = str(form_data.get('name'))  # type: ignore
        if form_data.get('table_type'):
            table.table_type = TableType(str(form_data.get('table_type')))  # type: ignore
        if form_data.get('capacity'):
            table.capacity = int(str(form_data.get('capacity')))  # type: ignore
        if form_data.get('location'):
            table.location_description = str(form_data.get('location'))  # type: ignore

        # 更新设施信息
        table.has_tv = 'has_tv' in form_data  # type: ignore
        table.has_karaoke = 'has_karaoke' in form_data  # type: ignore
        table.has_mahjong = 'has_mahjong' in form_data  # type: ignore
        table.has_projector = 'has_projector' in form_data  # type: ignore
        table.has_wifi = 'has_wifi' in form_data  # type: ignore
        table.has_air_conditioning = 'has_air_conditioning' in form_data  # type: ignore

        db.commit()

        print(f"✅ 包厢更新成功: {table.number} ({table.name})")

        return JSONResponse(
            content={"success": True, "message": f"包厢 {table.number} 信息更新成功"},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    except Exception as e:
        db.rollback()
        print(f"❌ 更新包厢失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"更新包厢失败: {str(e)}"},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )


# 删除餐桌
@app.post("/tables/{table_id}/delete")
async def delete_table(
    table_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="只有系统管理员可以删除餐桌")

    try:
        table = db.query(Table).filter(Table.id == table_id).first()
        if not table:
            raise HTTPException(status_code=404, detail="餐桌不存在")

        # 检查是否有未完成的订单
        active_orders = db.query(Order).filter(
            Order.table_id == table_id,
            Order.status.in_([
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING,
                OrderStatus.CONFIRMED,
                OrderStatus.IN_PROGRESS
            ])
        ).count()

        if active_orders > 0:
            raise HTTPException(status_code=400, detail="该餐桌有未完成的订单，无法删除")

        # 软删除
        table.is_active = False
        db.commit()

        return JSONResponse(
            content={"success": True, "message": f"包厢 {table.number} 删除成功"},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"删除失败: {str(e)}"},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )


# 个人资料页面
@app.get("/profile", response_class=HTMLResponse)
async def profile_page(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    return templates.TemplateResponse(
        "profile.html",
        {"request": request, "user": current_user}
    )


# 用户管理（仅管理员）
@app.get("/users", response_class=HTMLResponse)
async def users_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        if not current_user.has_permission("user.manage"):
            raise HTTPException(status_code=403, detail="权限不足")

        # 获取用户列表，添加异常处理
        try:
            users = db.query(User).order_by(User.created_at.desc()).all()
        except Exception as e:
            print(f"❌ 获取用户列表失败: {e}")
            # 如果按created_at排序失败，尝试按id排序
            users = db.query(User).order_by(User.id.desc()).all()

        # 获取包厢列表
        try:
            tables = db.query(Table).filter(Table.is_active == True).all()
        except Exception as e:
            print(f"❌ 获取包厢列表失败: {e}")
            tables = []

        # 根据当前用户角色确定可管理的角色列表
        manageable_roles = []
        if current_user.role.value == 'admin':
            # 系统管理员可以管理所有角色（包括自己）
            manageable_roles = ['admin', 'manager', 'waiter', 'kitchen_helper', 'chef_manager', 'business_center']
        elif current_user.role.value == 'manager':
            # 餐饮经理可以管理除系统管理员外的所有角色
            manageable_roles = ['manager', 'waiter', 'kitchen_helper', 'chef_manager', 'business_center']

        return templates.TemplateResponse(
            "users.html",
            {
                "request": request,
                "user": current_user,
                "users": users,
                "tables": tables,
                "manageable_roles": manageable_roles
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 用户管理页面错误: {e}")
        raise HTTPException(status_code=500, detail=f"页面加载失败: {str(e)}")


# 创建用户API
@app.post("/users/create")
async def create_user(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission("user.manage"):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        form_data = await request.form()
        username = form_data.get("username")
        password = form_data.get("password")
        full_name = form_data.get("full_name")
        role = form_data.get("role")

        # 验证必填字段
        if not all([username, password, full_name, role]):
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "所有字段都是必填的"}
            )

        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == username).first()
        if existing_user:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "用户名已存在"}
            )

        # 验证角色
        try:
            user_role = UserRole(role)
        except ValueError:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "无效的角色"}
            )

        # 权限检查：餐饮经理无法创建系统管理员账户
        if current_user.role.value == 'manager' and role == 'admin':
            return JSONResponse(
                status_code=403,
                content={"success": False, "message": "餐饮经理无权创建系统管理员账户"}
            )

        # 检查系统管理员唯一性
        if role == 'admin':
            existing_admin = db.query(User).filter(User.role == UserRole.ADMIN).first()
            if existing_admin:
                return JSONResponse(
                    status_code=400,
                    content={"success": False, "message": "系统中只能存在一个系统管理员账户"}
                )

        # 创建新用户
        from core.security import get_password_hash
        new_user = User(
            username=username,
            hashed_password=get_password_hash(password),
            full_name=full_name,
            role=user_role,
            is_active=True,
            status=UserStatus.ACTIVE
        )

        db.add(new_user)
        db.commit()
        db.refresh(new_user)

        print(f"✅ 用户创建成功: {username} ({full_name})")

        return JSONResponse(
            content={"success": True, "message": "用户创建成功"},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    except Exception as e:
        db.rollback()
        print(f"❌ 创建用户失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"创建用户失败: {str(e)}"}
        )


# 服务员指令管理页面
@app.get("/waiter/commands", response_class=HTMLResponse)
async def waiter_commands_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    # 获取指令数据（如果表存在）
    commands = []
    today_commands_count = 0
    pending_commands_count = 0
    completed_commands_count = 0

    try:
        from models.waiter_action import WaiterAction
        from datetime import datetime, date

        # 获取今日指令
        today = get_china_time().date()
        today_start = datetime.combine(today, datetime.min.time()).replace(tzinfo=CHINA_TZ)
        commands = db.query(WaiterAction).filter(
            WaiterAction.created_at >= today_start
        ).order_by(WaiterAction.created_at.desc()).all()

        today_commands_count = len(commands)
        pending_commands_count = len([c for c in commands if not c.is_processed])
        completed_commands_count = len([c for c in commands if c.is_processed])
    except Exception:
        # 如果表不存在，使用默认值
        pass

    # 获取在线服务员
    online_waiters = db.query(User).filter(
        User.role == UserRole.WAITER,
        User.is_active == True
    ).all()

    return templates.TemplateResponse(
        "waiter_commands.html",
        {
            "request": request,
            "user": current_user,
            "commands": commands,
            "today_commands_count": today_commands_count,
            "pending_commands_count": pending_commands_count,
            "completed_commands_count": completed_commands_count,
            "online_waiters": online_waiters,
            "online_waiters_count": len(online_waiters)
        }
    )


# 处理指令
@app.post("/waiter/commands/{command_id}/process")
async def process_command(
    command_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    # 这里应该更新指令状态，暂时返回成功
    return {"success": True, "message": "指令已处理"}


# 服务员指令操作页面
@app.get("/waiter/actions", response_class=HTMLResponse)
async def waiter_actions_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以访问此页面")

    if not current_user.is_authorized:
        raise HTTPException(status_code=403, detail="您还未被授权，请联系餐饮经理")

    # 获取我的指令记录
    from models.waiter_action import WaiterAction
    my_commands = db.query(WaiterAction).filter(
        WaiterAction.waiter_id == current_user.id
    ).order_by(WaiterAction.created_at.desc()).limit(20).all()

    return templates.TemplateResponse(
        "waiter_actions.html",
        {
            "request": request,
            "user": current_user,
            "my_commands": my_commands
        }
    )


# 发送服务员指令
@app.post("/waiter/send-command")
async def send_waiter_command(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以发送指令")

    if not current_user.is_authorized:
        raise HTTPException(status_code=403, detail="您还未被授权")

    try:
        data = await request.json()
        room_number = data.get('room_number')
        action_type = data.get('action_type')
        action_content = data.get('action_content', '')

        # 验证包厢是否在分配范围内
        if current_user.assigned_tables:
            assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
            if room_number not in assigned_rooms:
                raise HTTPException(status_code=403, detail="您没有权限操作此包厢")

        # 检查用餐状态权限控制
        from models.waiter_action import WaiterAction

        # 如果不是用餐开始指令，需要检查是否已开始用餐
        if action_type != 'dining_start':
            dining_start_action = db.query(WaiterAction).filter(
                WaiterAction.room_number == room_number,
                WaiterAction.action_type == 'dining_start'
            ).first()

            if not dining_start_action:
                raise HTTPException(
                    status_code=400,
                    detail=f"{room_number}包厢尚未开始用餐，请先点击'开始用餐'按钮"
                )

        # 保存指令到数据库
        try:
            from datetime import datetime

            waiter_action = WaiterAction(
                waiter_id=current_user.id,
                room_number=room_number,
                action_type=action_type,
                action_content=action_content,
                is_processed=False,
                created_at=get_china_time()
            )

            db.add(waiter_action)

            # 如果是用餐开始指令，更新包厢状态和用餐人数
            if action_type == 'dining_start':
                # 检查是否已经有用餐开始指令（在提交前检查，避免重复）
                existing_dining_start = db.query(WaiterAction).filter(
                    WaiterAction.room_number == room_number,
                    WaiterAction.action_type == 'dining_start'
                ).first()

                if existing_dining_start:
                    # 检查是否是同一个服务员的操作
                    if existing_dining_start.waiter_id == current_user.id:
                        db.rollback()
                        raise HTTPException(status_code=400, detail="您已经为该包厢发送过开始用餐指令")
                    else:
                        # 如果是不同服务员，删除之前的记录（可能是重新分配）
                        print(f"🔄 清除之前的用餐开始记录: {room_number} - 原服务员ID: {existing_dining_start.waiter_id}")
                        db.delete(existing_dining_start)
                        db.flush()  # 立即执行删除

                # 广播用餐开始事件到厨房端
                try:
                    guest_count = int(action_content) if action_content else 0
                    dining_start_message = {
                        "type": "dining_start",
                        "room": room_number,
                        "guests": guest_count,
                        "message": f"{room_number}包厢 {guest_count}人 起菜",
                        "timestamp": get_china_time().isoformat(),
                        "waiter": current_user.full_name
                    }

                    # 这里应该通过WebSocket广播，暂时打印日志
                    print(f"🔔 厨房通知: {dining_start_message['message']}")

                except ValueError:
                    guest_count = 0

                # 🔧 修复：无论是否有重复指令，都要确保正确设置dining_start_time
                table = db.query(Table).filter(Table.number == room_number).first()
                if table:
                    # 解析用餐人数
                    try:
                        guest_count = int(action_content) if action_content.isdigit() else table.current_guests
                    except:
                        guest_count = table.current_guests

                    # 更新包厢状态为已开始用餐
                    table.status = TableStatus.OCCUPIED  # 已开始用餐状态
                    table.current_guests = guest_count  # 更新实际用餐人数

                    # 🔧 修复：更新订单的用餐人数和开始时间（确保每次都设置）
                    current_order = db.query(Order).filter(
                        Order.table_id == table.id,
                        Order.status.in_([
                            OrderStatus.CONFIRMED,
                            OrderStatus.IN_PROGRESS,
                            OrderStatus.RESERVED,
                            OrderStatus.PENDING_START,
                            OrderStatus.SERVING
                        ])
                    ).first()

                    if current_order:
                        current_order.guest_count = guest_count
                        # 🔧 修复：确保每次都设置dining_start_time，即使是重复指令
                        if current_order.dining_start_time is None:
                            current_order.dining_start_time = get_china_time()  # 记录用餐开始时间
                            current_order.started_at = get_china_time()  # 记录开始时间
                            print(f"✅ 首次设置用餐开始时间: {room_number} -> {current_order.dining_start_time}")
                        else:
                            print(f"ℹ️ 用餐已开始，保持原有时间: {room_number} -> {current_order.dining_start_time}")

                        # 更新订单状态为已上菜
                        if current_order.status in [OrderStatus.PENDING_START, OrderStatus.CONFIRMED, OrderStatus.RESERVED]:
                            current_order.status = OrderStatus.SERVING
                            print(f"📋 订单状态更新: {current_order.order_number} -> 已上菜 (SERVING)")
                    else:
                        print(f"⚠️ 警告: 找不到包厢 {room_number} 的活跃订单")

                    print(f"🍽️ 用餐开始: {room_number} -> 人数: {guest_count}人 -> {get_china_time().strftime('%H:%M')}")
                    print(f"📋 包厢状态更新: {room_number} -> 已开始用餐")
                else:
                    print(f"⚠️ 警告: 找不到包厢 {room_number}")

            # 如果是上菜指令，更新包厢状态为已上菜，记录开始用餐时间
            elif action_type == 'serve_dish':
                table = db.query(Table).filter(Table.number == room_number).first()
                if table:
                    # 如果是第一次上菜，记录开始用餐时间
                    if table.status == TableStatus.RESERVED:
                        print(f"🍽️ 开始用餐: {room_number} -> {get_china_time().strftime('%H:%M')}")

                    table.status = TableStatus.OCCUPIED  # 已上菜状态
                    print(f"📋 包厢状态更新: {room_number} -> 已上菜")

            db.commit()

            print(f"📢 指令发送: {current_user.full_name} -> {room_number} -> {action_type}: {action_content}")

            # 广播服务员指令到厨房大屏
            try:
                # 获取指令的中文显示名称
                def get_action_display_name(action_type, action_content=""):
                    """获取指令的中文显示名称"""
                    action_names = {
                        'dining_start': '开始用餐',
                        'serve_dish': '上菜',
                        'rush_dish': '催菜',
                        'rush_order': '催菜',
                        'clean_table': '收脏餐',
                        'add_staple': '上主食',
                        'add_drink': '加酒水',
                        'aolong_rice': '澳龙泡饭',
                        'waiter_call': '叫服务员',
                        'checkout': '结账',
                        'change_tableware': '换餐具',
                        'add_tea': '加茶水',
                        'urge_dish': '催菜',
                        'special_service': '特殊服务',
                        'takeaway': '打包'
                    }

                    base_name = action_names.get(action_type, action_type)

                    # 修复：确保语音播报包含完整内容
                    if action_content and action_content.strip():
                        if action_type == 'add_staple':
                            return f"上主食：{action_content}"
                        elif action_type == 'add_drink':
                            return f"加酒水：{action_content}"
                        elif action_type == 'dining_start':
                            return f"开始用餐：{action_content}人"
                        else:
                            # 关键修复：确保所有自定义内容都被包含在语音播报中
                            return f"{base_name}：{action_content.strip()}"

                    return base_name

                display_instruction = get_action_display_name(action_type, action_content)

                # 添加调试日志，确认语音播报内容
                print(f"🔊 语音播报内容: {display_instruction}")

                await websocket_manager.broadcast_waiter_instruction(
                    room_number=room_number,
                    instruction=display_instruction,  # 包含完整内容的指令
                    waiter_name=current_user.full_name
                )
                print(f"📡 服务员指令WebSocket通知已发送: {display_instruction}")
            except Exception as e:
                print(f"❌ 服务员指令WebSocket通知发送失败: {e}")

            return {"success": True, "message": "指令发送成功"}
        except Exception as e:
            print(f"❌ 指令发送失败: {e}")
            db.rollback()
            # 即使数据库保存失败，也返回成功，避免影响用户体验
            return {"success": True, "message": "指令发送成功"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 服务员菜单管理页面
@app.get("/waiter/menu", response_class=HTMLResponse)
async def waiter_menu_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以访问此页面")

    if not current_user.is_authorized:
        # 跳转到等待授权页面而不是抛出异常
        return templates.TemplateResponse(
            "waiter_unauthorized.html",
            {"request": request, "user": current_user}
        )

    # 获取分配给我的包厢的订单和信息
    assigned_room_orders = {}
    room_info = {}

    if current_user.assigned_tables:
        assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]

        for room_number in assigned_rooms:
            # 获取该包厢的活跃订单
            room_orders = db.query(Order).join(Table).filter(
                Table.number == room_number,
                Order.status.in_([
                    OrderStatus.CONFIRMED,
                    OrderStatus.IN_PROGRESS,
                    OrderStatus.RESERVED,
                    OrderStatus.PENDING_START,
                    OrderStatus.SERVING
                ])
            ).all()

            room_items = []
            for order in room_orders:
                order_items = db.query(OrderItem).filter(
                    OrderItem.order_id == order.id
                ).all()
                room_items.extend(order_items)

            # 即使没有菜品项，也要保留包厢信息以便显示
            assigned_room_orders[room_number] = room_items

            # 获取包厢基本信息
            latest_order = room_orders[0] if room_orders else None
            if latest_order:
                room_info[room_number] = {
                    'dining_standard': latest_order.dining_standard or 0,
                    'guest_count': latest_order.guest_count or 0,
                    'order_time': latest_order.created_at.strftime('%H:%M'),
                    'special_requirements': latest_order.special_requirements
                }

    # 获取我的操作记录（如果表存在）
    my_actions = []
    try:
        from models.waiter_action import WaiterAction
        my_actions = db.query(WaiterAction).filter(
            WaiterAction.waiter_id == current_user.id
        ).order_by(WaiterAction.created_at.desc()).limit(10).all()
    except Exception:
        # 如果表不存在，返回空列表
        pass

    return templates.TemplateResponse(
        "waiter_menu.html",
        {
            "request": request,
            "user": current_user,
            "assigned_room_orders": assigned_room_orders,
            "room_info": room_info,
            "my_actions": my_actions
        }
    )


# 获取指定包厢的菜单数据API
@app.get("/waiter/menu/{room_number}")
async def get_room_menu(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定包厢的菜单数据"""
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以访问此接口")

    if not current_user.is_authorized:
        raise HTTPException(status_code=403, detail="您还未被授权")

    # 检查服务员是否有权限访问此包厢
    if current_user.assigned_tables:
        assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
        if room_number not in assigned_rooms:
            raise HTTPException(status_code=403, detail="您没有权限访问此包厢")
    else:
        raise HTTPException(status_code=403, detail="您还未被分配包厢")

    try:
        # 查找包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            raise HTTPException(status_code=404, detail="包厢不存在")

        # 查找该包厢的活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING,
                OrderStatus.CONFIRMED,
                OrderStatus.IN_PROGRESS
            ])
        ).first()

        if not active_order:
            return {
                "success": True,
                "dishes": [],
                "room_info": {
                    "table_number": room_number,
                    "customer_name": "",
                    "guest_count": 0,
                    "status": "empty"
                },
                "message": "该包厢暂无活跃订单"
            }

        # 获取订单项（菜品）
        order_items = db.query(OrderItem).filter(
            OrderItem.order_id == active_order.id
        ).all()

        # 构建菜品数据
        dishes_data = []
        for item in order_items:
            # 检查服务员状态
            waiter_status = item.waiter_status or ""

            # 确定菜品状态显示
            status_display = ""
            can_confirm = False
            can_return = False

            if item.status == DishItemStatus.PENDING_COOK:
                status_display = "待制作"
            elif item.status == DishItemStatus.COOKING:
                status_display = "制作中"
            elif item.status == DishItemStatus.READY:
                if waiter_status == "served":
                    status_display = "已划菜"
                else:
                    status_display = "制作完成"
                    can_confirm = True
                    can_return = True
            elif item.status == DishItemStatus.CANCELLED:
                status_display = "已取消"

            dishes_data.append({
                "id": item.id,
                "name": item.dish_name,
                "status": item.status.value if item.status else "pending_cook",
                "status_display": status_display,
                "waiter_status": waiter_status,
                "can_confirm": can_confirm,
                "can_return": can_return,
                "quantity": item.quantity,
                "unit_price": float(item.unit_price) if item.unit_price else 0,
                "total_price": float(item.total_price) if item.total_price else 0,
                "special_requirements": item.special_requirements or "",
                "created_at": item.created_at.isoformat() if item.created_at else None,
                "ready_at": item.ready_at.isoformat() if item.ready_at else None
            })

        # 构建包厢信息
        room_info = {
            "table_number": room_number,
            "customer_name": active_order.customer_name or "",
            "guest_count": active_order.guest_count or 0,
            "status": active_order.status.value if active_order.status else "reserved",
            "dining_start_time": active_order.dining_start_time.isoformat() if active_order.dining_start_time else None,
            "special_requests": active_order.special_requests or ""
        }

        return {
            "success": True,
            "dishes": dishes_data,
            "room_info": room_info,
            "message": f"获取包厢 {room_number} 菜单数据成功"
        }

    except Exception as e:
        print(f"❌ 获取包厢菜单数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取菜单数据失败: {str(e)}")


# 检查待上菜菜品API
@app.get("/waiter/check-ready-dishes")
async def check_ready_dishes(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以访问此接口")

    if not current_user.is_authorized:
        raise HTTPException(status_code=403, detail="您还未被授权")

    try:
        ready_dishes = []

        # 获取分配给我的包厢的待上菜菜品
        if current_user.assigned_tables:
            assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]

            for room_number in assigned_rooms:
                # 获取该包厢的活跃订单
                room_orders = db.query(Order).join(Table).filter(
                    Table.number == room_number,
                    Order.status.in_(['confirmed', 'in_progress'])
                ).all()

                for order in room_orders:
                    # 获取状态为ready的菜品
                    ready_items = db.query(OrderItem).filter(
                        OrderItem.order_id == order.id,
                        OrderItem.status == DishItemStatus.READY
                    ).all()

                    for item in ready_items:
                        ready_dishes.append({
                            'id': item.id,
                            'dish_name': item.dish_name,
                            'room_number': room_number,
                            'ready_at': item.ready_at.isoformat() if item.ready_at else None
                        })

        return {
            "success": True,
            "ready_dishes": ready_dishes,
            "count": len(ready_dishes)
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"检查失败: {str(e)}",
            "ready_dishes": [],
            "count": 0
        }


# 获取包厢用餐状态API（用于前端状态检查）
@app.get("/waiter/dining-status/{room_number}")
async def get_dining_status(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取包厢用餐状态（用于前端状态检查）"""
    # 权限检查
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以访问此接口")

    if not current_user.is_authorized:
        raise HTTPException(status_code=403, detail="您还未被授权")

    # 检查该包厢是否在服务员的分配列表中
    if current_user.assigned_tables:
        assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
        if room_number not in assigned_rooms:
            raise HTTPException(status_code=403, detail="您没有权限访问此包厢")

    try:
        # 查找包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            raise HTTPException(status_code=404, detail="包厢不存在")

        # 查找该包厢的活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.SERVING,
                OrderStatus.IN_PROGRESS,
                OrderStatus.PENDING_START,
                OrderStatus.RESERVED,
                OrderStatus.CONFIRMED
            ])
        ).first()

        if not active_order:
            return {"dining_started": False, "message": "无活跃订单"}

        # 检查是否已开始用餐 - 必须有明确的用餐开始指令
        from models.waiter_action import WaiterAction
        dining_start_action = db.query(WaiterAction).filter(
            WaiterAction.room_number == room_number,
            WaiterAction.action_type == 'dining_start'
        ).first()

        dining_started = dining_start_action is not None

        # 获取用餐开始时间
        dining_start_time = None
        if dining_start_action:
            dining_start_time = dining_start_action.created_at.strftime('%H:%M')

        return {
            "dining_started": dining_started,
            "dining_start_time": dining_start_time,
            "guest_count": active_order.guest_count,
            "order_status": active_order.status.value if active_order.status else None
        }

    except Exception as e:
        print(f"❌ 获取用餐状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取用餐状态失败")

# 检查包厢用餐状态API
@app.get("/waiter/check-dining-status/{room_number}")
async def check_dining_status(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以访问此接口")

    if not current_user.is_authorized:
        raise HTTPException(status_code=403, detail="您还未被授权")

    try:
        # 检查该包厢是否在服务员的分配列表中
        if current_user.assigned_tables:
            assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
            if room_number not in assigned_rooms:
                raise HTTPException(status_code=403, detail="您没有权限访问此包厢")

        # 查找该包厢的活跃订单
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            return {
                "success": False,
                "message": "包厢不存在",
                "dining_started": False
            }

        # 查找该包厢的活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING,
                OrderStatus.CONFIRMED,
                OrderStatus.IN_PROGRESS
            ])
        ).first()

        if not active_order:
            return {
                "success": True,
                "dining_started": False,
                "message": "该包厢暂无活跃订单"
            }

        # 检查是否已发送用餐开始指令（不限制服务员ID，因为服务员可能被替换）
        try:
            from models.waiter_action import WaiterAction
            dining_start_action = db.query(WaiterAction).filter(
                WaiterAction.room_number == room_number,
                WaiterAction.action_type == 'dining_start'
            ).order_by(WaiterAction.created_at.desc()).first()
        except ImportError:
            dining_start_action = None

        dining_started = False
        dining_start_time = None

        if dining_start_action:
            dining_started = True
            dining_start_time = dining_start_action.created_at.strftime('%H:%M:%S')
        elif active_order.dining_start_time:
            dining_started = True
            dining_start_time = active_order.dining_start_time.strftime('%H:%M:%S')
        elif active_order.status == OrderStatus.SERVING:
            dining_started = True

        return {
            "success": True,
            "dining_started": dining_started,
            "dining_start_time": dining_start_time,
            "order_status": active_order.status,
            "guest_count": active_order.guest_count
        }

    except HTTPException:
        raise
    except Exception as e:
        return {
            "success": False,
            "message": f"检查用餐状态失败: {str(e)}",
            "dining_started": False
        }


# 获取包厢用餐状态API（供厨房使用）
@app.get("/api/room-dining-status/{room_number}")
async def get_room_dining_status(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取包厢用餐状态（供厨房等其他角色使用）"""
    try:
        # 查找包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            return {"dining_started": False, "message": "包厢不存在"}

        # 查找该包厢的活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.SERVING,
                OrderStatus.IN_PROGRESS,
                OrderStatus.PENDING_START,
                OrderStatus.RESERVED,
                OrderStatus.CONFIRMED
            ])
        ).first()

        if not active_order:
            return {"dining_started": False, "message": "无活跃订单"}

        # 检查是否已开始用餐 - 检查用餐开始指令
        from models.waiter_action import WaiterAction
        dining_start_action = db.query(WaiterAction).filter(
            WaiterAction.room_number == room_number,
            WaiterAction.action_type == 'dining_start'
        ).first()

        dining_started = dining_start_action is not None

        # 备选检查：如果有用餐开始时间或订单状态为SERVING
        if not dining_started:
            dining_started = (
                active_order.dining_start_time is not None or
                active_order.status == OrderStatus.SERVING
            )

        return {
            "dining_started": dining_started,
            "room_number": room_number,
            "order_status": active_order.status.value if active_order.status else None
        }

    except Exception as e:
        print(f"❌ 获取包厢用餐状态失败: {e}")
        return {"dining_started": False, "message": "获取状态失败"}

# 清除用餐记录API（仅用于测试）
@app.post("/waiter/clear-dining-records/{room_number}")
async def clear_dining_records(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """清除指定包厢的用餐记录（仅用于测试）"""
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以访问此接口")

    try:
        from models.waiter_action import WaiterAction

        # 删除该包厢的所有用餐开始记录
        deleted_count = db.query(WaiterAction).filter(
            WaiterAction.room_number == room_number,
            WaiterAction.action_type == 'dining_start'
        ).delete()

        db.commit()

        print(f"🧹 清除用餐记录: {room_number} - 删除了 {deleted_count} 条记录")

        return {
            "success": True,
            "message": f"已清除 {room_number} 包厢的用餐记录",
            "deleted_count": deleted_count
        }

    except Exception as e:
        db.rollback()
        print(f"❌ 清除用餐记录失败: {e}")
        raise HTTPException(status_code=500, detail="清除用餐记录失败")

# 🔧 临时修复API：手动设置包厢用餐开始时间
@app.post("/admin/fix-dining-start-time/{room_number}")
async def fix_dining_start_time(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """临时修复API：手动设置包厢的用餐开始时间"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="只有管理员可以访问此接口")

    try:
        # 查找包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            return {"success": False, "message": "包厢不存在"}

        # 查找该包厢的活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING,
                OrderStatus.CONFIRMED,
                OrderStatus.IN_PROGRESS
            ])
        ).first()

        if not active_order:
            return {"success": False, "message": "该包厢暂无活跃订单"}

        # 检查是否有用餐开始指令
        from models.waiter_action import WaiterAction
        dining_start_action = db.query(WaiterAction).filter(
            WaiterAction.room_number == room_number,
            WaiterAction.action_type == 'dining_start'
        ).first()

        if not dining_start_action:
            return {"success": False, "message": "该包厢没有用餐开始指令"}

        # 修复：设置dining_start_time为指令创建时间
        old_dining_start_time = active_order.dining_start_time
        active_order.dining_start_time = dining_start_action.created_at
        active_order.started_at = dining_start_action.created_at

        # 确保订单状态正确
        if active_order.status in [OrderStatus.PENDING_START, OrderStatus.CONFIRMED, OrderStatus.RESERVED]:
            active_order.status = OrderStatus.SERVING

        db.commit()

        print(f"🔧 修复用餐开始时间: {room_number}")
        print(f"   原时间: {old_dining_start_time}")
        print(f"   新时间: {active_order.dining_start_time}")
        print(f"   订单状态: {active_order.status}")

        return {
            "success": True,
            "message": f"已修复 {room_number} 包厢的用餐开始时间",
            "old_time": old_dining_start_time.isoformat() if old_dining_start_time else None,
            "new_time": active_order.dining_start_time.isoformat(),
            "order_status": active_order.status.value
        }

    except Exception as e:
        db.rollback()
        print(f"❌ 修复用餐开始时间失败: {e}")
        raise HTTPException(status_code=500, detail=f"修复失败: {str(e)}")

# 结束用餐API
@app.post("/waiter/end-dining")
async def end_dining(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以结束用餐")

    if not current_user.is_authorized:
        raise HTTPException(status_code=403, detail="您还未被授权")

    try:
        # 获取请求数据
        data = await request.json()
        room_number = data.get('room_number')

        if not room_number:
            return {"success": False, "message": "包厢号不能为空"}

        # 检查该包厢是否在服务员的分配列表中
        if current_user.assigned_tables:
            assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
            if room_number not in assigned_rooms:
                raise HTTPException(status_code=403, detail="您没有权限操作此包厢")

        # 查找该包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            return {"success": False, "message": "包厢不存在"}

        # 查找该包厢的活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING,
                OrderStatus.CONFIRMED,
                OrderStatus.IN_PROGRESS
            ])
        ).first()

        if not active_order:
            return {"success": False, "message": "该包厢暂无活跃订单"}

        # 检查是否所有菜品都已上齐 - 修复版本，只检查未划菜的菜品
        from models.order import DishItemStatus

        # 查找所有未划菜的菜品（包括待制作、制作中、已完成但未划菜）
        unserved_items = db.query(OrderItem).filter(
            OrderItem.order_id == active_order.id,
            OrderItem.status.in_([
                DishItemStatus.PENDING_COOK,  # 待制作
                DishItemStatus.COOKING,       # 制作中
                DishItemStatus.READY          # 已完成但可能未划菜
            ]),
            # 关键修复：只检查未划菜的菜品
            OrderItem.waiter_status != 'served'  # 排除已划菜的菜品
        ).all()

        if unserved_items:
            # 分类显示未完成的菜品
            pending_cook = [item.dish_name for item in unserved_items if item.status == DishItemStatus.PENDING_COOK]
            cooking = [item.dish_name for item in unserved_items if item.status == DishItemStatus.COOKING]
            ready_not_served = [item.dish_name for item in unserved_items if item.status == DishItemStatus.READY]

            message_parts = [f"包厢 {room_number} 还有 {len(unserved_items)} 道菜品未上齐，请确认所有菜品都已上桌后再结束用餐"]

            if pending_cook:
                message_parts.append(f"\n待制作菜品：{', '.join(pending_cook)}")
            if cooking:
                message_parts.append(f"\n制作中菜品：{', '.join(cooking)}")
            if ready_not_served:
                message_parts.append(f"\n已完成但未划菜：{', '.join(ready_not_served)}")

            return {
                "success": False,
                "message": "\n".join(message_parts)
            }

        # 1. 更新订单状态为已完成
        active_order.status = OrderStatus.COMPLETED
        active_order.completed_at = get_china_time()
        active_order.dining_end_time = get_china_time()  # 设置用餐结束时间
        active_order.waiter_id = None  # 清除服务员关联

        # 2. 更新餐桌状态为空闲
        table.status = TableStatus.AVAILABLE
        table.current_order_id = None
        table.current_guests = 0
        table.assigned_waiter_id = None  # 清除餐桌的服务员分配

        # 3. 从服务员的分配列表中移除该包厢（支持多包厢独立管理）
        remaining_rooms = []
        if current_user.assigned_tables:
            assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
            remaining_rooms = [room for room in assigned_rooms if room != room_number]
            current_user.assigned_tables = ','.join(remaining_rooms) if remaining_rooms else None

            print(f"📋 服务员 {current_user.full_name} 包厢分配更新:")
            print(f"   原分配: {assigned_rooms}")
            print(f"   移除包厢: {room_number}")
            print(f"   剩余分配: {remaining_rooms}")

            # 如果没有分配的包厢了，取消授权
            if not remaining_rooms:
                current_user.is_authorized = False
                current_user.authorized_by = None
                print(f"🔐 服务员 {current_user.full_name} 完成所有包厢服务，自动取消授权")
            else:
                print(f"✅ 服务员 {current_user.full_name} 还有其他包厢分配，保持授权状态")

        # 确保只更新当前服务员的状态
        db.flush()  # 立即同步到数据库，但不提交事务

        # 4. 清除所有相关的用餐开始记录
        try:
            from models.waiter_action import WaiterAction
            # 删除该包厢的用餐开始记录，允许重新开始用餐
            db.query(WaiterAction).filter(
                WaiterAction.room_number == room_number,
                WaiterAction.action_type == 'dining_start'
            ).delete()
        except ImportError:
            # 如果WaiterAction模型不存在，跳过
            pass

        # 5. 更新所有订单项状态为已完成
        db.query(OrderItem).filter(
            OrderItem.order_id == active_order.id,
            OrderItem.status.in_([DishItemStatus.PENDING_COOK, DishItemStatus.COOKING, DishItemStatus.READY])
        ).update({
            'status': DishItemStatus.READY,  # 使用枚举值，保持为已完成状态
            'served_at': get_china_time()
        })

        # 记录结束用餐操作
        try:
            from models.waiter_action import WaiterAction
            end_action = WaiterAction(
                waiter_id=current_user.id,
                room_number=room_number,
                action_type='dining_end',
                action_content='用餐结束',  # 修复：移除订单号，避免语音播报不必要信息
                created_at=get_china_time()
            )
            db.add(end_action)
        except ImportError:
            # 如果WaiterAction模型不存在，跳过记录
            pass

        db.commit()

        # 检查服务员是否还有其他包厢分配
        should_logout = len(remaining_rooms) == 0

        print(f"🏁 结束用餐成功: {current_user.full_name} -> {room_number}")
        print(f"   剩余包厢: {remaining_rooms}")
        print(f"   需要退出登录: {should_logout}")

        # 广播结束用餐消息，触发厨房大屏刷新
        try:
            await websocket_manager.broadcast_dining_ended(
                room_number=room_number,
                waiter_name=current_user.full_name
            )
            print(f"📡 结束用餐WebSocket通知已发送")
        except Exception as e:
            print(f"❌ 结束用餐WebSocket通知发送失败: {e}")

        return {
            "success": True,
            "message": f"{room_number}包厢用餐服务已结束，餐桌已变为空闲状态",
            "should_logout": should_logout,  # 告诉前端是否需要退出登录
            "logout_message": "您已完成所有包厢的服务，系统将自动退出登录" if should_logout else None
        }

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        return {
            "success": False,
            "message": f"结束用餐失败: {str(e)}"
        }


# 确认收菜
@app.post("/waiter/confirm-receive/{item_id}")
async def confirm_receive_dish(
    item_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以确认收菜")

    try:
        order_item = db.query(OrderItem).filter(OrderItem.id == item_id).first()
        if not order_item:
            raise HTTPException(status_code=404, detail="菜品不存在")

        # 更新状态为已收菜（准备上菜）
        order_item.status = DishItemStatus.READY
        order_item.served_at = get_china_time()

        db.commit()

        return {"success": True, "message": "确认收菜成功"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 发送菜品相关指令
@app.post("/waiter/dish-command")
async def send_dish_command(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以发送指令")

    try:
        data = await request.json()
        item_id = data.get('item_id')
        action_type = data.get('action_type')
        action_content = data.get('action_content', '')

        # 获取菜品信息
        order_item = db.query(OrderItem).join(Order).filter(OrderItem.id == item_id).first()
        if not order_item:
            raise HTTPException(status_code=404, detail="菜品不存在")

        room_number = order_item.order.table.number if order_item.order.table else "外带"

        # 保存指令到数据库
        from models.waiter_action import WaiterAction
        from datetime import datetime

        waiter_action = WaiterAction(
            waiter_id=current_user.id,
            room_number=room_number,
            action_type=action_type,
            action_content=f"{order_item.dish_name}: {action_content}",
            is_processed=False,
            created_at=get_china_time()
        )

        db.add(waiter_action)
        db.commit()

        return {"success": True, "message": "指令发送成功"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 指令模板管理页面
@app.get("/command-templates", response_class=HTMLResponse)
async def command_templates_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    # 获取所有指令模板
    commands = []
    try:
        from models.command_template import CommandTemplate
        commands = db.query(CommandTemplate).order_by(CommandTemplate.sort_order, CommandTemplate.name).all()
    except Exception as e:
        print(f"⚠️ 指令模板表不存在: {e}")
        # 返回空列表，不影响页面显示
        commands = []

    return templates.TemplateResponse(
        "command_templates.html",
        {
            "request": request,
            "user": current_user,
            "commands": commands
        }
    )


# 创建指令模板
@app.post("/command-templates")
async def create_command_template(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        data = await request.json()

        try:
            from models.command_template import CommandTemplate

            # 检查代码是否已存在
            existing = db.query(CommandTemplate).filter(CommandTemplate.code == data['code']).first()
            if existing:
                raise HTTPException(status_code=400, detail="指令代码已存在")

            command = CommandTemplate(
                name=data['name'],
                code=data['code'],
                category=data.get('category', 'general'),
                description=data.get('description', ''),
                voice_text=data.get('voice_text', ''),
                is_active=True
            )

            db.add(command)
            db.commit()

            # 处理输入配置
            allow_input = data.get('allow_input', False)
            input_placeholder = data.get('input_placeholder', '')
            input_required = data.get('input_required', False)

            # 保存输入配置到文件
            if allow_input or input_placeholder or input_required:
                update_input_config(data['code'], allow_input, input_placeholder, input_required)
                print(f"✅ 指令模板创建成功: {data['code']} ({data['name']})")
                print(f"   输入设定: 允许输入={allow_input}, 提示='{input_placeholder}', 必须输入={input_required}")

            return {"success": True, "message": "指令创建成功"}
        except Exception as e:
            print(f"⚠️ 指令模板功能不可用: {e}")
            return {"success": False, "message": "指令模板功能暂不可用"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 切换指令状态
@app.post("/command-templates/{command_id}/toggle")
async def toggle_command_template(
    command_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        data = await request.json()

        from models.command_template import CommandTemplate
        command = db.query(CommandTemplate).filter(CommandTemplate.id == command_id).first()
        if not command:
            raise HTTPException(status_code=404, detail="指令不存在")

        command.is_active = data['is_active']
        db.commit()

        return {"success": True, "message": "状态更新成功"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 删除指令模板
@app.delete("/command-templates/{command_id}")
async def delete_command_template(
    command_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        from models.command_template import CommandTemplate
        command = db.query(CommandTemplate).filter(CommandTemplate.id == command_id).first()
        if not command:
            raise HTTPException(status_code=404, detail="指令不存在")

        db.delete(command)
        db.commit()

        return {"success": True, "message": "指令删除成功"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 创建订单页面
@app.get("/orders/create", response_class=HTMLResponse)
async def create_order_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 获取可用餐桌
    available_tables = db.query(Table).filter(
        Table.is_active == True,
        Table.status == TableStatus.AVAILABLE
    ).order_by(Table.number).all()

    # 获取可用菜品
    available_dishes = db.query(Dish).filter(
        Dish.is_active == True,
        Dish.status == DishStatus.AVAILABLE
    ).order_by(Dish.category, Dish.name).all()

    return templates.TemplateResponse(
        "create_order_simple.html",
        {
            "request": request,
            "user": current_user,
            "tables": available_tables,
            "dishes": available_dishes
        }
    )


# 处理创建订单
@app.post("/orders/create")
async def create_order(
    request: Request,
    table_id: int = Form(...),
    customer_name: str = Form(""),
    guest_count: int = Form(1),
    dining_standard: float = Form(...),
    menu_content: str = Form(""),
    special_requests: str = Form(""),
    meal_period: str = Form("dinner"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # 获取餐桌信息
        table = db.query(Table).filter(Table.id == table_id).first()
        if not table:
            raise HTTPException(status_code=404, detail="餐桌不存在")

        # 检查餐桌是否已有活跃订单（防止重复提交）
        existing_order = db.query(Order).filter(
            Order.table_id == table_id,
            Order.status.in_([
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING,
                OrderStatus.CONFIRMED,
                OrderStatus.IN_PROGRESS
            ])
        ).first()

        if existing_order:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "message": f"包厢 {table.number} 已有活跃订单（订单号: {existing_order.order_number}），请勿重复提交"
                },
                headers={"Content-Type": "application/json; charset=utf-8"}
            )

        # 解析菜单内容
        dishes = []
        if menu_content.strip():
            for line in menu_content.strip().split('\n'):
                dish_name = line.strip()
                if dish_name:
                    dishes.append(dish_name)

        if not dishes:
            raise HTTPException(status_code=400, detail="请输入菜单内容")

        # 检查菜品数量限制
        MAX_DISHES_PER_ORDER = 50  # 设置最大菜品数量限制
        if len(dishes) > MAX_DISHES_PER_ORDER:
            raise HTTPException(
                status_code=400,
                detail=f"菜品数量超出限制，最多允许{MAX_DISHES_PER_ORDER}个菜品，当前有{len(dishes)}个菜品。请减少菜品数量后重新提交。"
            )

        # 检查菜单重复
        dish_names = []
        duplicate_dishes = []

        for dish_name in dishes:
            if dish_name in dish_names:
                if dish_name not in duplicate_dishes:
                    duplicate_dishes.append(dish_name)
            else:
                dish_names.append(dish_name)

        if duplicate_dishes:
            raise HTTPException(
                status_code=400,
                detail=f"订单中包含重复菜品：{', '.join(duplicate_dishes)}。请检查并修改后重新提交。"
            )

        # 创建订单
        from datetime import datetime
        import uuid

        order_number = f"JY{get_china_time().strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4())[:4].upper()}"

        # 用餐标准就是整桌金额，不需要乘以人数
        total_amount = dining_standard

        # 验证用餐时段
        from models.order import MealPeriod
        try:
            meal_period_enum = MealPeriod(meal_period)
        except ValueError:
            meal_period_enum = MealPeriod.DINNER  # 默认晚餐

        china_time = get_china_time()
        order = Order(
            order_number=order_number,
            table_id=table_id,
            waiter_id=None,  # 商务中心创建订单时不设置服务员，等待经理授权
            customer_name=customer_name,
            guest_count=guest_count,
            special_requests=special_requests,
            subtotal=total_amount,
            total_amount=total_amount,
            dining_standard_amount=dining_standard,  # 保存餐标金额
            meal_period=meal_period_enum,  # 添加用餐时段
            status=OrderStatus.RESERVED,  # 商务中心创建订单后为已预订状态
            ordered_at=china_time,
            created_at=china_time  # 手动设置创建时间为中国时区
        )

        db.add(order)
        db.flush()  # 获取订单ID

        # 添加订单项（从菜单文本解析）
        for i, dish_name in enumerate(dishes):
            order_item = OrderItem(
                order_id=order.id,
                dish_id=None,  # 不关联具体菜品
                dish_name=dish_name,
                dish_price=0,  # 暂时不设置价格
                quantity=1,
                unit_price=0,
                total_price=0,
                status=DishItemStatus.PENDING_COOK,  # 设置菜品状态为待制作
                ordered_at=china_time,
                created_at=china_time  # 手动设置创建时间为中国时区
            )
            db.add(order_item)

        # 保持用餐标准金额
        order.subtotal = dining_standard
        order.total_amount = dining_standard

        # 更新餐桌状态 - 商务中心开包厢后为预订状态，等待餐饮经理授权
        table.status = TableStatus.RESERVED
        table.current_guests = guest_count
        table.assigned_waiter_id = None  # 清除之前的授权，需要重新授权

        db.commit()

        # 构建详细的成功消息
        meal_period_display = {
            'breakfast': '早餐',
            'lunch': '午餐',
            'dinner': '晚餐'
        }.get(meal_period, '晚餐')

        detailed_message = f"{table.number}包厢，{guest_count}人，{meal_period_display}订单创建成功"

        return JSONResponse(
            content={
                "success": True,
                "message": detailed_message,
                "order_number": order_number,
                "table_number": table.number,
                "guest_count": guest_count,
                "meal_period": meal_period_display,
                "total_amount": float(total_amount)
            },
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    except HTTPException as he:
        # 重新抛出HTTPException，保持原有的状态码和错误信息
        db.rollback()
        raise he
    except Exception as e:
        db.rollback()
        import traceback
        error_details = traceback.format_exc()
        print(f"❌ 创建订单失败: {e}")
        print(f"❌ 详细错误信息: {error_details}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"创建订单失败: {str(e)}"},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )


# 编辑订单页面
@app.get("/orders/{order_id}/edit", response_class=HTMLResponse)
async def edit_order_page(
    order_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 权限检查：厨师长、餐饮经理、商务中心具有编辑订单权限
    if not (current_user.role in [UserRole.KITCHEN_HELPER, UserRole.MANAGER, UserRole.BUSINESS_CENTER] or
            current_user.has_permission('order.manage')):
        raise HTTPException(status_code=403, detail="权限不足，只有厨房、餐饮经理、商务中心可以编辑订单")

    try:
        # 获取订单信息
        from sqlalchemy.orm import joinedload
        order = db.query(Order).options(
            joinedload(Order.order_items),
            joinedload(Order.table),
            joinedload(Order.waiter)
        ).filter(Order.id == order_id).first()

        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")

        # 检查订单是否可编辑
        # 厨师长可以编辑已开始用餐的订单
        if not order.is_editable and not current_user.has_permission("order.edit_after_start"):
            raise HTTPException(status_code=400, detail="订单状态不允许编辑")

        # 获取可用餐桌
        available_tables = db.query(Table).filter(Table.is_active == True).all()

        return templates.TemplateResponse(
            "edit_order.html",
            {
                "request": request,
                "user": current_user,
                "order": order,
                "tables": available_tables
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单信息失败: {str(e)}")


# 处理编辑订单
@app.post("/orders/{order_id}/edit")
async def update_order(
    order_id: int,
    request: Request,
    customer_name: str = Form(""),
    guest_count: int = Form(1),
    dining_standard: float = Form(...),
    special_requests: str = Form(""),
    menu_content: str = Form(""),
    meal_period: str = Form("dinner"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 权限检查
    if not (current_user.role in [UserRole.KITCHEN_HELPER, UserRole.MANAGER, UserRole.BUSINESS_CENTER] or
            current_user.has_permission('order.manage')):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 获取订单
        order = db.query(Order).filter(Order.id == order_id).first()
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")

        # 检查订单是否可编辑
        # 厨师长可以编辑已开始用餐的订单
        if not order.is_editable and not current_user.has_permission("order.edit_after_start"):
            raise HTTPException(status_code=400, detail="订单状态不允许编辑")

        # 记录修改历史
        from datetime import datetime
        modification_log = f"[{get_china_time().strftime('%Y-%m-%d %H:%M:%S')}] {current_user.full_name} 修改订单"

        # 更新订单基本信息
        old_customer_name = order.customer_name
        old_guest_count = order.guest_count
        old_dining_standard = order.dining_standard_amount
        old_special_requests = order.special_requests
        old_meal_period = order.meal_period

        # 验证用餐时段
        from models.order import MealPeriod
        try:
            meal_period_enum = MealPeriod(meal_period)
        except ValueError:
            meal_period_enum = MealPeriod.DINNER  # 默认晚餐

        order.customer_name = customer_name
        order.guest_count = guest_count
        order.dining_standard_amount = dining_standard
        order.subtotal = dining_standard
        order.total_amount = dining_standard
        order.special_requests = special_requests
        order.meal_period = meal_period_enum
        order.updated_at = get_china_time()

        # 记录具体修改内容
        changes = []
        if old_customer_name != customer_name:
            changes.append(f"客户名称: {old_customer_name} → {customer_name}")
        if old_guest_count != guest_count:
            changes.append(f"用餐人数: {old_guest_count} → {guest_count}")
        if old_dining_standard != dining_standard:
            changes.append(f"用餐标准: {old_dining_standard} → {dining_standard}")
        if old_special_requests != special_requests:
            changes.append(f"特殊要求: {old_special_requests} → {special_requests}")
        if old_meal_period != meal_period_enum:
            old_period_display = old_meal_period.value if old_meal_period else '未设置'
            new_period_display = meal_period_enum.value
            changes.append(f"用餐时段: {old_period_display} → {new_period_display}")

        # 处理菜品修改
        if menu_content.strip():
            # 解析新的菜品列表
            new_dishes = [dish.strip() for dish in menu_content.strip().split('\n') if dish.strip()]

            # 检查菜品数量限制
            MAX_DISHES_PER_ORDER = 50  # 设置最大菜品数量限制
            if len(new_dishes) > MAX_DISHES_PER_ORDER:
                raise HTTPException(
                    status_code=400,
                    detail=f"菜品数量超出限制，最多允许{MAX_DISHES_PER_ORDER}个菜品，当前有{len(new_dishes)}个菜品。请减少菜品数量后重新提交。"
                )

            # 检查重复菜品
            dish_names_check = []
            duplicate_dishes_check = []
            for dish_name in new_dishes:
                if dish_name in dish_names_check:
                    if dish_name not in duplicate_dishes_check:
                        duplicate_dishes_check.append(dish_name)
                else:
                    dish_names_check.append(dish_name)

            if duplicate_dishes_check:
                raise HTTPException(
                    status_code=400,
                    detail=f"菜单中包含重复菜品：{', '.join(duplicate_dishes_check)}。请检查并修改后重新提交。"
                )

            # 获取当前菜品
            current_items = db.query(OrderItem).filter(OrderItem.order_id == order_id).all()
            current_dish_names = [item.dish_name for item in current_items]

            # 找出需要删除的菜品
            dishes_to_remove = [dish for dish in current_dish_names if dish not in new_dishes]
            # 找出需要添加的菜品
            dishes_to_add = [dish for dish in new_dishes if dish not in current_dish_names]

            # 删除不需要的菜品
            for dish_name in dishes_to_remove:
                items_to_remove = db.query(OrderItem).filter(
                    OrderItem.order_id == order_id,
                    OrderItem.dish_name == dish_name
                ).all()
                for item in items_to_remove:
                    db.delete(item)
                changes.append(f"删除菜品: {dish_name}")

            # 添加新菜品
            for dish_name in dishes_to_add:
                china_time = get_china_time()
                new_item = OrderItem(
                    order_id=order.id,
                    dish_id=None,
                    dish_name=dish_name,
                    dish_price=0,
                    quantity=1,
                    unit_price=0,
                    total_price=0,
                    status=DishItemStatus.PENDING_COOK,
                    ordered_at=china_time,
                    created_at=china_time  # 手动设置创建时间为中国时区
                )
                db.add(new_item)
                changes.append(f"添加菜品: {dish_name}")

        # 更新修改记录
        if changes:
            modification_log += f": {'; '.join(changes)}"
            # 这里可以添加到修改历史表，暂时记录到日志
            print(f"📝 {modification_log}")

        db.commit()

        return JSONResponse(
            content={"success": True, "message": "订单更新成功"},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新订单失败: {str(e)}")


# 获取可用服务员列表
@app.get("/api/waiters/available")
async def get_available_waiters(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取可用的服务员列表"""
    if not current_user.has_permission('waiter.authorize'):
        return JSONResponse(
            status_code=403,
            content={"success": False, "message": "权限不足"}
        )

    try:
        # 获取所有活跃的服务员
        waiters = db.query(User).filter(
            User.role == UserRole.WAITER,
            User.is_active == True
        ).all()

        waiter_list = []
        for waiter in waiters:
            waiter_list.append({
                "id": waiter.id,
                "username": waiter.username,
                "full_name": waiter.full_name,
                "is_authorized": waiter.is_authorized,
                "assigned_tables": waiter.assigned_tables
            })

        return JSONResponse(
            content={"success": True, "waiters": waiter_list},
            headers={"Content-Type": "application/json; charset=utf-8"}
        )

    except Exception as e:
        print(f"❌ 获取服务员列表失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"获取服务员列表失败: {str(e)}"}
        )

# 授权服务员（支持多包厢分配）
@app.post("/users/{user_id}/authorize")
async def authorize_waiter(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 获取请求数据
        data = await request.json()
        assigned_tables = data.get('assigned_tables', '')

        # 查找服务员
        waiter = db.query(User).filter(
            User.id == user_id,
            User.role == UserRole.WAITER
        ).first()

        if not waiter:
            raise HTTPException(status_code=404, detail="服务员不存在")

        # 验证包厢列表格式
        if assigned_tables:
            table_list = [table.strip() for table in assigned_tables.split(',') if table.strip()]
            if not table_list:
                raise HTTPException(status_code=400, detail="包厢列表不能为空")

            # 验证包厢是否存在
            for table_number in table_list:
                table = db.query(Table).filter(Table.number == table_number).first()
                if not table:
                    raise HTTPException(status_code=400, detail=f"包厢 {table_number} 不存在")

            # 清理格式化包厢列表
            assigned_tables = ','.join(table_list)

        # 更新授权信息（支持多包厢分配）
        waiter.is_authorized = True
        waiter.authorized_by = current_user.id
        waiter.assigned_tables = assigned_tables

        # 更新相关包厢的服务员分配
        if assigned_tables:
            for table_number in assigned_tables.split(','):
                table_number = table_number.strip()
                table = db.query(Table).filter(Table.number == table_number).first()
                if table:
                    table.assigned_waiter_id = waiter.id
                    print(f"✅ 包厢 {table_number} 分配给服务员 {waiter.full_name}")

        db.commit()

        print(f"✅ 服务员授权成功: {waiter.full_name} -> 包厢: {assigned_tables or '无'}")
        return {"success": True, "message": f"授权成功，已分配包厢: {assigned_tables or '无'}"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 取消授权服务员
@app.post("/users/{user_id}/revoke")
async def revoke_waiter(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        waiter = db.query(User).filter(
            User.id == user_id,
            User.role == UserRole.WAITER
        ).first()

        if not waiter:
            raise HTTPException(status_code=404, detail="服务员不存在")

        waiter.is_authorized = False
        waiter.authorized_by = None
        waiter.assigned_tables = None

        db.commit()

        return {"success": True, "message": "取消授权成功"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 获取单个用户信息API
@app.get("/api/users/{user_id}")
async def get_user_api(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('user.manage'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")

        return {
            "success": True,
            "user": {
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "role": user.role.value,
                "status": user.status.value,
                "is_active": user.is_active,
                "created_at": user.created_at.isoformat() if user.created_at else None
            }
        }
    except Exception as e:
        return {"success": False, "message": str(e)}


# 更新用户信息API
@app.post("/users/{user_id}/update")
async def update_user_api(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('user.manage'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 获取用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return {"success": False, "message": "用户不存在"}

        # 权限检查：餐饮经理无法编辑系统管理员账户
        if current_user.role.value == 'manager' and user.role.value == 'admin':
            return {"success": False, "message": "餐饮经理无权编辑系统管理员账户"}

        # 保护系统管理员账户：不能被降级
        if user.username == 'admin' and current_user.role.value != 'admin':
            return {"success": False, "message": "系统管理员账户受保护，无法修改"}

        # 获取请求数据
        data = await request.json()

        # 检查用户名是否已被其他用户使用
        if data.get('username') and data['username'] != user.username:
            existing_user = db.query(User).filter(
                User.username == data['username'],
                User.id != user_id
            ).first()
            if existing_user:
                return {"success": False, "message": "用户名已被其他用户使用"}

        # 更新用户信息
        if data.get('username'):
            user.username = data['username']
        if data.get('full_name'):
            user.full_name = data['full_name']
        if data.get('role'):
            try:
                new_role = UserRole(data['role'])

                # 权限检查：餐饮经理无法将用户设置为系统管理员
                if current_user.role.value == 'manager' and data['role'] == 'admin':
                    return {"success": False, "message": "餐饮经理无权创建系统管理员账户"}

                # 检查系统管理员唯一性（如果要设置为admin）
                if data['role'] == 'admin' and user.role.value != 'admin':
                    existing_admin = db.query(User).filter(User.role == UserRole.ADMIN).first()
                    if existing_admin:
                        return {"success": False, "message": "系统中只能存在一个系统管理员账户"}

                # 保护现有系统管理员：不能被降级为其他角色
                if user.role.value == 'admin' and data['role'] != 'admin':
                    return {"success": False, "message": "系统管理员账户不能被降级为其他角色"}

                user.role = new_role
            except ValueError:
                return {"success": False, "message": "无效的角色"}
        if data.get('status'):
            try:
                # 处理前端发送的大写状态值
                status_value = data['status'].upper()
                if status_value == 'ACTIVE':
                    user.status = UserStatus.ACTIVE
                    user.is_active = True
                elif status_value == 'INACTIVE':
                    user.status = UserStatus.INACTIVE
                    user.is_active = False
                else:
                    return {"success": False, "message": "无效的状态"}
            except (ValueError, AttributeError):
                return {"success": False, "message": "无效的状态"}

        # 如果提供了新密码，则更新密码
        if data.get('password'):
            from passlib.context import CryptContext
            pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
            user.hashed_password = pwd_context.hash(data['password'])

        db.commit()

        return {"success": True, "message": "用户更新成功"}
    except Exception as e:
        db.rollback()
        return {"success": False, "message": str(e)}


# 删除用户API
@app.post("/users/{user_id}/delete")
async def delete_user_api(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('user.manage'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 获取用户
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            return {"success": False, "message": "用户不存在"}

        # 不能删除自己
        if user.id == current_user.id:
            return {"success": False, "message": "不能删除自己"}

        # 系统管理员账户受保护
        if user.role.value == 'admin':
            return {"success": False, "message": "系统管理员账户受保护，无法删除"}

        # 餐饮经理无法删除系统管理员账户
        if current_user.role.value == 'manager' and user.role.value == 'admin':
            return {"success": False, "message": "餐饮经理无权删除系统管理员账户"}

        # 删除用户
        db.delete(user)
        db.commit()

        print(f"✅ 用户删除成功: {user.username} (ID: {user.id})")
        return {"success": True, "message": f"用户 {user.username} 删除成功"}
    except Exception as e:
        db.rollback()
        print(f"❌ 删除用户失败: {e}")
        return {"success": False, "message": str(e)}


# 菜单录入功能已移除，使用新建订单功能代替


# 传菜上菜页面已移除
@app.get("/serve", response_class=HTMLResponse)
async def serve_page_removed(request: Request):
    return HTMLResponse("<h1>此页面已移除</h1><p>传菜上菜功能已整合到厨房操作界面中</p>", status_code=404)


# 厨房操作页面 - 重定向到新的打荷操作页面
@app.get("/kitchen", response_class=HTMLResponse)
async def kitchen_page_redirect(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """旧的厨房操作页面，重定向到新的打荷操作页面"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    # 重定向到新的打荷操作页面
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/kitchen-helper", status_code=301)


# 旧版厨房操作页面 - 已弃用，保留用于兼容性
@app.get("/kitchen/legacy", response_class=HTMLResponse)
async def kitchen_legacy_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """旧版厨房操作页面 - 已弃用"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    # 获取需要制作的菜品 - 包含所有可能的订单状态
    order_items = db.query(OrderItem).join(Order).filter(
        OrderItem.status.in_(['pending_cook', 'ready']),
        Order.status.in_([
            'reserved', 'pending_start', 'serving', 'confirmed',
            'in_progress', 'pending_kitchen'
        ])
    ).order_by(OrderItem.created_at.asc()).all()

    # 获取未处理的服务员指令
    waiter_actions = []
    try:
        from models.waiter_action import WaiterAction
        waiter_actions = db.query(WaiterAction).filter(
            WaiterAction.is_processed == False
        ).order_by(WaiterAction.created_at.desc()).limit(10).all()
    except Exception as e:
        print(f"⚠️ 获取服务员指令失败: {e}")

    # 获取包厢信息用于新的厨房界面
    orders_by_room = {}

    # 获取所有有订单的餐桌 - 包含商务中心创建的订单
    tables_with_orders = db.query(Table).join(Order).filter(
        Order.status.in_([
            'reserved', 'pending_start', 'serving', 'confirmed',
            'in_progress', 'pending_kitchen'
        ])
    ).distinct().all()

    for table in tables_with_orders:
        room_name = table.number

        # 获取该餐桌的所有订单项
        order_items_for_room = db.query(OrderItem).join(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                'reserved', 'pending_start', 'serving', 'confirmed',
                'in_progress', 'pending_kitchen'
            ])
        ).order_by(OrderItem.created_at.asc()).all()

        if order_items_for_room:
            # 判断是否已上菜（服务员是否点击了上菜）
            is_served = table.status == TableStatus.OCCUPIED

            # 获取包厢详细信息
            room_info = {}
            if order_items_for_room:
                latest_order = order_items_for_room[0].order
                # 优先显示用餐开始时间，如果没有则显示预订时间
                display_time = None
                if latest_order.dining_start_time:
                    display_time = latest_order.dining_start_time.strftime('%H:%M')
                elif latest_order.ordered_at:
                    display_time = latest_order.ordered_at.strftime('%H:%M')

                room_info = {
                    'dining_standard': latest_order.total_amount or 0,
                    'guest_count': latest_order.guest_count or 0,
                    'dining_start_time': display_time,
                    'special_requirements': latest_order.special_requests or '',
                    'has_started': latest_order.dining_start_time is not None
                }

            orders_by_room[room_name] = {
                'dish_items': order_items_for_room,
                'is_served': is_served,
                'can_operate': is_served,
                'room_info': room_info
            }

    # 为相同菜品添加统一颜色标识和布局优化
    def generate_dish_layout_data(orders_by_room):
        """为跨包厢的相同菜品生成统一的颜色标识和布局数据"""
        # 收集所有菜品名称和位置信息
        all_dish_names = set()
        dish_room_mapping = {}  # 菜品名称 -> 包厢列表

        for room_name, room_data in orders_by_room.items():
            if isinstance(room_data, dict) and 'dish_items' in room_data:
                for index, item in enumerate(room_data['dish_items']):
                    dish_name = item.dish_name
                    all_dish_names.add(dish_name)

                    if dish_name not in dish_room_mapping:
                        dish_room_mapping[dish_name] = []

                    dish_room_mapping[dish_name].append({
                        'room_name': room_name,
                        'original_index': index,
                        'item': item
                    })

        # 预定义的颜色方案（适合厨房界面的高对比度颜色）
        colors = [
            '#FF6B6B',  # 红色
            '#4ECDC4',  # 青色
            '#45B7D1',  # 蓝色
            '#96CEB4',  # 绿色
            '#FFEAA7',  # 黄色
            '#DDA0DD',  # 紫色
            '#98D8C8',  # 薄荷绿
            '#F7DC6F',  # 金黄色
            '#BB8FCE',  # 淡紫色
            '#85C1E9',  # 天蓝色
            '#F8C471',  # 橙色
            '#82E0AA',  # 浅绿色
        ]

        # 为菜品分配颜色和布局位置
        dish_color_map = {}
        dish_layout_map = {}  # 菜品名称 -> 统一显示位置
        color_index = 0
        layout_position = 0

        # 按菜品名称排序，确保一致性
        sorted_dish_names = sorted(all_dish_names)

        # 统计每个菜品在多少个包厢中出现
        dish_room_count = {}
        for dish_name, room_list in dish_room_mapping.items():
            dish_room_count[dish_name] = len(set(item['room_name'] for item in room_list))

        # 优先处理出现在多个包厢的菜品
        multi_room_dishes = [dish for dish in sorted_dish_names if dish_room_count.get(dish, 0) > 1]
        single_room_dishes = [dish for dish in sorted_dish_names if dish_room_count.get(dish, 0) == 1]

        # 为多包厢菜品分配特殊颜色和优先位置
        for dish_name in multi_room_dishes:
            dish_color_map[dish_name] = colors[color_index % len(colors)]
            dish_layout_map[dish_name] = layout_position
            color_index += 1
            layout_position += 1

        # 为单包厢菜品分配位置（不分配特殊颜色）
        for dish_name in single_room_dishes:
            dish_layout_map[dish_name] = layout_position
            layout_position += 1

        # 转换dish_room_mapping为可序列化的格式
        serializable_dish_room_mapping = {}
        for dish_name, room_list in dish_room_mapping.items():
            serializable_dish_room_mapping[dish_name] = [
                {
                    'room_name': item['room_name'],
                    'original_index': item['original_index'],
                    'item_id': item['item'].id,
                    'dish_name': item['item'].dish_name
                }
                for item in room_list
            ]

        return dish_color_map, dish_layout_map, serializable_dish_room_mapping

    dish_color_map, dish_layout_map, dish_room_mapping = generate_dish_layout_data(orders_by_room)

    # 重新组织包厢数据，按菜品布局位置排序
    def reorganize_room_dishes(orders_by_room, dish_layout_map):
        """根据菜品布局位置重新排序包厢内的菜品"""
        reorganized_orders = {}

        for room_name, room_data in orders_by_room.items():
            if isinstance(room_data, dict) and 'dish_items' in room_data:
                # 按布局位置排序菜品
                sorted_dishes = sorted(
                    room_data['dish_items'],
                    key=lambda item: dish_layout_map.get(item.dish_name, 999)
                )

                # 创建新的房间数据
                reorganized_orders[room_name] = {
                    'dish_items': sorted_dishes,
                    'is_served': room_data.get('is_served', False),
                    'can_operate': room_data.get('can_operate', True),
                    'room_info': room_data.get('room_info', {})
                }
            else:
                reorganized_orders[room_name] = room_data

        return reorganized_orders

    # 应用菜品布局优化
    orders_by_room = reorganize_room_dishes(orders_by_room, dish_layout_map)

    return templates.TemplateResponse(
        "kitchen.html",
        {
            "request": request,
            "user": current_user,
            "order_items": order_items,
            "waiter_actions": waiter_actions,
            "orders_by_room": orders_by_room,
            "dish_color_map": dish_color_map,
            "dish_layout_map": dish_layout_map,
            "dish_room_mapping": dish_room_mapping,
            "deprecated_notice": "此页面已弃用，请使用新的打荷操作页面"
        }
    )


# 新版厨房操作页面 - 已弃用
@app.get("/kitchen/new", response_class=HTMLResponse)
async def kitchen_new_page_deprecated(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """新版厨房操作页面 - 已弃用，重定向到打荷操作页面"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    # 重定向到新的打荷操作页面
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/kitchen-helper", status_code=301)


# 厨房大屏显示
@app.get("/kitchen/display", response_class=HTMLResponse)
async def kitchen_display(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    # 获取所有有活跃订单的包厢（不依赖餐桌状态）
    orders_by_room = {}

    # 直接查询所有有活跃订单项的餐桌 - 包含商务中心创建的订单
    # 按照用餐开始时间升序排序，未开始用餐的按预订时间排序
    all_active_orders = db.query(Order).filter(
        Order.status.in_([
            OrderStatus.RESERVED,      # 已预订
            OrderStatus.CONFIRMED,     # 已确认
            OrderStatus.PENDING_START, # 等待开始
            OrderStatus.SERVING,       # 用餐中
            OrderStatus.IN_PROGRESS    # 进行中
        ])
    ).order_by(
        Order.dining_start_time.asc().nulls_last(),  # 用餐开始时间升序，空值在后
        Order.ordered_at.asc()  # 预订时间排序
    ).all()

    # 统计包厢状态
    started_rooms_count = 0
    not_started_rooms_count = 0

    # 统计已开始和未开始用餐的包厢数量（修复：基于dining_start_time）
    serving_orders = []
    for order in all_active_orders:
        if order.dining_start_time is not None:
            started_rooms_count += 1
            serving_orders.append(f"{order.table.number if order.table else 'None'}")
        else:
            not_started_rooms_count += 1

    print(f"🔍 详细统计: serving状态的包厢: {serving_orders}")
    print(f"🔍 统计结果: started_rooms_count={started_rooms_count}, not_started_rooms_count={not_started_rooms_count}")

    # 只显示已开始用餐的包厢（有dining_start_time的订单）
    active_orders = []
    for order in all_active_orders:
        # 修复：主要依赖dining_start_time字段，而不是订单状态
        if order.dining_start_time is not None:
            active_orders.append(order)

    print(f"🔧 修复后的厨房大屏过滤: 从 {len(all_active_orders)} 个订单中筛选出 {len(active_orders)} 个已开始用餐的订单")

    print(f"🔍 厨房大屏查询: 找到 {len(all_active_orders)} 个活跃订单")

    # 详细打印所有订单的用餐开始时间
    for order in all_active_orders:
        print(f"  订单 {order.id}: 状态={order.status}, 餐桌={order.table.number if order.table else 'None'}")

    print(f"📊 统计数据: 已开始用餐包厢 {started_rooms_count} 个, 未开始用餐包厢 {not_started_rooms_count} 个")
    print(f"  厨房大屏显示: {len(active_orders)} 个包厢")

    if len(active_orders) > 0:
        print("  ✅ 显示的订单:")
        for order in active_orders:
            print(f"    订单 {order.id}: 状态={order.status}, 餐桌={order.table.number if order.table else 'None'}, 用餐开始时间={order.dining_start_time}")

    # 打印被过滤掉的订单
    filtered_orders = [order for order in all_active_orders if order.dining_start_time is None]
    if len(filtered_orders) > 0:
        print("  ❌ 过滤掉的订单:")
        for order in filtered_orders:
            print(f"    订单 {order.id}: 状态={order.status}, 餐桌={order.table.number if order.table else 'None'}, 用餐开始时间={order.dining_start_time}")

    # 🔧 修复：使用过滤后的active_orders而不是all_active_orders
    for order in active_orders:
        if not order.table:
            continue

        table = order.table
        room_name = table.number

        # 获取该包厢的菜品 - 暂时使用字符串查询避免枚举问题
        order_items = db.query(OrderItem).filter(
            OrderItem.order_id == order.id,
            OrderItem.status.in_(['pending_cook', 'ready'])
        ).order_by(OrderItem.created_at.asc()).all()

        print(f"  包厢 {table.number}: 找到 {len(order_items)} 个菜品项")

        # 如果已经处理过这个包厢，合并菜品
        if room_name in orders_by_room:
            orders_by_room[room_name]['dish_items'].extend(order_items)
            continue

        # 判断是否已上菜（服务员是否点击了上菜）
        is_served = table.status == TableStatus.OCCUPIED

        # 获取用餐开始指令的时间（作为开始用餐时间）
        from models.waiter_action import WaiterAction
        dining_start_action = db.query(WaiterAction).filter(
            WaiterAction.room_number == table.number,
            WaiterAction.action_type == 'dining_start'
        ).order_by(WaiterAction.created_at.desc()).first()

        dining_start_time = None
        if dining_start_action:
            dining_start_time = dining_start_action.created_at.strftime('%H:%M')
        elif order.dining_start_time:
            # 如果没有指令记录，使用订单的用餐开始时间
            dining_start_time = order.dining_start_time.strftime('%H:%M')

        # 获取包厢详细信息
        room_info = {
            'dining_standard': order.dining_standard or 0,  # 使用订单的餐标字段
            'guest_count': order.guest_count or 0,
            'dining_start_time': dining_start_time,  # 使用服务员上菜指令时间
            'special_requirements': order.special_requests or ''
        }

        # 菜品去重：相同菜品只显示一行，不显示份数
        unique_dishes = {}
        for item in order_items:
            dish_name = item.dish_name
            if dish_name not in unique_dishes:
                # 选择状态最靠前的菜品项作为代表
                unique_dishes[dish_name] = item
            else:
                # 如果当前菜品状态更靠前，则替换
                current_status_order = [DishItemStatus.PENDING_COOK.value, DishItemStatus.READY.value]
                current_order = current_status_order.index(item.status.value) if item.status.value in current_status_order else 999
                existing_order = current_status_order.index(unique_dishes[dish_name].status.value) if unique_dishes[dish_name].status.value in current_status_order else 999

                if current_order < existing_order:
                    unique_dishes[dish_name] = item

        # 转换为列表，按创建时间排序
        unique_dish_items = sorted(unique_dishes.values(), key=lambda x: x.created_at or datetime.min)

        orders_by_room[room_name] = {
            'dish_items': unique_dish_items,  # 使用去重后的菜品列表
            'is_served': is_served,
            'can_operate': True,  # 允许操作
            'room_info': room_info
        }

    # 按包厢号排序
    orders_by_room = dict(sorted(orders_by_room.items(), key=lambda x: x[0]))

    # 为相同菜品添加统一颜色标识和布局优化（厨房大屏版本）
    def generate_dish_layout_data_display(orders_by_room):
        """为跨包厢的相同菜品生成统一的颜色标识和布局数据（厨房大屏版本）"""
        # 收集所有菜品名称和位置信息
        all_dish_names = set()
        dish_room_mapping = {}  # 菜品名称 -> 包厢列表

        for room_name, room_data in orders_by_room.items():
            if isinstance(room_data, dict) and 'dish_items' in room_data:
                for index, item in enumerate(room_data['dish_items']):
                    dish_name = item.dish_name
                    all_dish_names.add(dish_name)

                    if dish_name not in dish_room_mapping:
                        dish_room_mapping[dish_name] = []

                    dish_room_mapping[dish_name].append({
                        'room_name': room_name,
                        'original_index': index,
                        'item': item
                    })

        # 预定义的颜色方案（适合厨房大屏的高对比度颜色）
        colors = [
            '#FF6B6B',  # 红色
            '#4ECDC4',  # 青色
            '#45B7D1',  # 蓝色
            '#96CEB4',  # 绿色
            '#FFEAA7',  # 黄色
            '#DDA0DD',  # 紫色
            '#98D8C8',  # 薄荷绿
            '#F7DC6F',  # 金黄色
            '#BB8FCE',  # 淡紫色
            '#85C1E9',  # 天蓝色
            '#F8C471',  # 橙色
            '#82E0AA',  # 浅绿色
        ]

        # 为菜品分配颜色和布局位置
        dish_color_map = {}
        dish_layout_map = {}  # 菜品名称 -> 统一显示位置
        color_index = 0
        layout_position = 0

        # 按菜品名称排序，确保一致性
        sorted_dish_names = sorted(all_dish_names)

        # 统计每个菜品在多少个包厢中出现
        dish_room_count = {}
        for dish_name, room_list in dish_room_mapping.items():
            dish_room_count[dish_name] = len(set(item['room_name'] for item in room_list))

        # 优先处理出现在多个包厢的菜品
        multi_room_dishes = [dish for dish in sorted_dish_names if dish_room_count.get(dish, 0) > 1]
        single_room_dishes = [dish for dish in sorted_dish_names if dish_room_count.get(dish, 0) == 1]

        # 为多包厢菜品分配特殊颜色和优先位置
        for dish_name in multi_room_dishes:
            dish_color_map[dish_name] = colors[color_index % len(colors)]
            dish_layout_map[dish_name] = layout_position
            color_index += 1
            layout_position += 1

        # 为单包厢菜品分配位置（不分配特殊颜色）
        for dish_name in single_room_dishes:
            dish_layout_map[dish_name] = layout_position
            layout_position += 1

        # 转换dish_room_mapping为可序列化的格式
        serializable_dish_room_mapping = {}
        for dish_name, room_list in dish_room_mapping.items():
            serializable_dish_room_mapping[dish_name] = [
                {
                    'room_name': item['room_name'],
                    'original_index': item['original_index'],
                    'item_id': item['item'].id,
                    'dish_name': item['item'].dish_name
                }
                for item in room_list
            ]

        return dish_color_map, dish_layout_map, serializable_dish_room_mapping

    dish_color_map, dish_layout_map, dish_room_mapping = generate_dish_layout_data_display(orders_by_room)

    # 重新组织包厢数据，按菜品布局位置排序（厨房大屏版本）
    def reorganize_room_dishes_display(orders_by_room, dish_layout_map):
        """根据菜品布局位置重新排序包厢内的菜品（厨房大屏版本）"""
        reorganized_orders = {}

        for room_name, room_data in orders_by_room.items():
            if isinstance(room_data, dict) and 'dish_items' in room_data:
                # 按布局位置排序菜品
                sorted_dishes = sorted(
                    room_data['dish_items'],
                    key=lambda item: dish_layout_map.get(item.dish_name, 999)
                )

                # 创建新的房间数据
                reorganized_orders[room_name] = {
                    'dish_items': sorted_dishes,
                    'is_served': room_data.get('is_served', False),
                    'can_operate': room_data.get('can_operate', True),
                    'room_info': room_data.get('room_info', {})
                }
            else:
                reorganized_orders[room_name] = room_data

        return reorganized_orders

    # 应用菜品布局优化
    orders_by_room = reorganize_room_dishes_display(orders_by_room, dish_layout_map)

    # 获取未处理的指令
    pending_commands = []
    try:
        from models.waiter_action import WaiterAction
        pending_commands = db.query(WaiterAction).filter(
            WaiterAction.is_processed == False
        ).order_by(WaiterAction.created_at.desc()).limit(10).all()
    except Exception:
        pass

    return templates.TemplateResponse(
        "kitchen_display.html",
        {
            "request": request,
            "user": current_user,
            "orders_by_room": orders_by_room,
            "pending_commands": pending_commands,
            "dish_color_map": dish_color_map,
            "dish_layout_map": dish_layout_map,
            "dish_room_mapping": dish_room_mapping,
            "current_time": get_china_time(),
            "started_rooms_count": started_rooms_count,
            "not_started_rooms_count": not_started_rooms_count
        }
    )





# 更新菜品制作状态
@app.post("/kitchen/items/{item_id}/status")
async def update_item_status(
    item_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not (current_user.has_permission('dish.cook') or
            current_user.has_permission('dish.prepare') or
            current_user.has_permission('dish.serve')):
        raise HTTPException(status_code=403, detail="权限不足")

    # 获取请求数据
    data = await request.json()
    new_status = data.get('status')

    # 查找菜品项
    item = db.query(OrderItem).filter(OrderItem.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="菜品不存在")

    # 验证用餐状态 - 只有已开始用餐的包厢才允许厨房操作
    if new_status == DishItemStatus.READY.value:  # 制作完成操作需要验证用餐状态
        dining_started = (
            item.order.dining_start_time is not None or
            item.order.status in [OrderStatus.SERVING, OrderStatus.IN_PROGRESS]
        )

        if not dining_started:
            raise HTTPException(
                status_code=400,
                detail=f"包厢 {item.order.table.number if item.order.table else '外带'} 尚未开始用餐，无法执行制作完成操作"
            )

    # 获取包厢信息
    room_number = "未知包厢"
    if item.order and item.order.table:
        room_number = item.order.table.number

    # 检查包厢是否已开始用餐
    dining_started = False
    if item.order:
        # 检查订单状态或用餐开始时间
        if (item.order.dining_start_time or
            item.order.status in [OrderStatus.SERVING, OrderStatus.IN_PROGRESS]):
            dining_started = True
        else:
            # 检查是否有用餐开始指令
            from models.waiter_action import WaiterAction
            dining_action = db.query(WaiterAction).filter(
                WaiterAction.room_number == room_number,
                WaiterAction.action_type == 'dining_start'
            ).first()
            if dining_action:
                dining_started = True

    # 如果包厢未开始用餐，拒绝操作
    if not dining_started:
        raise HTTPException(
            status_code=400,
            detail=f"{room_number}包厢尚未开始用餐，请等待服务员发送'用餐开始'指令后再进行菜品操作"
        )

    # 更新状态
    try:
        if new_status == DishItemStatus.READY.value:
            item.status = DishItemStatus.READY
            # 厨房大屏语音播报：XXX包厢XXX菜品，跑菜
            voice_message = f"{room_number}{item.dish_name}，跑菜"
            print(f"🔊 语音播报: {voice_message}")
            # 记录制作完成时间
            item.ready_at = get_china_time()

        item.updated_at = get_china_time()
        db.commit()

        # 发送WebSocket通知（当菜品制作完成时）
        if new_status == DishItemStatus.READY.value:
            try:
                await websocket_manager.broadcast_dish_ready(
                    dish_id=item_id,
                    room_number=room_number,
                    dish_name=item.dish_name
                )
                print(f"📡 WebSocket通知已发送: 包厢{room_number}的{item.dish_name}制作完成")
            except Exception as e:
                print(f"❌ WebSocket通知发送失败: {e}")

        # 返回语音播报信息和配置（集成播报控制）
        voice_message = ""
        voice_config = {}
        broadcast_info = {}

        if new_status == DishItemStatus.READY.value:
            voice_message = f"{room_number}{item.dish_name}，跑菜"

            # 使用语音播报管理器
            try:
                from voice_broadcast_manager import get_broadcast_manager
                broadcast_manager = get_broadcast_manager(db)

                # 获取语音配置
                voice_config = broadcast_manager.get_voice_config()

                # 创建或获取播报记录
                # 注意：这里需要关联到相应的WaiterAction，如果没有则创建虚拟记录
                # 为了简化，我们先返回配置，让前端处理播报控制
                broadcast_info = {
                    'can_broadcast': voice_config.get('enabled', True),
                    'max_count': voice_config.get('repeat_count', 2),
                    'interval': voice_config.get('repeat_interval', 3)
                }

            except Exception as e:
                print(f"❌ 获取语音播报配置失败: {e}")
                # 使用默认配置
                voice_config = {
                    'enabled': True,
                    'repeat_count': 2,
                    'repeat_interval': 3,
                    'rate': 0.8,
                    'volume': 1.0,
                    'pitch': 1.0
                }
                broadcast_info = {
                    'can_broadcast': True,
                    'max_count': 2,
                    'interval': 3
                }

        return {
            "success": True,
            "voice_message": voice_message,
            "voice_config": voice_config,
            "broadcast_info": broadcast_info,
            "room_number": room_number,
            "dish_name": item.dish_name
        }
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 厨房退菜功能
@app.post("/kitchen/items/{item_id}/return")
async def return_dish(
    item_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not (current_user.has_permission('dish.cook') or
            current_user.has_permission('dish.prepare')):
        raise HTTPException(status_code=403, detail="权限不足，只有厨房打荷人员可以退菜")

    # 获取请求数据
    data = await request.json()
    reason = data.get('reason', '').strip()

    if not reason:
        raise HTTPException(status_code=400, detail="请输入退菜原因")

    # 查找菜品项
    item = db.query(OrderItem).filter(OrderItem.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="菜品不存在")

    # 获取包厢信息
    room_number = "未知包厢"
    if item.order and item.order.table:
        room_number = item.order.table.number

    # 检查包厢是否已开始用餐
    dining_started = False
    if item.order:
        # 检查订单状态或用餐开始时间
        if (item.order.dining_start_time or
            item.order.status in [OrderStatus.SERVING, OrderStatus.IN_PROGRESS]):
            dining_started = True
        else:
            # 检查是否有用餐开始指令
            from models.waiter_action import WaiterAction
            dining_action = db.query(WaiterAction).filter(
                WaiterAction.room_number == room_number,
                WaiterAction.action_type == 'dining_start'
            ).first()
            if dining_action:
                dining_started = True

    # 如果包厢未开始用餐，拒绝退菜操作
    if not dining_started:
        raise HTTPException(
            status_code=400,
            detail=f"{room_number}包厢尚未开始用餐，请等待服务员发送'用餐开始'指令后再进行退菜操作"
        )

    try:
        # 记录退菜日志
        print(f"🚫 退菜操作: {current_user.full_name} -> {room_number} -> {item.dish_name} (原因: {reason})")

        # 删除菜品项（退菜）
        db.delete(item)
        db.commit()

        return {
            "success": True,
            "message": f"{room_number} {item.dish_name} 已退菜，原因：{reason}"
        }
    except Exception as e:
        db.rollback()
        print(f"❌ 退菜失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 厨房撤销菜品完成状态
@app.post("/kitchen/items/{item_id}/undo")
async def undo_item_status(
    item_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not (current_user.has_permission('dish.cook') or
            current_user.has_permission('dish.prepare')):
        raise HTTPException(status_code=403, detail="权限不足，只有厨房工作人员可以撤销菜品状态")

    # 查找菜品项
    item = db.query(OrderItem).filter(OrderItem.id == item_id).first()
    if not item:
        raise HTTPException(status_code=404, detail="菜品不存在")

    # 检查菜品状态
    if item.status != DishItemStatus.READY:
        raise HTTPException(status_code=400, detail="只能撤销已完成的菜品状态")

    # 获取包厢信息
    room_number = "未知包厢"
    if item.order and item.order.table:
        room_number = item.order.table.number

    try:
        # 记录撤销日志
        print(f"↩️ 撤销菜品完成状态: {current_user.full_name} -> {room_number} -> {item.dish_name}")

        # 将菜品状态从已完成改为待制作
        item.update_status(DishItemStatus.PENDING_COOK)

        # 清除制作完成时间
        item.ready_at = None

        db.commit()

        return {
            "success": True,
            "message": f"{room_number} {item.dish_name} 已撤销完成状态，重新变为待制作"
        }
    except Exception as e:
        db.rollback()
        print(f"❌ 撤销菜品状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 厨房处理指令
@app.post("/kitchen/process-command/{command_id}")
async def kitchen_process_command(
    command_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        from models.waiter_action import WaiterAction
        command = db.query(WaiterAction).filter(WaiterAction.id == command_id).first()
        if command:
            command.is_processed = True
            command.processed_by = current_user.id
            command.processed_at = get_china_time()
            db.commit()
            print(f"✅ 指令已处理: {command.action_type} - {command.action_content}")

        return {"success": True, "message": "指令已处理"}
    except Exception as e:
        print(f"❌ 处理指令失败: {e}")
        return {"success": False, "message": "处理失败"}


# 检查是否所有菜品已上齐
@app.get("/waiter/check-all-served/{room_number}")
async def check_all_served(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以检查")

    try:
        # 查找该包厢的所有订单项
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            return {"all_served": False, "message": "包厢不存在"}

        # 获取当前活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_(['confirmed', 'in_progress'])
        ).first()

        if not active_order:
            return {"all_served": True, "message": "没有活跃订单"}

        # 检查所有菜品是否已制作完成
        unfinished_items = db.query(OrderItem).filter(
            OrderItem.order_id == active_order.id,
            OrderItem.status != DishItemStatus.READY
        ).count()

        return {
            "all_served": unfinished_items == 0,
            "unserved_count": unfinished_items,
            "message": "所有菜品已制作完成" if unfinished_items == 0 else f"还有{unfinished_items}道菜未制作完成"
        }

    except Exception as e:
        return {"all_served": False, "message": f"检查失败: {str(e)}"}


# 检查是否有新的菜品完成（用于服务员页面自动刷新）- 优化版本
@app.get("/api/check-dish-ready/{room_number}")
async def check_dish_ready(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """检查指定包厢是否有新的菜品完成（优化版本）"""
    try:
        # 只允许服务员角色调用此API
        if current_user.role != UserRole.WAITER:
            return {"has_new_ready_dishes": False, "message": "权限不足"}

        # 检查服务员是否被分配到该包厢
        if not current_user.assigned_tables or room_number not in current_user.assigned_tables.split(','):
            return {"has_new_ready_dishes": False, "message": "未分配到该包厢"}

        # 使用更高效的查询：直接联表查询
        from sqlalchemy import and_, func
        from datetime import timedelta

        # 检查最近2分钟内是否有菜品完成（增加时间窗口减少遗漏）
        two_minutes_ago = get_china_time() - timedelta(minutes=2)

        # 优化查询：使用EXISTS子查询
        recent_ready_count = db.query(func.count(OrderItem.id)).join(Order).join(Table).filter(
            and_(
                Table.number == room_number,
                Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.IN_PROGRESS, OrderStatus.SERVING]),
                OrderItem.status == DishItemStatus.READY,
                OrderItem.ready_at >= two_minutes_ago
            )
        ).scalar()

        # 如果有新菜品完成，同时返回总的待处理菜品数
        total_pending = 0
        if recent_ready_count > 0:
            total_pending = db.query(func.count(OrderItem.id)).join(Order).join(Table).filter(
                and_(
                    Table.number == room_number,
                    Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.IN_PROGRESS, OrderStatus.SERVING]),
                    OrderItem.status == DishItemStatus.READY,
                    OrderItem.waiter_status.is_(None)  # 未被服务员处理
                )
            ).scalar()

        return {
            "has_new_ready_dishes": recent_ready_count > 0,
            "ready_count": recent_ready_count,
            "total_pending": total_pending,
            "message": f"最近2分钟内有{recent_ready_count}道菜品完成" if recent_ready_count > 0 else "没有新完成的菜品"
        }

    except Exception as e:
        print(f"❌ 检查菜品完成状态失败: {e}")
        return {"has_new_ready_dishes": False, "message": f"检查失败: {str(e)}"}


# 获取最新制作完成的菜品（用于厨房大屏通知）
@app.get("/api/dish-ready/latest")
async def get_latest_ready_dishes(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取最近制作完成的菜品列表（用于厨房大屏显示跑菜指令）"""
    try:
        # 只允许厨房相关角色调用此API
        if current_user.role not in [UserRole.CHEF_MANAGER, UserRole.KITCHEN_HELPER]:
            return {"success": False, "dishes": [], "message": "权限不足"}

        from datetime import timedelta

        # 检查最近2分钟内制作完成的菜品
        two_minutes_ago = get_china_time() - timedelta(minutes=2)

        # 查询最近制作完成的菜品
        ready_dishes = db.query(OrderItem).join(Order).join(Table).filter(
            OrderItem.status == DishItemStatus.READY,
            OrderItem.ready_at >= two_minutes_ago,
            Order.status.in_([
                OrderStatus.CONFIRMED, OrderStatus.IN_PROGRESS,
                OrderStatus.SERVING, OrderStatus.PENDING_START
            ])
        ).order_by(OrderItem.ready_at.desc()).all()

        dishes_data = []
        for item in ready_dishes:
            dishes_data.append({
                'id': item.id,
                'dish_name': item.dish_name,
                'room_number': item.order.table.number if item.order.table else '外带',
                'ready_at': item.ready_at.isoformat() if item.ready_at else None,
                'order_id': item.order_id
            })

        return {
            "success": True,
            "dishes": dishes_data,
            "count": len(dishes_data),
            "message": f"找到{len(dishes_data)}道最近制作完成的菜品"
        }

    except Exception as e:
        print(f"❌ 获取最新制作完成菜品失败: {e}")
        return {"success": False, "dishes": [], "message": f"获取失败: {str(e)}"}


# 结束用餐
@app.post("/waiter/finish-dining/{room_number}")
async def finish_dining(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以结束用餐")

    try:
        # 查找包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            raise HTTPException(status_code=404, detail="包厢不存在")

        # 获取当前活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_(['confirmed', 'in_progress'])
        ).first()

        if active_order:
            # 更新订单状态为已完成
            active_order.status = OrderStatus.COMPLETED
            active_order.completed_at = get_china_time()

        # 更新餐桌状态为空闲，清除授权
        table.status = TableStatus.AVAILABLE
        table.current_guests = 0
        table.assigned_waiter_id = None

        # 清除服务员的包厢分配
        waiter = db.query(User).filter(User.id == current_user.id).first()
        if waiter and waiter.assigned_tables:
            assigned_list = waiter.assigned_tables.split(',')
            if room_number in assigned_list:
                assigned_list.remove(room_number)
                waiter.assigned_tables = ','.join(assigned_list) if assigned_list else None

        db.commit()

        print(f"🎉 用餐结束: {room_number} - 服务员: {current_user.full_name}")

        return {"success": True, "message": "用餐已结束，包厢已清空"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 检查权限状态API
@app.get("/api/check-auth-status")
async def check_auth_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """检查当前用户的权限状态"""
    try:
        # 重新从数据库获取用户信息，确保状态是最新的
        fresh_user = db.query(User).filter(User.id == current_user.id).first()
        if not fresh_user:
            raise HTTPException(status_code=401, detail="用户不存在")

        # 检查用户状态
        if fresh_user.status != UserStatus.ACTIVE:
            raise HTTPException(status_code=401, detail="账号已被禁用")

        # 对于服务员，检查授权状态
        if fresh_user.role == UserRole.WAITER:
            return {
                "success": True,
                "is_authorized": fresh_user.is_authorized,
                "role": fresh_user.role.value,
                "assigned_tables": fresh_user.assigned_tables
            }
        else:
            # 非服务员角色，返回基本状态
            return {
                "success": True,
                "is_authorized": True,  # 非服务员默认有权限
                "role": fresh_user.role.value
            }
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        print(f"❌ 检查权限状态失败: {e}")
        raise HTTPException(status_code=500, detail="检查权限状态失败")


# 获取服务员列表API
@app.get("/api/users")
async def get_users_api(
    role: str = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 餐饮经理可以获取服务员列表
    print(f"🔍 API调用: /users?role={role}")
    print(f"🔍 当前用户: {current_user.username}")
    print(f"🔍 用户角色: {current_user.role}")
    print(f"🔍 角色类型: {type(current_user.role)}")
    print(f"🔍 角色值: {current_user.role.value if hasattr(current_user.role, 'value') else 'No value attr'}")

    # 检查用户角色
    allowed_roles = ['manager', 'admin']
    user_role = current_user.role.value if hasattr(current_user.role, 'value') else str(current_user.role)

    if user_role not in allowed_roles:
        print(f"❌ 权限不足: {user_role} 不在 {allowed_roles} 中")
        raise HTTPException(status_code=403, detail=f"权限不足，当前角色: {user_role}")

    print(f"✅ 权限检查通过: {user_role}")

    try:
        query = db.query(User)
        if role and role == 'waiter':
            # 只返回服务员角色的用户
            query = query.filter(User.role == UserRole.WAITER)

        users = query.filter(User.is_active == True).all()

        result = []
        for user in users:
            result.append({
                "id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "role": user.role.value,
                "assigned_tables": user.assigned_tables or ""
            })

        print(f"📋 获取用户列表: role={role}, 返回{len(result)}个用户")
        return result

    except Exception as e:
        print(f"❌ 获取用户列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取用户列表失败: {str(e)}")


# 餐饮经理授权服务员
@app.post("/tables/{table_number}/assign-waiter")
async def assign_waiter_to_table(
    table_number: str,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        data = await request.json()
        waiter_id = data.get('waiter_id')

        # 查找餐桌
        table = db.query(Table).filter(Table.number == table_number).first()
        if not table:
            raise HTTPException(status_code=404, detail="餐桌不存在")

        # 查找服务员，确保只能授权给服务员角色
        waiter = db.query(User).filter(
            User.id == waiter_id,
            User.role == UserRole.WAITER,
            User.is_active == True
        ).first()
        if not waiter:
            raise HTTPException(status_code=404, detail="服务员不存在或不是服务员角色")

        # 检查服务员是否已经负责此包厢（防止重复分配同一包厢）
        if waiter.assigned_tables:
            assigned_list = [room.strip() for room in waiter.assigned_tables.split(',')]
            if table_number in assigned_list:
                raise HTTPException(
                    status_code=400,
                    detail=f"服务员{waiter.full_name}已被分配负责{table_number}包厢"
                )

        print(f"🔄 服务员{waiter.full_name}当前分配包厢: {waiter.assigned_tables or '无'}")

        # 检查该包厢是否已分配给其他服务员
        if table.assigned_waiter_id and table.assigned_waiter_id != waiter_id:
            current_waiter = db.query(User).filter(User.id == table.assigned_waiter_id).first()
            if current_waiter:
                # 从当前服务员的分配列表中移除该包厢
                if current_waiter.assigned_tables:
                    assigned_list = [room.strip() for room in current_waiter.assigned_tables.split(',')]
                    if table_number in assigned_list:
                        assigned_list.remove(table_number)
                        current_waiter.assigned_tables = ','.join(assigned_list) if assigned_list else None
                        # 如果没有分配的包厢了，取消授权
                        if not current_waiter.assigned_tables:
                            current_waiter.is_authorized = False
                            current_waiter.authorized_by = None
                print(f"🔄 从 {current_waiter.full_name} 移除包厢 {table_number}")

        # 分配服务员到餐桌
        table.assigned_waiter_id = waiter_id
        table.status = TableStatus.RESERVED  # 改为已预订状态

        # 更新该餐桌的活跃订单的服务员信息和状态
        active_orders = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.CONFIRMED,
                OrderStatus.IN_PROGRESS,
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING
            ])
        ).all()

        for order in active_orders:
            order.waiter_id = waiter_id
            # 如果订单是已预订状态，转为待开始状态
            if order.status == OrderStatus.RESERVED or order.status == OrderStatus.CONFIRMED:
                order.status = OrderStatus.PENDING_START
            print(f"📋 更新订单服务员: 订单{order.order_number} -> {waiter.full_name}, 状态: {order.status}")

        # 授权服务员并分配包厢（确保一个包厢只对应一个服务员）
        waiter.is_authorized = True
        waiter.authorized_by = current_user.id

        # 重新设置分配的包厢列表，确保唯一性
        if waiter.assigned_tables:
            assigned_list = [room.strip() for room in waiter.assigned_tables.split(',')]
            if table_number not in assigned_list:
                assigned_list.append(table_number)
            waiter.assigned_tables = ','.join(assigned_list)
        else:
            waiter.assigned_tables = table_number

        db.commit()

        print(f"✅ 授权成功: {waiter.full_name} -> {table_number}")

        return {"success": True, "message": f"已授权{waiter.full_name}负责{table_number}包厢"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 更换服务员
@app.post("/tables/{table_number}/change-waiter")
async def change_waiter(
    table_number: str,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('waiter.authorize'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        data = await request.json()
        old_waiter_id = data.get('old_waiter_id')
        new_waiter_id = data.get('new_waiter_id')

        # 查找餐桌
        table = db.query(Table).filter(Table.number == table_number).first()
        if not table:
            raise HTTPException(status_code=404, detail="餐桌不存在")

        # 查找新服务员，确保只能授权给服务员角色
        new_waiter = db.query(User).filter(
            User.id == new_waiter_id,
            User.role == UserRole.WAITER,
            User.is_active == True
        ).first()
        if not new_waiter:
            raise HTTPException(status_code=404, detail="新服务员不存在或不是服务员角色")

        # 查找旧服务员
        old_waiter = db.query(User).filter(User.id == old_waiter_id).first()

        # 清除旧服务员的包厢分配
        if old_waiter and old_waiter.assigned_tables:
            assigned_list = [room.strip() for room in old_waiter.assigned_tables.split(',')]
            if table_number in assigned_list:
                assigned_list.remove(table_number)
                old_waiter.assigned_tables = ','.join(assigned_list) if assigned_list else None
                # 如果没有分配的包厢了，取消授权
                if not old_waiter.assigned_tables:
                    old_waiter.is_authorized = False
                    old_waiter.authorized_by = None

        # 检查新服务员当前分配情况（支持多包厢分配）
        if new_waiter.assigned_tables:
            print(f"🔄 新服务员{new_waiter.full_name}当前已分配包厢: {new_waiter.assigned_tables}")
        else:
            print(f"🔄 新服务员{new_waiter.full_name}当前未分配任何包厢")

        # 分配新服务员到餐桌
        table.assigned_waiter_id = new_waiter_id

        # 更新该餐桌的活跃订单的服务员信息
        active_orders = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([OrderStatus.CONFIRMED, OrderStatus.IN_PROGRESS])
        ).all()

        for order in active_orders:
            order.waiter_id = new_waiter_id
            print(f"📋 更新订单服务员: 订单{order.order_number} -> {new_waiter.full_name}")

        # 授权新服务员并分配包厢（支持多包厢分配）
        new_waiter.is_authorized = True
        new_waiter.authorized_by = current_user.id

        # 将包厢添加到服务员的分配列表中（如果还没有的话）
        if new_waiter.assigned_tables:
            assigned_list = [room.strip() for room in new_waiter.assigned_tables.split(',')]
            if table_number not in assigned_list:
                assigned_list.append(table_number)
                new_waiter.assigned_tables = ','.join(assigned_list)
                print(f"✅ 添加包厢到服务员分配列表: {new_waiter.full_name} -> {new_waiter.assigned_tables}")
        else:
            new_waiter.assigned_tables = table_number
            print(f"✅ 首次分配包厢给服务员: {new_waiter.full_name} -> {table_number}")

        db.commit()

        print(f"🔄 更换服务员: {table_number} -> {old_waiter.full_name if old_waiter else '未知'} → {new_waiter.full_name}")

        return {"success": True, "message": f"已将{table_number}包厢从{old_waiter.full_name if old_waiter else '未知'}更换为{new_waiter.full_name}"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 更换服务员 (仅餐饮经理)
@app.post("/api/orders/{order_id}/change-waiter")
async def change_waiter(
    order_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission("waiter.change"):
        raise HTTPException(status_code=403, detail="权限不足：只有餐饮经理可以更换服务员")

    try:
        data = await request.json()
        new_waiter_id = data.get('new_waiter_id')

        if not new_waiter_id:
            raise HTTPException(status_code=400, detail="请选择新服务员")

        # 获取订单
        order = db.query(Order).filter(Order.id == order_id).first()
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")

        # 检查订单状态 - 已完成的订单不允许更换服务员
        if order.status == OrderStatus.COMPLETED:
            raise HTTPException(status_code=400, detail="已完成的订单不允许更换服务员")

        # 获取新服务员
        new_waiter = db.query(User).filter(
            User.id == new_waiter_id,
            User.role == UserRole.WAITER,
            User.status == UserStatus.ACTIVE
        ).first()
        if not new_waiter:
            raise HTTPException(status_code=404, detail="服务员不存在或不可用")

        # 获取原服务员
        old_waiter = None
        if order.waiter_id:
            old_waiter = db.query(User).filter(User.id == order.waiter_id).first()

        # 更新订单服务员
        order.waiter_id = new_waiter_id

        # 更新餐桌分配
        if order.table:
            order.table.assigned_waiter_id = new_waiter_id

        # 从原服务员的分配列表中移除该包厢
        if old_waiter and order.table:
            table_number = order.table.number
            if old_waiter.assigned_tables:
                assigned_list = [room.strip() for room in old_waiter.assigned_tables.split(',')]
                if table_number in assigned_list:
                    assigned_list.remove(table_number)
                    old_waiter.assigned_tables = ','.join(assigned_list) if assigned_list else None
                    print(f"🔄 从原服务员移除包厢: {old_waiter.full_name} -> 移除{table_number}，剩余: {old_waiter.assigned_tables or '无'}")

                    # 如果原服务员没有其他包厢了，取消授权
                    if not old_waiter.assigned_tables:
                        old_waiter.is_authorized = False
                        old_waiter.authorized_by = None
                        print(f"🚪 原服务员无其他包厢，取消授权: {old_waiter.full_name}")

        # 设置新服务员为已授权状态并分配包厢
        new_waiter.is_authorized = True
        new_waiter.authorized_by = current_user.id
        if order.table:
            table_number = order.table.number
            # 将包厢添加到新服务员的分配列表中
            if new_waiter.assigned_tables:
                assigned_list = [room.strip() for room in new_waiter.assigned_tables.split(',')]
                if table_number not in assigned_list:
                    assigned_list.append(table_number)
                    new_waiter.assigned_tables = ','.join(assigned_list)
                    print(f"✅ 添加包厢到新服务员: {new_waiter.full_name} -> {new_waiter.assigned_tables}")
            else:
                new_waiter.assigned_tables = table_number
                print(f"✅ 首次分配包厢给新服务员: {new_waiter.full_name} -> {table_number}")

        db.commit()

        # 记录服务员更换日志
        print(f"🔄 服务员更换完成: 订单{order.order_number} -> {old_waiter.full_name if old_waiter else '无'} → {new_waiter.full_name}")

        return {
            "success": True,
            "message": f"服务员已从 {old_waiter.full_name if old_waiter else '无'} 更换为 {new_waiter.full_name}",
            "old_waiter_revoked": True,  # 标记原服务员权限已被撤销
            "new_waiter_authorized": True
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更换服务员失败: {str(e)}")


# 系统管理员强制结束包厢用餐
@app.post("/admin/force-end-dining")
async def admin_force_end_dining(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """系统管理员强制结束包厢用餐"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="只有系统管理员可以强制结束用餐")

    try:
        # 获取请求数据
        data = await request.json()
        room_number = data.get('room_number')
        reason = data.get('reason', '系统管理员强制结束')

        if not room_number:
            raise HTTPException(status_code=400, detail="包厢号不能为空")

        # 调用统一的状态重置机制（强制结束）
        result = await complete_dining_reset(
            room_number=room_number,
            operator_user=current_user,
            db=db,
            reason=f"系统管理员强制结束，原因：{reason}",
            force_end=True  # 强制结束，跳过所有检查
        )

        if not result["success"]:
            return result

        # 广播强制结束用餐消息
        try:
            await websocket_manager.broadcast_dining_ended(
                room_number=room_number,
                waiter_name=f"系统管理员({current_user.full_name})"
            )
            print(f"📡 强制结束用餐WebSocket通知已发送")
        except Exception as e:
            print(f"❌ 强制结束用餐WebSocket通知发送失败: {e}")

        # 返回管理员专用的响应格式
        return {
            "success": True,
            "message": f"{room_number}包厢用餐已被强制结束",
            "operator": current_user.full_name,
            "reason": reason,
            "reset_details": result.get("reset_details", {})
        }

    except Exception as e:
        print(f"❌ 系统管理员强制结束用餐失败: {e}")
        raise HTTPException(status_code=500, detail=f"强制结束用餐失败: {str(e)}")


# 餐饮经理强制结束包厢
@app.post("/tables/{table_number}/force-end")
async def force_end_room(
    table_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """餐饮经理强制结束包厢用餐"""
    if current_user.role != UserRole.MANAGER:
        raise HTTPException(status_code=403, detail="只有餐饮经理可以强制结束包厢")

    try:
        # 调用统一的状态重置机制（强制结束）
        result = await complete_dining_reset(
            room_number=table_number,
            operator_user=current_user,
            db=db,
            reason=f"餐饮经理强制结束，操作人：{current_user.full_name}",
            force_end=True  # 强制结束，跳过所有检查
        )

        if not result["success"]:
            return result

        # 广播强制结束用餐消息
        try:
            await websocket_manager.broadcast_dining_ended(
                room_number=table_number,
                waiter_name=f"餐饮经理({current_user.full_name})"
            )
            print(f"📡 餐饮经理强制结束用餐WebSocket通知已发送")
        except Exception as e:
            print(f"❌ 餐饮经理强制结束用餐WebSocket通知发送失败: {e}")

        # 返回经理专用的响应格式
        reset_details = result.get("reset_details", {})
        message_parts = [f"{table_number}包厢已强制结束"]
        if reset_details.get("waiters_reset", 0) > 0:
            message_parts.append(f"重置了{reset_details['waiters_reset']}个服务员状态")
        if reset_details.get("items_completed", 0) > 0:
            message_parts.append(f"完成了{reset_details['items_completed']}个菜品")
        message_parts.append("包厢状态已重置为空闲")

        return {"success": True, "message": "，".join(message_parts)}

    except Exception as e:
        print(f"❌ 餐饮经理强制结束包厢失败: {e}")
        raise HTTPException(status_code=500, detail=f"强制结束包厢失败: {str(e)}")


# 厨房标记指令已处理
@app.post("/kitchen/actions/{action_id}/processed")
async def mark_action_processed(
    action_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        from models.waiter_action import WaiterAction
        action = db.query(WaiterAction).filter(WaiterAction.id == action_id).first()
        if not action:
            raise HTTPException(status_code=404, detail="指令不存在")

        action.is_processed = True
        action.processed_by = current_user.id
        action.processed_at = get_china_time()

        db.commit()

        print(f"✅ 指令已处理: {action.room_number} - {action.action_type}")

        return {"success": True, "message": "指令已标记为已处理"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 获取最新指令API（需要权限）
@app.get("/api/waiter-actions/latest")
async def get_latest_waiter_actions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        from models.waiter_action import WaiterAction

        # 获取最近5分钟内的未处理指令
        five_minutes_ago = get_china_time() - timedelta(minutes=5)

        actions = db.query(WaiterAction).filter(
            WaiterAction.created_at >= five_minutes_ago,
            WaiterAction.is_processed == False
        ).order_by(WaiterAction.created_at.desc()).limit(10).all()

        action_list = []
        for action in actions:
            action_list.append({
                "id": action.id,
                "room_number": action.room_number,
                "action_type": action.action_type,
                "action_type_display": action.action_type_display,
                "action_content": action.action_content,
                "created_at": action.created_at.isoformat(),
                "is_processed": action.is_processed
            })

        return {"success": True, "actions": action_list}
    except Exception as e:
        print(f"⚠️ 获取最新指令失败: {e}")
        return {"success": False, "actions": []}


# 厨房大屏专用API（无需权限验证）
@app.get("/api/kitchen-display/waiter-actions")
async def get_kitchen_display_waiter_actions(db: Session = Depends(get_db)):
    """厨房大屏获取服务员指令（无需登录）"""
    try:
        from models.waiter_action import WaiterAction

        # 获取最近10分钟内的未处理指令
        ten_minutes_ago = get_china_time() - timedelta(minutes=10)

        actions = db.query(WaiterAction).filter(
            WaiterAction.created_at >= ten_minutes_ago,
            WaiterAction.is_processed == False
        ).order_by(WaiterAction.created_at.desc()).limit(20).all()

        action_list = []
        for action in actions:
            action_list.append({
                "id": action.id,
                "room_number": action.room_number,
                "action_type": action.action_type,
                "action_type_display": action.action_type_display,
                "action_content": action.action_content,
                "created_at": action.created_at.isoformat(),
                "is_processed": action.is_processed
            })

        print(f"🔍 厨房大屏API返回 {len(action_list)} 条未处理指令")
        return action_list  # 直接返回数组，保持与原API兼容
    except Exception as e:
        print(f"⚠️ 厨房大屏获取指令失败: {e}")
        return []  # 返回空数组


# 服务员划菜（不支持撤销）
@app.post("/waiter/mark-dish/{item_id}")
async def mark_dish(
    item_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以划菜")

    try:
        data = await request.json()
        status = data.get('status')  # 只支持 'served'

        # 只允许划菜操作，不允许撤销
        if status != 'served':
            raise HTTPException(status_code=400, detail="不支持撤销操作，菜品一旦划菜无法撤销")

        # 获取菜品信息
        order_item = db.query(OrderItem).join(Order).join(Table).filter(
            OrderItem.id == item_id
        ).first()

        if not order_item:
            raise HTTPException(status_code=404, detail="菜品不存在")

        # 检查服务员是否有权限操作此包厢
        if current_user.assigned_tables:
            assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
            room_number = order_item.order.table.number
            if room_number not in assigned_rooms:
                raise HTTPException(status_code=403, detail="您没有权限操作此包厢")
        else:
            raise HTTPException(status_code=403, detail="您还未被分配包厢")

        # 检查菜品是否已经被划菜
        if order_item.waiter_status == 'served':
            raise HTTPException(status_code=400, detail="此菜品已经划菜，无法重复操作")

        # 更新服务员状态为已划菜
        order_item.waiter_status = status
        order_item.served_at = get_china_time()
        action_msg = "划菜"

        db.commit()

        print(f"📋 {action_msg}: {current_user.full_name} -> {order_item.dish_name} ({order_item.order.table.number})")

        return {"success": True, "message": f"{action_msg}成功"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 服务员确认菜品已上菜
@app.post("/waiter/confirm-dish-served/{item_id}")
async def confirm_dish_served(
    item_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以确认上菜")

    try:
        # 获取菜品信息
        order_item = db.query(OrderItem).join(Order).join(Table).filter(
            OrderItem.id == item_id
        ).first()

        if not order_item:
            raise HTTPException(status_code=404, detail="菜品不存在")

        # 检查服务员是否有权限操作此包厢
        if current_user.assigned_tables:
            assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
            room_number = order_item.order.table.number
            if room_number not in assigned_rooms:
                raise HTTPException(status_code=403, detail="您没有权限操作此包厢")
        else:
            raise HTTPException(status_code=403, detail="您还未被分配包厢")

        # 确认菜品已上菜
        order_item.waiter_confirmed = True
        order_item.confirmed_at = get_china_time()

        db.commit()

        print(f"✅ 确认上菜: {current_user.full_name} -> {order_item.dish_name} ({order_item.order.table.number})")

        return {"success": True, "message": "确认上菜成功"}

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


# 🔧 完整的用餐结束状态重置机制
async def complete_dining_reset(
    room_number: str,
    operator_user: User,
    db: Session,
    reason: str = "正常结束用餐",
    force_end: bool = False
) -> dict:
    """
    完整的用餐结束状态重置机制

    Args:
        room_number: 包厢号
        operator_user: 操作用户（服务员或管理员）
        db: 数据库会话
        reason: 结束原因
        force_end: 是否强制结束（跳过检查）

    Returns:
        dict: 操作结果
    """
    try:
        print(f"🔄 开始用餐结束状态重置: {room_number} - 操作员: {operator_user.full_name} - 原因: {reason}")

        # 1. 查找包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            return {"success": False, "message": "包厢不存在"}

        # 2. 查找该包厢的活跃订单
        active_order = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.SERVING,
                OrderStatus.IN_PROGRESS,
                OrderStatus.PENDING_START,
                OrderStatus.RESERVED,
                OrderStatus.CONFIRMED
            ])
        ).first()

        if not active_order:
            return {"success": False, "message": "该包厢暂无活跃订单"}

        # 3. 检查菜品完成状态（非强制结束时）
        if not force_end:
            pending_items = db.query(OrderItem).filter(
                OrderItem.order_id == active_order.id,
                OrderItem.status.in_(['pending_cook', 'cooking'])
            ).count()

            if pending_items > 0:
                return {
                    "success": False,
                    "message": f"还有 {pending_items} 道菜品未完成，请确认所有菜品都已上齐后再结束用餐"
                }

        # 4. 获取当前分配的服务员信息
        assigned_waiter = None
        if table.assigned_waiter_id:
            assigned_waiter = db.query(User).filter(User.id == table.assigned_waiter_id).first()

        # === 开始原子性状态重置 ===

        # 5. 订单状态更新
        old_order_status = active_order.status
        active_order.status = OrderStatus.COMPLETED
        active_order.completed_at = get_china_time()
        active_order.dining_end_time = get_china_time()
        active_order.waiter_id = None  # 清除订单的服务员关联

        print(f"📋 订单状态更新: {old_order_status} -> {active_order.status}")

        # 6. 包厢状态重置
        old_table_status = table.status
        old_guests = table.current_guests
        old_waiter_id = table.assigned_waiter_id

        table.status = TableStatus.AVAILABLE
        table.current_order_id = None
        table.current_guests = 0
        table.assigned_waiter_id = None

        print(f"🏠 包厢状态重置: {old_table_status} -> {table.status}, 人数: {old_guests} -> 0, 服务员: {old_waiter_id} -> None")

        # 7. 服务员状态重置
        waiters_to_reset = []

        # 重置分配的服务员
        if assigned_waiter:
            waiters_to_reset.append(assigned_waiter)

        # 如果操作员是服务员且不同于分配的服务员，也要重置
        if operator_user.role == UserRole.WAITER and operator_user.id != (assigned_waiter.id if assigned_waiter else None):
            waiters_to_reset.append(operator_user)

        # 去重
        waiters_to_reset = list({w.id: w for w in waiters_to_reset}.values())

        for waiter in waiters_to_reset:
            if waiter.assigned_tables:
                assigned_list = waiter.assigned_tables.split(',')
                if room_number in assigned_list:
                    assigned_list.remove(room_number)
                    waiter.assigned_tables = ','.join(assigned_list) if assigned_list else None
                    print(f"👤 服务员状态重置: {waiter.full_name} - 移除包厢: {room_number}")

                    # 如果服务员没有其他包厢了，取消授权
                    if not waiter.assigned_tables:
                        waiter.is_authorized = False
                        waiter.authorized_by = None
                        print(f"🚪 服务员无其他包厢，取消授权: {waiter.full_name}")

        # 8. 更新所有订单项状态为已完成
        updated_items = db.query(OrderItem).filter(
            OrderItem.order_id == active_order.id,
            OrderItem.status.in_([DishItemStatus.PENDING_COOK, DishItemStatus.READY])
        ).update({
            'status': DishItemStatus.READY,
            'served_at': get_china_time(),
            'waiter_confirmed': True,
            'confirmed_at': get_china_time()
        })

        print(f"🍽️ 订单项状态更新: {updated_items} 个菜品标记为已完成")

        # 9. 清除所有相关的服务员操作记录
        try:
            from models.waiter_action import WaiterAction

            # 删除该包厢的所有用餐相关记录
            deleted_actions = db.query(WaiterAction).filter(
                WaiterAction.room_number == room_number,
                WaiterAction.action_type.in_(['dining_start', 'serve_dish', 'rush_dish', 'urge_dish'])
            ).delete()

            print(f"🗑️ 清除服务员操作记录: {deleted_actions} 条记录")

            # 记录结束用餐操作（修复：确保不包含订单号，避免语音播报不必要信息）
            end_action = WaiterAction(
                waiter_id=operator_user.id,
                room_number=room_number,
                action_type='force_end' if force_end else 'dining_end',
                action_content=f'{"强制" if force_end else ""}用餐结束',
                created_at=get_china_time()
            )
            db.add(end_action)

        except ImportError:
            print("⚠️ WaiterAction模型不存在，跳过操作记录清理")

        # 10. 提交所有更改（原子性操作）
        db.commit()

        # 11. 检查服务员是否需要退出登录
        should_logout = False
        logout_message = None

        if operator_user.role == UserRole.WAITER:
            should_logout = not operator_user.assigned_tables or operator_user.assigned_tables.strip() == ""
            if should_logout:
                logout_message = "您已完成所有包厢的服务，系统将自动退出登录"

        print(f"✅ 用餐结束状态重置完成: {room_number}")
        print(f"   - 订单: {active_order.order_number} -> 已完成")
        print(f"   - 包厢: {room_number} -> 可用")
        print(f"   - 服务员: {len(waiters_to_reset)} 人状态已重置")
        print(f"   - 需要退出登录: {should_logout}")

        return {
            "success": True,
            "message": f"{room_number}包厢用餐已{'强制' if force_end else ''}结束",
            "should_logout": should_logout,
            "logout_message": logout_message,
            "order_number": active_order.order_number,
            "reset_details": {
                "table_status": f"{old_table_status} -> {table.status}",
                "order_status": f"{old_order_status} -> {active_order.status}",
                "guests_count": f"{old_guests} -> 0",
                "waiters_reset": len(waiters_to_reset),
                "items_completed": updated_items
            }
        }

    except Exception as e:
        db.rollback()
        error_msg = f"用餐结束状态重置失败: {str(e)}"
        print(f"❌ {error_msg}")
        return {"success": False, "message": error_msg}


# 服务员结束包厢用餐
@app.post("/waiter/finish-room-dining/{room_number}")
async def finish_room_dining(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """服务员结束包厢用餐"""
    if current_user.role != UserRole.WAITER:
        raise HTTPException(status_code=403, detail="只有服务员可以结束用餐")

    try:
        # 检查服务员是否有权限操作此包厢
        if current_user.assigned_tables:
            assigned_rooms = [room.strip() for room in current_user.assigned_tables.split(',')]
            if room_number not in assigned_rooms:
                raise HTTPException(status_code=403, detail="您没有权限操作此包厢")
        else:
            raise HTTPException(status_code=403, detail="您还未被分配包厢")

        # 调用统一的状态重置机制
        result = await complete_dining_reset(
            room_number=room_number,
            operator_user=current_user,
            db=db,
            reason="服务员正常结束用餐",
            force_end=False
        )

        if not result["success"]:
            return result

        # 广播结束用餐消息，触发厨房大屏刷新
        try:
            await websocket_manager.broadcast_dining_ended(
                room_number=room_number,
                waiter_name=current_user.full_name
            )
            print(f"📡 结束用餐WebSocket通知已发送")
        except Exception as e:
            print(f"❌ 结束用餐WebSocket通知发送失败: {e}")

        return result

    except Exception as e:
        print(f"❌ 服务员结束用餐失败: {e}")
        raise HTTPException(status_code=500, detail=f"结束用餐失败: {str(e)}")


# 删除重复的API，使用第一个API


# 打荷员工作台
@app.get("/kitchen-helper", response_class=HTMLResponse)
async def kitchen_helper_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # 允许打荷员和系统管理员访问
    if not (current_user.role == UserRole.KITCHEN_HELPER or
            current_user.role == UserRole.ADMIN or
            current_user.has_permission('kitchen.view')):
        raise HTTPException(status_code=403, detail="权限不足，只有打荷员和系统管理员可以访问此页面")

    # 获取制作任务 - 暂时使用字符串查询避免枚举问题
    order_items = db.query(OrderItem).join(Order).filter(
        OrderItem.status.in_(['pending_cook', 'ready'])
    ).order_by(OrderItem.created_at.asc()).all()

    # 获取服务员指令
    waiter_actions = []
    try:
        from models.waiter_action import WaiterAction
        waiter_actions = db.query(WaiterAction).order_by(
            WaiterAction.created_at.desc()
        ).limit(10).all()
    except Exception:
        pass

    return templates.TemplateResponse(
        "kitchen_helper.html",
        {
            "request": request,
            "user": current_user,
            "order_items": order_items,
            "waiter_actions": waiter_actions
        }
    )


# API: 获取厨房包厢状态（用于打荷操作页面）
@app.get("/api/kitchen/rooms-status")
async def get_kitchen_rooms_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取厨房包厢状态数据"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 获取所有已开始用餐的包厢
        orders_by_room = {}

        # 查询所有有活跃订单的餐桌，只显示已开始用餐的
        # 修复：不仅仅依赖订单状态，主要依赖dining_start_time字段
        active_orders = db.query(Order).filter(
            Order.status.in_([
                OrderStatus.RESERVED,      # 已预订
                OrderStatus.CONFIRMED,     # 已确认
                OrderStatus.PENDING_START, # 等待开始
                OrderStatus.SERVING,       # 用餐中
                OrderStatus.IN_PROGRESS    # 进行中
            ]),
            Order.dining_start_time.isnot(None)  # 必须已开始用餐
        ).order_by(Order.dining_start_time.asc()).all()

        print(f"🔍 厨房状态查询: 找到 {len(active_orders)} 个已开始用餐的订单")

        for order in active_orders:
            table = order.table
            room_name = table.number

            # 获取该订单的所有菜品项
            order_items = db.query(OrderItem).filter(
                OrderItem.order_id == order.id
            ).order_by(OrderItem.created_at.asc()).all()

            if order_items:
                # 按状态分类菜品
                pending_dishes = []
                cooking_dishes = []
                ready_dishes = []

                for item in order_items:
                    dish_data = {
                        'id': item.id,
                        'name': item.dish_name,
                        'status': item.status.value if item.status else 'pending_cook',
                        'created_at': item.created_at.isoformat() if item.created_at else None,
                        'ready_at': item.ready_at.isoformat() if item.ready_at else None
                    }

                    if item.status == DishItemStatus.PENDING_COOK:
                        pending_dishes.append(dish_data)
                    elif item.status == DishItemStatus.COOKING:
                        cooking_dishes.append(dish_data)
                    elif item.status == DishItemStatus.READY:
                        ready_dishes.append(dish_data)

                orders_by_room[room_name] = {
                    'room_name': room_name,
                    'guest_count': order.guest_count or 0,
                    'dining_start_time': order.dining_start_time.strftime('%H:%M') if order.dining_start_time else '',
                    'special_requirements': order.special_requests or '',
                    'pending_dishes': pending_dishes,
                    'cooking_dishes': cooking_dishes,
                    'ready_dishes': ready_dishes,
                    'total_dishes': len(order_items),
                    'completed_dishes': len(ready_dishes)
                }

        return {
            "success": True,
            "rooms": list(orders_by_room.values()),
            "total_rooms": len(orders_by_room)
        }

    except Exception as e:
        print(f"❌ 获取厨房包厢状态失败: {e}")
        return {"success": False, "rooms": [], "error": str(e)}


# API: 更新菜品状态（用于打荷操作页面）
@app.post("/api/kitchen/dish-status")
async def update_dish_status_api(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新菜品状态"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        data = await request.json()
        dish_id = data.get('dish_id')
        new_status = data.get('status')

        if not dish_id or not new_status:
            raise HTTPException(status_code=400, detail="缺少必要参数")

        # 查找菜品项
        item = db.query(OrderItem).filter(OrderItem.id == dish_id).first()
        if not item:
            raise HTTPException(status_code=404, detail="菜品不存在")

        # 获取包厢信息
        order = item.order
        table = order.table
        room_number = table.number

        # 更新状态（支持PENDING_COOK、COOKING、READY三个状态）
        if new_status == 'ready':
            item.status = DishItemStatus.READY
            item.ready_at = get_china_time()
        elif new_status == 'cooking':
            item.status = DishItemStatus.COOKING
            # 制作中状态不设置ready_at时间
        elif new_status == 'pending_cook':
            item.status = DishItemStatus.PENDING_COOK

        item.updated_at = get_china_time()
        db.commit()

        # 如果是制作完成，发送WebSocket通知
        if new_status == 'ready':
            try:
                await websocket_manager.broadcast_dish_ready(
                    dish_id=dish_id,
                    room_number=room_number,
                    dish_name=item.dish_name
                )
                print(f"📡 WebSocket通知已发送: 包厢{room_number}的{item.dish_name}制作完成")
            except Exception as e:
                print(f"❌ WebSocket通知发送失败: {e}")

        return {"success": True, "message": "状态更新成功"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 更新菜品状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# API: 获取最新服务员指令（用于打荷操作页面）
@app.get("/api/waiter-actions/latest")
async def get_latest_waiter_actions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取最新的服务员指令"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        from models.waiter_action import WaiterAction
        actions = db.query(WaiterAction).filter(
            WaiterAction.is_processed == False
        ).order_by(WaiterAction.created_at.desc()).limit(20).all()

        action_list = []
        for action in actions:
            action_list.append({
                'id': action.id,
                'room_number': action.room_number,
                'action_type': action.action_type,
                'action_type_display': action.action_type_display,
                'action_content': action.action_content,
                'created_at': action.created_at.isoformat() if action.created_at else None,
                'waiter_name': action.waiter_name,
                'is_processed': action.is_processed
            })

        return {
            "success": True,
            "actions": action_list
        }

    except Exception as e:
        print(f"❌ 获取服务员指令失败: {e}")
        return {"success": False, "actions": []}


# API: 确认服务员指令（用于打荷操作页面）
@app.post("/api/waiter-actions/{action_id}/confirm")
async def confirm_waiter_action(
    action_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """确认服务员指令，标记为已处理"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        from models.waiter_action import WaiterAction
        action = db.query(WaiterAction).filter(WaiterAction.id == action_id).first()

        if not action:
            return {"success": False, "message": "指令不存在"}

        if action.is_processed:
            return {"success": False, "message": "指令已被处理"}

        # 标记为已处理
        action.is_processed = True
        action.processed_at = get_china_time()
        action.processed_by = current_user.id

        db.commit()

        print(f"✅ 服务员指令已确认: ID={action_id}, 处理人={current_user.full_name}")
        return {"success": True, "message": "指令已确认"}

    except Exception as e:
        db.rollback()
        print(f"❌ 确认服务员指令失败: {e}")
        return {"success": False, "message": f"确认失败: {str(e)}"}


# API: 获取未开始用餐的包厢（用于打荷操作页面）
@app.get("/api/kitchen/pending-rooms")
async def get_pending_rooms(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取已预订但尚未开始用餐的包厢"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 查询已预订但尚未开始用餐的订单
        pending_orders = db.query(Order).filter(
            Order.status.in_([
                OrderStatus.RESERVED,      # 已预订
                OrderStatus.PENDING_START  # 等待开始
            ]),
            Order.dining_start_time.is_(None)  # 尚未开始用餐
        ).order_by(Order.ordered_at.asc()).all()

        pending_rooms = []
        for order in pending_orders:
            table = order.table
            # 获取负责该包厢的服务员
            waiter_name = "未分配"
            if order.waiter_id:
                waiter = db.query(User).filter(User.id == order.waiter_id).first()
                if waiter:
                    waiter_name = waiter.full_name

            pending_rooms.append({
                'room_name': table.number,
                'waiter_name': waiter_name,
                'guest_count': order.guest_count or 0,
                'ordered_at': order.ordered_at.strftime('%H:%M') if order.ordered_at else '',
                'special_requirements': order.special_requests or ''
            })

        return {
            "success": True,
            "rooms": pending_rooms,
            "total_rooms": len(pending_rooms)
        }

    except Exception as e:
        print(f"❌ 获取未开始包厢失败: {e}")
        return {"success": False, "rooms": [], "error": str(e)}


# API: 删除订单
@app.delete("/api/orders/{order_id}")
async def delete_order_api(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除订单 - 仅餐饮经理和系统管理员可操作"""
    # 权限检查
    if current_user.role.value not in ['restaurant_manager', 'admin']:
        raise HTTPException(status_code=403, detail="权限不足，只有餐饮经理和系统管理员可以删除订单")

    try:
        # 查找订单
        order = db.query(Order).filter(Order.id == order_id).first()
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")

        # 检查订单状态 - 只能删除已完成或已取消的订单
        if order.status.value not in ['completed', 'cancelled']:
            raise HTTPException(
                status_code=400,
                detail=f"不能删除进行中的订单，当前状态：{order.status.value}"
            )

        # 使用事务进行删除操作
        try:
            # 记录操作日志
            from models.waiter_action import WaiterAction
            log_action = WaiterAction(
                waiter_id=current_user.id,
                room_number=order.table.number if order.table else '无',
                action_type='delete_order',
                action_content=f'删除订单 {order.order_number}，包厢：{order.table.number if order.table else "无"}，客户：{order.customer_name or "无"}',
                created_at=get_china_time(),
                is_processed=True  # 操作日志直接标记为已处理
            )
            db.add(log_action)

            # 删除订单项
            db.query(OrderItem).filter(OrderItem.order_id == order_id).delete()

            # 删除订单
            db.delete(order)

            # 提交事务
            db.commit()

            print(f"✅ 订单删除成功: {order.order_number} by {current_user.full_name}")

            return {
                "success": True,
                "message": f"订单 {order.order_number} 删除成功"
            }

        except Exception as e:
            db.rollback()
            print(f"❌ 删除订单事务失败: {e}")
            raise e

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 删除订单失败: {e}")
        raise HTTPException(status_code=500, detail=f"删除订单失败: {str(e)}")


# API: 批量删除订单
@app.post("/api/orders/batch-delete")
async def batch_delete_orders_api(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量删除订单 - 仅餐饮经理和系统管理员可操作"""
    # 权限检查
    if current_user.role.value not in ['restaurant_manager', 'admin']:
        raise HTTPException(status_code=403, detail="权限不足，只有餐饮经理和系统管理员可以删除订单")

    try:
        # 获取请求数据
        data = await request.json()
        order_ids = data.get('order_ids', [])

        if not order_ids:
            raise HTTPException(status_code=400, detail="请选择要删除的订单")

        # 查找所有要删除的订单
        orders = db.query(Order).filter(Order.id.in_(order_ids)).all()

        if not orders:
            raise HTTPException(status_code=404, detail="未找到要删除的订单")

        # 检查所有订单状态
        invalid_orders = []
        for order in orders:
            if order.status.value not in ['completed', 'cancelled']:
                invalid_orders.append(f"{order.order_number}({order.status.value})")

        if invalid_orders:
            raise HTTPException(
                status_code=400,
                detail=f"以下订单不能删除（状态不允许）：{', '.join(invalid_orders)}"
            )

        # 使用事务进行批量删除操作
        try:
            deleted_count = 0
            order_info_list = []

            for order in orders:
                # 记录订单信息用于日志
                order_info = f"{order.order_number}({order.table.number if order.table else '无'})"
                order_info_list.append(order_info)

                # 删除订单项
                db.query(OrderItem).filter(OrderItem.order_id == order.id).delete()

                # 删除订单
                db.delete(order)
                deleted_count += 1

            # 记录批量删除操作日志
            from models.waiter_action import WaiterAction
            log_action = WaiterAction(
                waiter_id=current_user.id,
                room_number='批量操作',
                action_type='batch_delete_orders',
                action_content=f'批量删除 {deleted_count} 个订单：{", ".join(order_info_list)}',
                created_at=get_china_time(),
                is_processed=True  # 操作日志直接标记为已处理
            )
            db.add(log_action)

            # 提交事务
            db.commit()

            print(f"✅ 批量删除订单成功: {deleted_count} 个订单 by {current_user.full_name}")

            return {
                "success": True,
                "message": f"成功删除 {deleted_count} 个订单",
                "deleted_count": deleted_count
            }

        except Exception as e:
            db.rollback()
            print(f"❌ 批量删除订单事务失败: {e}")
            raise e

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 批量删除订单失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量删除订单失败: {str(e)}")


# API: 处理服务员指令（用于打荷操作页面）
@app.post("/api/waiter-actions/{action_id}/process")
async def process_waiter_action_api(
    action_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """处理服务员指令"""
    if not current_user.has_permission('kitchen.view'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        from models.waiter_action import WaiterAction
        action = db.query(WaiterAction).filter(WaiterAction.id == action_id).first()

        if not action:
            raise HTTPException(status_code=404, detail="指令不存在")

        action.is_processed = True
        action.processed_by = current_user.id
        action.processed_at = get_china_time()
        db.commit()

        print(f"✅ 指令已处理: {action.action_type} - {action.action_content}")
        return {"success": True, "message": "指令已处理"}

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ 处理指令失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# API: 菜品完成通知（用于打荷操作页面）
@app.post("/api/kitchen/dish-completion-notification")
async def send_dish_completion_notification(
    request: Request,
    db: Session = Depends(get_db)
):
    """发送菜品完成通知到厨房大屏"""
    # 暂时跳过权限检查，允许打荷员发送通知
    # TODO: 后续可以添加更精确的权限控制

    try:
        data = await request.json()
        message = data.get('message', '')
        room_name = data.get('roomName', '')
        dish_name = data.get('dishName', '')

        print(f"🍽️ 打荷员菜品完成通知: {room_name}{dish_name}，跑菜")

        # 添加通知到全局列表（用于厨房大屏轮询）
        from datetime import datetime
        import time

        # 修复：使用时间戳生成唯一ID，避免重复
        unique_id = int(time.time() * 1000)  # 毫秒级时间戳作为唯一ID

        notification = {
            'id': unique_id,
            'room_number': room_name,
            'dish_name': dish_name,
            'message': message,
            'timestamp': datetime.now().isoformat(),
            'processed': False
        }
        dish_completion_notifications.append(notification)

        # 保持列表大小，只保留最近50条通知
        if len(dish_completion_notifications) > 50:
            dish_completion_notifications.pop(0)

        print(f"📋 添加菜品完成通知: ID={unique_id}, 消息={message}")

        # 通过WebSocket广播到厨房大屏（不发送给打荷员界面）
        try:
            await websocket_manager.broadcast_dish_completion_from_helper(
                room_number=room_name,
                dish_name=dish_name,
                message=message
            )
            print(f"📡 WebSocket通知已发送: {message}")
        except Exception as e:
            print(f"❌ WebSocket通知发送失败: {e}")

        return {"success": True, "message": "通知已发送"}

    except Exception as e:
        print(f"❌ 发送菜品完成通知失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 全局变量存储打荷员菜品完成通知
dish_completion_notifications = []

# API: 获取打荷员菜品完成通知（用于厨房大屏轮询）
@app.get("/api/kitchen/dish-completion-notifications")
async def get_dish_completion_notifications(
    request: Request,
    db: Session = Depends(get_db)
):
    """获取最近的打荷员菜品完成通知"""
    # 暂时跳过权限检查，允许厨房大屏获取通知
    # TODO: 后续可以添加更精确的权限控制

    try:
        # 返回最近5分钟内的通知
        from datetime import datetime, timedelta
        five_minutes_ago = datetime.now() - timedelta(minutes=5)

        recent_notifications = [
            notification for notification in dish_completion_notifications
            if datetime.fromisoformat(notification['timestamp'].replace('Z', '+00:00')) > five_minutes_ago
        ]

        return {
            "success": True,
            "notifications": recent_notifications
        }

    except Exception as e:
        print(f"❌ 获取菜品完成通知失败: {e}")
        return {"success": False, "notifications": []}


# API: 获取指令模板
@app.get("/api/command-templates")
async def get_command_templates_api(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        from models.command_template import CommandTemplate
        templates = db.query(CommandTemplate).filter(
            CommandTemplate.is_active == True
        ).order_by(CommandTemplate.sort_order, CommandTemplate.id).all()

        # 从文件或缓存中读取输入设定配置
        input_configs = load_input_configs()

        result = []
        for template in templates:
            # 优先从数据库获取allow_input设定，如果没有则从配置文件获取
            allow_input = getattr(template, 'allow_input', False)
            input_placeholder = getattr(template, 'input_placeholder', '')
            input_required = getattr(template, 'input_required', False)

            # 如果数据库中没有，则从配置文件获取
            if not allow_input:
                config = input_configs.get(template.code, {})
                allow_input = config.get('allow_input', False)
                input_placeholder = input_placeholder or config.get('input_placeholder', '')
                input_required = input_required or config.get('input_required', False)

            result.append({
                "id": template.id,
                "name": template.name,
                "code": template.code,
                "description": template.description,
                "voice_text": template.voice_text,
                "category": template.category,
                "allow_input": allow_input,
                "input_placeholder": input_placeholder,
                "input_required": input_required
            })

        return result
    except Exception as e:
        print(f"❌ 获取指令模板失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 创建指令模板API
@app.post("/command-templates/create")
async def create_command_template(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission("command.manage"):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        form_data = await request.form()
        name = form_data.get("name")
        code = form_data.get("code")
        description = form_data.get("description")
        voice_text = form_data.get("voice_text")
        category = form_data.get("category", "general")

        # 输入设定暂时不存储到数据库，使用硬编码逻辑
        allow_input = form_data.get("allow_input") == "on"
        input_placeholder = form_data.get("input_placeholder")
        input_required = form_data.get("input_required") == "on"

        # 验证必填字段
        if not all([name, code]):
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "名称和代码是必填的"}
            )

        # 检查代码是否已存在
        from models.command_template import CommandTemplate
        existing_template = db.query(CommandTemplate).filter(CommandTemplate.code == code).first()
        if existing_template:
            return JSONResponse(
                status_code=400,
                content={"success": False, "message": "指令代码已存在"}
            )

        # 创建新指令模板（不包含输入设定字段，因为模型中没有这些字段）
        new_template = CommandTemplate(
            name=name,
            code=code,
            description=description,
            voice_text=voice_text,
            category=category
        )

        db.add(new_template)
        db.commit()
        db.refresh(new_template)

        # 保存输入配置到文件
        if allow_input or input_placeholder or input_required:
            update_input_config(code, allow_input, input_placeholder, input_required)

        print(f"✅ 指令模板创建成功: {code} ({name})")
        print(f"   输入设定: 允许输入={allow_input}, 提示='{input_placeholder}', 必须输入={input_required}")

        return JSONResponse(
            content={"success": True, "message": "指令模板创建成功"}
        )

    except Exception as e:
        db.rollback()
        print(f"❌ 创建指令模板失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"创建指令模板失败: {str(e)}"}
        )


# 更新指令模板API
@app.post("/command-templates/{template_id}/update")
async def update_command_template(
    template_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新指令模板"""
    if not current_user.has_permission("command.manage"):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        data = await request.json()

        from models.command_template import CommandTemplate
        template = db.query(CommandTemplate).filter(CommandTemplate.id == template_id).first()
        if not template:
            return JSONResponse(
                status_code=404,
                content={"success": False, "message": "指令模板不存在"}
            )

        # 更新基本信息
        template.name = data.get("name", template.name)
        template.code = data.get("code", template.code)
        template.description = data.get("description", template.description)
        template.voice_text = data.get("voice_text", template.voice_text)
        template.category = data.get("category", template.category)

        # 更新allow_input字段到数据库
        template.allow_input = data.get("allow_input", False)
        template.input_placeholder = data.get("input_placeholder", "")
        template.input_required = data.get("input_required", False)

        db.commit()

        # 同时更新输入配置文件
        allow_input = data.get("allow_input", False)
        input_placeholder = data.get("input_placeholder", "")
        input_required = data.get("input_required", False)

        if allow_input or input_placeholder or input_required:
            update_input_config(template.code, allow_input, input_placeholder, input_required)

        print(f"✅ 指令模板更新成功: {template.code} ({template.name})")
        print(f"   输入设定: 允许输入={allow_input}, 提示='{input_placeholder}', 必须输入={input_required}")

        return JSONResponse(
            content={"success": True, "message": "指令模板更新成功"}
        )

    except Exception as e:
        db.rollback()
        print(f"❌ 更新指令模板失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"success": False, "message": f"更新指令模板失败: {str(e)}"}
        )


# 取消订单API
@app.post("/orders/{order_id}/cancel")
async def cancel_order(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.has_permission('order.manage'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        order = db.query(Order).filter(Order.id == order_id).first()
        if not order:
            raise HTTPException(status_code=404, detail="订单不存在")

        # 检查订单是否可以取消
        if order.status in [OrderStatus.COMPLETED, OrderStatus.CANCELLED]:
            raise HTTPException(status_code=400, detail="订单已完成或已取消，无法取消")

        # 取消订单
        order.status = OrderStatus.CANCELLED
        order.updated_at = get_china_time()

        # 释放餐桌
        if order.table:
            order.table.status = TableStatus.AVAILABLE
            order.table.current_guests = 0
            order.table.assigned_waiter_id = None

        db.commit()

        print(f"✅ 订单已取消: {order.id}")

        return {"success": True, "message": "订单已取消"}

    except Exception as e:
        db.rollback()
        print(f"❌ 取消订单失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 语音播报配置API
@app.get("/api/voice-config")
async def get_voice_config(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取语音播报配置"""
    try:
        from models.voice_config import VoiceConfig
        configs = db.query(VoiceConfig).filter(VoiceConfig.is_active == True).all()

        result = {}
        for config in configs:
            if config.config_key in ['voice_enabled']:
                result[config.config_key] = config.config_value.lower() == 'true'
            elif config.config_key in ['voice_repeat_count']:
                result[config.config_key] = int(config.config_value)
            elif config.config_key in ['voice_repeat_interval']:
                result[config.config_key] = int(config.config_value)
            elif config.config_key in ['voice_rate', 'voice_volume', 'voice_pitch']:
                result[config.config_key] = float(config.config_value)
            else:
                result[config.config_key] = config.config_value

        # 设置默认值
        default_config = {
            'voice_enabled': True,
            'voice_repeat_count': 2,
            'voice_repeat_interval': 3,
            'voice_rate': 0.8,
            'voice_volume': 1.0,
            'voice_pitch': 1.0
        }

        for key, default_value in default_config.items():
            if key not in result:
                result[key] = default_value

        return result
    except Exception as e:
        print(f"❌ 获取语音配置失败: {e}")
        return {
            'voice_enabled': True,
            'voice_repeat_count': 2,
            'voice_repeat_interval': 3,
            'voice_rate': 0.8,
            'voice_volume': 1.0,
            'voice_pitch': 1.0
        }


@app.post("/api/voice-config")
async def update_voice_config(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新语音播报配置"""
    if not current_user.has_permission('system.config'):
        raise HTTPException(status_code=403, detail="权限不足，只有管理员可以修改语音配置")

    try:
        data = await request.json()
        from models.voice_config import VoiceConfig

        # 更新配置
        for key, value in data.items():
            if key in ['voice_enabled', 'voice_repeat_count', 'voice_repeat_interval',
                      'voice_rate', 'voice_volume', 'voice_pitch']:
                config = db.query(VoiceConfig).filter(VoiceConfig.config_key == key).first()
                if config:
                    config.config_value = str(value)
                else:
                    # 创建新配置
                    config = VoiceConfig(
                        config_key=key,
                        config_value=str(value),
                        description=f"语音播报配置: {key}"
                    )
                    db.add(config)

        db.commit()
        print(f"✅ 语音配置更新成功: {data}")

        return {"success": True, "message": "语音配置更新成功"}

    except Exception as e:
        db.rollback()
        print(f"❌ 更新语音配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# 语音播报设置页面
@app.get("/voice-settings", response_class=HTMLResponse)
async def voice_settings_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """语音播报设置页面"""
    if not current_user.has_permission('system.config'):
        raise HTTPException(status_code=403, detail="权限不足，只有管理员可以访问语音设置")

    return templates.TemplateResponse(
        "voice_settings.html",
        {
            "request": request,
            "user": current_user
        }
    )


# 系统设置页面
@app.get("/system-settings", response_class=HTMLResponse)
async def system_settings_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """系统设置页面"""
    if not current_user.has_permission('system.config'):
        raise HTTPException(status_code=403, detail="权限不足，只有管理员可以访问系统设置")

    return templates.TemplateResponse(
        "system_settings.html",
        {
            "request": request,
            "user": current_user
        }
    )


# 获取系统配置API
@app.get("/api/system-config")
async def get_system_config(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取系统配置"""
    # 服务员可以读取基本配置，但不能修改
    # 只有管理员可以读取所有配置
    is_admin = current_user.has_permission('system.config')
    is_waiter = current_user.role == UserRole.WAITER

    # 允许服务员和管理员访问，其他角色拒绝
    if not (is_admin or is_waiter):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        configs = db.query(SystemConfigModel).filter(SystemConfigModel.is_active == True).all()

        # 转换为字典格式
        result = {}
        for config in configs:
            # 如果是服务员，只返回服务员相关的配置
            if is_waiter and not is_admin:
                if config.config_key.startswith('waiter_') or config.config_key in ['system_timezone']:
                    result[config.config_key] = config.typed_value
            else:
                # 管理员可以看到所有配置
                result[config.config_key] = config.typed_value

        # 确保所有默认配置都存在
        default_config = {
            'waiter_auto_refresh_interval': 15,
            'waiter_auto_refresh_enabled': True,
            'kitchen_display_auto_flip_interval': 8,
            'system_timezone': 'Asia/Shanghai'
        }

        # 如果是服务员，只提供服务员相关的默认配置
        if is_waiter and not is_admin:
            waiter_default_config = {
                'waiter_auto_refresh_interval': 15,
                'waiter_auto_refresh_enabled': True,
                'system_timezone': 'Asia/Shanghai'
            }
            for key, default_value in waiter_default_config.items():
                if key not in result:
                    result[key] = default_value
        else:
            # 管理员获取所有默认配置
            for key, default_value in default_config.items():
                if key not in result:
                    result[key] = default_value

        return result
    except Exception as e:
        print(f"❌ 获取系统配置失败: {e}")
        # 根据用户角色返回不同的默认配置
        if is_waiter and not is_admin:
            return {
                'waiter_auto_refresh_interval': 15,
                'waiter_auto_refresh_enabled': True,
                'system_timezone': 'Asia/Shanghai'
            }
        else:
            return {
                'waiter_auto_refresh_interval': 15,
                'waiter_auto_refresh_enabled': True,
                'kitchen_display_auto_flip_interval': 8,
                'system_timezone': 'Asia/Shanghai'
            }


# 更新系统配置API
@app.post("/api/system-config")
async def update_system_config(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新系统配置"""
    if not current_user.has_permission('system.config'):
        raise HTTPException(status_code=403, detail="权限不足，只有管理员可以修改系统配置")

    try:
        data = await request.json()

        for key, value in data.items():
            # 查找现有配置
            config = db.query(SystemConfigModel).filter(SystemConfigModel.config_key == key).first()

            if config:
                # 更新现有配置
                config.config_value = str(value)
            else:
                # 创建新配置
                # 确定配置类型
                config_type = "string"
                category = "general"

                if isinstance(value, bool):
                    config_type = "bool"
                elif isinstance(value, int):
                    config_type = "int"
                elif isinstance(value, float):
                    config_type = "float"

                # 根据键名确定分类
                if key.startswith('waiter_'):
                    category = "waiter"
                elif key.startswith('kitchen_display_'):
                    category = "kitchen_display"
                elif key.startswith('kitchen_helper_'):
                    category = "kitchen_helper"
                elif key.startswith('kitchen_'):
                    category = "kitchen"
                elif key.startswith('mobile_'):
                    category = "mobile"
                elif key.startswith('order_'):
                    category = "order"

                config = SystemConfigModel(
                    config_key=key,
                    config_value=str(value),
                    config_type=config_type,
                    category=category,
                    description=f"系统配置: {key}",
                    is_active=True
                )
                db.add(config)

        db.commit()
        return {"success": True, "message": "系统配置更新成功"}

    except Exception as e:
        db.rollback()
        print(f"❌ 更新系统配置失败: {e}")
        return {"success": False, "message": f"更新失败: {str(e)}"}

# 获取厨房大屏配置API
@app.get("/api/kitchen-display-config")
async def get_kitchen_display_config(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取厨房大屏配置"""
    try:
        # 获取厨房大屏相关配置
        configs = db.query(SystemConfigModel).filter(
            SystemConfigModel.category == "kitchen_display",
            SystemConfigModel.is_active == True
        ).all()

        result = {}
        for config in configs:
            result[config.config_key] = config.typed_value

        # 设置默认值
        defaults = {
            'kitchen_display_rooms_per_page': 5,
            'kitchen_display_font_size': 14,
            'kitchen_display_layout_mode': 'auto',
            'kitchen_display_grid_columns': 3,
            'kitchen_display_grid_rows': 2,
            'kitchen_display_auto_flip_interval': 8,
            'kitchen_display_auto_flip_enabled': True,
            'kitchen_display_hide_navigation': True,
            'kitchen_display_room_width': 370,
            'kitchen_display_auto_font_size': True
        }

        for key, default_value in defaults.items():
            if key not in result:
                result[key] = default_value

        return result
    except Exception as e:
        print(f"❌ 获取厨房大屏配置失败: {e}")
        # 返回默认配置
        return {
            'kitchen_display_rooms_per_page': 5,
            'kitchen_display_font_size': 14,
            'kitchen_display_layout_mode': 'auto',
            'kitchen_display_grid_columns': 3,
            'kitchen_display_grid_rows': 2,
            'kitchen_display_auto_flip_interval': 8,
            'kitchen_display_auto_flip_enabled': True,
            'kitchen_display_hide_navigation': True,
            'kitchen_display_room_width': 370,
            'kitchen_display_auto_font_size': True
        }


# 获取打荷页面配置API
@app.get("/api/kitchen-helper-config")
async def get_kitchen_helper_config(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取打荷页面配置"""
    try:
        # 获取打荷页面相关配置
        configs = db.query(SystemConfigModel).filter(
            SystemConfigModel.category == "kitchen_helper",
            SystemConfigModel.is_active == True
        ).all()

        result = {}
        for config in configs:
            result[config.config_key] = config.typed_value

        # 确保所有默认配置都存在
        defaults = {
            'kitchen_helper_font_size': 16,
            'kitchen_helper_font_color': '#ffffff',
            'kitchen_helper_theme': 'dark',
            'kitchen_helper_border_size': 2,
            'kitchen_helper_container_width': 'auto',
            'kitchen_helper_padding_top': 8,
            'kitchen_helper_padding_bottom': 8,
            'kitchen_helper_padding_left': 12,
            'kitchen_helper_padding_right': 12,
            'kitchen_helper_rooms_per_row': 5,
            'kitchen_helper_room_gap': 10
        }

        for key, default_value in defaults.items():
            if key not in result:
                result[key] = default_value

        return result
    except Exception as e:
        print(f"❌ 获取打荷页面配置失败: {e}")
        return {
            'kitchen_helper_font_size': 16,
            'kitchen_helper_font_color': '#ffffff',
            'kitchen_helper_theme': 'dark',
            'kitchen_helper_border_size': 2,
            'kitchen_helper_container_width': 'auto',
            'kitchen_helper_padding_top': 8,
            'kitchen_helper_padding_bottom': 8,
            'kitchen_helper_padding_left': 12,
            'kitchen_helper_padding_right': 12,
            'kitchen_helper_rooms_per_row': 5,
            'kitchen_helper_room_gap': 10
        }


# 同步人数到所有界面API
@app.post("/api/sync-guest-count")
async def sync_guest_count(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """同步人数到所有相关界面"""
    try:
        data = await request.json()
        room_number = data.get('room_number')
        guest_count = data.get('guest_count')

        if not room_number or guest_count is None:
            return {"success": False, "message": "缺少必要参数"}

        # 查找包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            return {"success": False, "message": "包厢不存在"}

        # 更新该包厢的所有活跃订单的人数信息
        active_orders = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING,
                OrderStatus.IN_PROGRESS,
                OrderStatus.CONFIRMED
            ])
        ).all()

        for order in active_orders:
            order.guest_count = guest_count
            print(f"📋 更新订单 {order.order_number} 人数: {guest_count}人")

        db.commit()

        # 通过WebSocket广播人数更新事件
        await websocket_manager.broadcast_to_all({
            "type": "guest_count_updated",
            "room_number": room_number,
            "guest_count": guest_count,
            "timestamp": get_china_time().isoformat()
        })

        return {"success": True, "message": "人数信息已同步"}

    except Exception as e:
        db.rollback()
        print(f"❌ 同步人数失败: {e}")
        return {"success": False, "message": f"同步失败: {str(e)}"}


# 广播语音配置更新API
@app.post("/api/broadcast-voice-config-update")
async def broadcast_voice_config_update(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """广播语音配置更新事件"""
    if not current_user.has_permission('system.config'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 通过WebSocket广播配置更新事件
        await websocket_manager.broadcast_to_all({
            "type": "voice_config_updated",
            "message": "语音配置已更新，请重新加载设置",
            "timestamp": get_china_time().isoformat()
        })

        return {"success": True, "message": "配置更新已广播"}

    except Exception as e:
        print(f"❌ 广播配置更新失败: {e}")
        return {"success": False, "message": f"广播失败: {str(e)}"}








# 强制重置包厢状态API
@app.post("/api/force-reset-room/{room_number}")
async def force_reset_room(
    room_number: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """强制重置包厢状态"""
    if not current_user.has_permission('table.manage'):
        raise HTTPException(status_code=403, detail="权限不足")

    try:
        # 查找包厢
        table = db.query(Table).filter(Table.number == room_number).first()
        if not table:
            return {"success": False, "message": "包厢不存在"}

        # 查找该包厢的所有活跃订单
        active_orders = db.query(Order).filter(
            Order.table_id == table.id,
            Order.status.in_([
                OrderStatus.RESERVED,
                OrderStatus.PENDING_START,
                OrderStatus.SERVING,
                OrderStatus.IN_PROGRESS,
                OrderStatus.CONFIRMED
            ])
        ).all()

        reset_count = 0
        for order in active_orders:
            # 清除服务员授权
            if order.waiter_id:
                waiter = db.query(User).filter(User.id == order.waiter_id).first()
                if waiter:
                    waiter.assigned_tables = None
                    waiter.is_authorized = False

            # 更新订单状态
            order.status = OrderStatus.CANCELLED
            order.waiter_id = None
            order.dining_start_time = None

            # 取消订单项
            for item in order.items:
                if item.status not in ['served', 'cancelled']:
                    item.status = 'cancelled'

            reset_count += 1

        # 重置包厢状态
        table.status = TableStatus.AVAILABLE
        table.assigned_waiter_id = None

        db.commit()

        print(f"🔧 管理员 {current_user.username} 强制重置包厢 {room_number}")

        return {
            "success": True,
            "message": f"包厢 {room_number} 状态已强制重置，清理了 {reset_count} 个订单",
            "reset_count": reset_count
        }

    except Exception as e:
        db.rollback()
        print(f"❌ 强制重置包厢状态失败: {e}")
        return {"success": False, "message": f"重置失败: {str(e)}"}


# 系统初始化API
@app.post("/api/system-reset")
async def system_reset(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """系统初始化 - 清除所有业务数据"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="只有系统管理员可以执行系统初始化")

    try:
        print("🔄 开始系统初始化...")

        # 1. 删除所有订单项
        deleted_items = db.query(OrderItem).delete()
        print(f"✅ 清理订单项: {deleted_items} 条")

        # 2. 删除所有订单
        deleted_orders = db.query(Order).delete()
        print(f"✅ 清理订单: {deleted_orders} 条")

        # 3. 删除所有包厢
        deleted_tables = db.query(Table).delete()
        print(f"✅ 清理包厢: {deleted_tables} 个")

        # 4. 删除所有用户（除admin外）
        deleted_users = db.query(User).filter(User.username != 'admin').delete()
        print(f"✅ 清理用户: {deleted_users} 个")

        # 5. 删除所有指令模板
        try:
            from models.command_template import CommandTemplate
            deleted_commands = db.query(CommandTemplate).delete()
            print(f"✅ 清理指令模板: {deleted_commands} 个")
        except Exception:
            print("⚠️ 指令模板表不存在，跳过")

        # 6. 删除所有操作记录
        try:
            from models.waiter_action import WaiterAction
            deleted_actions = db.query(WaiterAction).delete()
            print(f"✅ 清理操作记录: {deleted_actions} 条")
        except Exception:
            print("⚠️ 操作记录表不存在，跳过")

        # 7. 重置自增ID序列
        from sqlalchemy import text
        db.execute(text("DELETE FROM sqlite_sequence WHERE name IN ('orders', 'order_items', 'tables', 'users', 'command_templates', 'waiter_actions')"))
        print("✅ 重置自增ID序列")

        # 8. 重置admin用户状态
        admin_user = db.query(User).filter(User.username == 'admin').first()
        if admin_user:
            admin_user.is_authorized = False
            admin_user.assigned_tables = None
            print("✅ 重置admin用户状态")

        db.commit()

        print("🎉 系统初始化完成！")

        return {
            "success": True,
            "message": "系统初始化成功！所有业务数据已清除，系统已恢复到全新状态。",
            "details": {
                "deleted_items": deleted_items,
                "deleted_orders": deleted_orders,
                "deleted_tables": deleted_tables,
                "deleted_users": deleted_users
            }
        }

    except Exception as e:
        db.rollback()
        print(f"❌ 系统初始化失败: {e}")
        return {"success": False, "message": f"系统初始化失败: {str(e)}"}


# 订单详情API
@app.get("/api/orders/{order_id}/details")
async def get_order_details(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取订单详情"""
    try:
        # 获取订单详细信息
        from sqlalchemy.orm import joinedload
        order = db.query(Order).options(
            joinedload(Order.order_items),
            joinedload(Order.table),
            joinedload(Order.waiter)
        ).filter(Order.id == order_id).first()

        if not order:
            return {"success": False, "message": "订单不存在"}

        # 检查权限
        if not (current_user.has_permission("order.view") or
                current_user.has_permission("order.manage") or
                order.waiter_id == current_user.id):
            return {"success": False, "message": "权限不足"}

        # 构建订单数据
        order_data = {
            "id": order.id,
            "order_number": order.order_number,
            "customer_name": order.customer_name,
            "customer_phone": order.customer_phone,
            "guest_count": order.guest_count,
            "dining_standard_amount": float(order.dining_standard_amount) if order.dining_standard_amount else 0,
            "total_amount": float(order.total_amount) if order.total_amount else 0,
            "subtotal": float(order.subtotal) if order.subtotal else 0,
            "service_charge": float(order.service_charge) if order.service_charge else 0,
            "discount_amount": float(order.discount_amount) if order.discount_amount else 0,
            "status": order.status.value if order.status else "reserved",
            "meal_period": order.meal_period.value if order.meal_period else "dinner",
            "special_requests": order.special_requests,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "started_at": order.started_at.isoformat() if order.started_at else None,
            "completed_at": getattr(order, 'completed_at', None),
            "table": {
                "id": order.table.id,
                "number": order.table.number
            } if order.table else None,
            "waiter": {
                "id": order.waiter.id,
                "full_name": order.waiter.full_name,
                "username": order.waiter.username
            } if order.waiter else None,
            "items": []
        }

        # 添加订单项数据
        if order.order_items:
            for item in order.order_items:
                item_data = {
                    "id": item.id,
                    "dish_name": item.dish_name,
                    "quantity": item.quantity,
                    "status": item.status.value if item.status else "pending",
                    "special_requirements": getattr(item, 'special_requirements', ''),
                    "kitchen_notes": getattr(item, 'kitchen_notes', '')
                }
                order_data["items"].append(item_data)

        return {
            "success": True,
            "order": order_data
        }

    except Exception as e:
        print(f"❌ 获取订单详情失败: {e}")
        return {"success": False, "message": f"获取订单详情失败: {str(e)}"}


# 订单状态检查API
@app.get("/api/orders/{order_id}/status")
async def get_order_status(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取订单状态，用于编辑权限检查"""
    try:
        order = db.query(Order).filter(Order.id == order_id).first()
        if not order:
            return {"success": False, "message": "订单不存在"}

        return {
            "success": True,
            "status": order.status.value,
            "order_id": order.id
        }
    except Exception as e:
        print(f"❌ 获取订单状态失败: {e}")
        return {"success": False, "message": f"获取订单状态失败: {str(e)}"}


# 订单打印数据API
@app.get("/api/orders/{order_id}/print-data")
async def get_order_print_data(
    order_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取订单打印数据"""
    try:
        # 获取订单详细信息
        order = db.query(Order).filter(Order.id == order_id).first()
        if not order:
            return {"success": False, "message": "订单不存在"}

        # 检查权限
        if not (current_user.has_permission("order.view") or
                current_user.has_permission("order.manage") or
                order.waiter_id == current_user.id):
            return {"success": False, "message": "权限不足"}

        # 获取订单项
        order_items = db.query(OrderItem).filter(OrderItem.order_id == order_id).all()

        # 获取包厢信息
        table_info = None
        if order.table_id:
            table = db.query(Table).filter(Table.id == order.table_id).first()
            if table:
                table_info = {
                    "id": table.id,
                    "number": table.number,
                    "name": table.name
                }

        # 获取服务员信息
        waiter_info = None
        if order.waiter_id:
            waiter = db.query(User).filter(User.id == order.waiter_id).first()
            if waiter:
                waiter_info = {
                    "id": waiter.id,
                    "full_name": waiter.full_name,
                    "username": waiter.username
                }

        # 构建订单数据
        order_data = {
            "id": order.id,
            "order_number": getattr(order, 'order_number', f"ORD{order.id:06d}"),
            "table": table_info,
            "guest_count": order.guest_count,
            "customer_name": order.customer_name,
            "customer_phone": order.customer_phone,
            "dining_standard_amount": float(order.dining_standard_amount or 0),
            "total_amount": float(order.total_amount or 0),
            "subtotal": float(getattr(order, 'subtotal', order.total_amount or 0)),
            "service_charge": float(getattr(order, 'service_charge', 0)),
            "discount_amount": float(getattr(order, 'discount_amount', 0)),
            "special_requests": order.special_requests,
            "status": order.status.value if order.status else "RESERVED",
            "waiter": waiter_info,
            "created_at": order.created_at.isoformat() if order.created_at else None,
            "started_at": order.started_at.isoformat() if order.started_at else None,
            "completed_at": getattr(order, 'completed_at', None),
            "items": []
        }

        # 添加订单项数据
        for item in order_items:
            item_data = {
                "id": item.id,
                "dish_name": item.dish_name,
                "quantity": item.quantity,
                "status": item.status.value if item.status else "PENDING",
                "special_requirements": getattr(item, 'special_requirements', ''),
                "kitchen_notes": getattr(item, 'kitchen_notes', '')
            }
            order_data["items"].append(item_data)

        return {
            "success": True,
            "order": order_data
        }

    except Exception as e:
        print(f"❌ 获取订单打印数据失败: {e}")
        return {"success": False, "message": f"获取订单数据失败: {str(e)}"}


# 离线状态检查页面
@app.get("/offline-status", response_class=HTMLResponse)
async def offline_status_page(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """离线运行状态检查页面"""
    return templates.TemplateResponse(
        "offline_status.html",
        {"request": request, "user": current_user}
    )


# API测试页面
@app.get("/test-api", response_class=HTMLResponse)
async def test_api_page(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """API功能测试页面"""
    return templates.TemplateResponse(
        "test_api.html",
        {"request": request, "user": current_user}
    )


# 权限测试API
@app.get("/api/test-permissions")
async def test_permissions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """测试当前用户的权限"""
    permissions_to_test = [
        'dish.cook', 'dish.prepare', 'dish.serve', 'kitchen.view',
        'kitchen.manage', 'dish.mark_done', 'system.config'
    ]

    result = {
        "user": {
            "username": current_user.username,
            "full_name": current_user.full_name,
            "role": current_user.role.value if hasattr(current_user.role, 'value') else str(current_user.role),
            "is_superuser": current_user.is_superuser
        },
        "permissions": {}
    }

    for permission in permissions_to_test:
        result["permissions"][permission] = current_user.has_permission(permission)

    return result


if __name__ == "__main__":
    import uvicorn
    import sys
    import locale
    import argparse
    from config import ServerConfig

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='暨阳湖大酒店传菜管理系统')
    parser.add_argument('--port', '-p', type=int, help='指定服务端口号')
    parser.add_argument('--host', type=str, help='指定服务主机地址')
    parser.add_argument('--create-config', action='store_true', help='创建默认配置文件')
    args = parser.parse_args()

    # 如果用户要求创建配置文件
    if args.create_config:
        ServerConfig.create_default_config()
        from config import create_default_env
        create_default_env()
        print("✅ 配置文件创建完成")
        exit(0)

    # 端口优先级：命令行参数 > 环境变量 > 配置文件 > 默认值
    port = args.port or ServerConfig.get_port()
    host = args.host or ServerConfig.get_host()

    # 端口有效性验证
    if not ServerConfig.validate_port(port):
        print(f"❌ 错误：端口号 {port} 无效，请使用 1024-65535 范围内的端口")
        exit(1)

    # 设置控制台编码为UTF-8
    try:
        # 设置系统编码
        if sys.platform.startswith('win'):
            os.system('chcp 65001')  # Windows设置UTF-8编码

        # 设置locale
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            try:
                locale.setlocale(locale.LC_ALL, 'C.UTF-8')
            except:
                pass

        # 确保stdout使用UTF-8编码
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8')
        elif hasattr(sys.stdout, 'buffer'):
            import io
            sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

    except Exception as e:
        print(f"Warning: Could not set UTF-8 encoding: {e}")

    print("🚀 启动暨阳湖大酒店传菜管理系统...")
    print(f"🌐 访问地址: http://localhost:{port}")
    print(f"📚 API 文档: http://localhost:{port}/docs")
    print("按 Ctrl+C 停止服务")
    print("=" * 50)

    try:
        uvicorn.run(app, host=host, port=port, reload=False)
    except OSError as e:
        if "address already in use" in str(e).lower():
            print(f"\n❌ 错误：端口 {port} 已被占用")
            print(f"💡 解决方案：")
            print(f"   1. 使用其他端口：python main.py --port {port + 1}")
            print(f"   2. 杀死占用进程：python port_manager.py --kill {port}")
            print(f"   3. 使用启动脚本自动处理端口冲突")
            print(f"   4. 查看端口占用情况：python port_manager.py --info {port}")
        else:
            print(f"❌ 启动失败：{e}")
        exit(1)
    except KeyboardInterrupt:
        print(f"\n👋 用户停止服务")
        exit(0)
    except Exception as e:
        print(f"❌ 未知错误：{e}")
        exit(1)
