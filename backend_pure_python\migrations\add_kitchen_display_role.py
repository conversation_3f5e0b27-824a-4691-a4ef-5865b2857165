#!/usr/bin/env python3
"""
数据库迁移脚本：添加厨房大屏专用角色
创建时间：2025-06-30
目的：为厨房大屏权限重构添加新的KITCHEN_DISPLAY角色
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from core.database import get_db_url
from models.user import User, UserRole, UserStatus
import bcrypt

def hash_password(password: str) -> str:
    """密码哈希"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def main():
    """执行迁移"""
    print("🚀 开始执行厨房大屏角色迁移...")
    
    # 创建数据库连接
    engine = create_engine(get_db_url())
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 1. 检查是否已经有KITCHEN_DISPLAY角色的用户
        existing_users = db.query(User).filter(User.role == UserRole.KITCHEN_DISPLAY).all()
        if existing_users:
            print(f"✅ 已存在 {len(existing_users)} 个厨房大屏用户，跳过创建")
        else:
            # 2. 创建默认的厨房大屏用户
            kitchen_display_user = User(
                username="kitchen_display",
                full_name="厨房大屏",
                password_hash=hash_password("kitchen123"),  # 默认密码
                role=UserRole.KITCHEN_DISPLAY,
                status=UserStatus.ACTIVE,
                is_active=True,
                is_authorized=True,
                employee_id="KD001",
                department="厨房",
                position="厨房大屏操作员"
            )
            
            db.add(kitchen_display_user)
            print("✅ 创建厨房大屏用户: kitchen_display (密码: kitchen123)")
        
        # 3. 检查现有用户的角色，提供迁移建议
        print("\n📊 现有用户角色分析:")
        
        # 统计各角色用户数量
        role_counts = {}
        all_users = db.query(User).all()
        for user in all_users:
            role = user.role.value if hasattr(user.role, 'value') else str(user.role)
            role_counts[role] = role_counts.get(role, 0) + 1
        
        for role, count in role_counts.items():
            print(f"  - {role}: {count} 个用户")
        
        # 4. 提供迁移建议
        print("\n💡 迁移建议:")
        print("1. 如果有专门负责厨房大屏的人员，请将其角色改为 'kitchen_display'")
        print("2. 厨师长(chef_manager)和打荷员(kitchen_helper)将失去厨房大屏访问权限")
        print("3. 商务中心(business_center)将失去厨房大屏访问权限")
        print("4. 只有管理员(admin)和厨房大屏用户(kitchen_display)可以访问厨房大屏")
        
        # 5. 显示需要手动调整的用户
        users_with_kitchen_access = db.query(User).filter(
            User.role.in_([UserRole.CHEF_MANAGER, UserRole.KITCHEN_HELPER, UserRole.BUSINESS_CENTER])
        ).all()
        
        if users_with_kitchen_access:
            print(f"\n⚠️  以下 {len(users_with_kitchen_access)} 个用户将失去厨房大屏访问权限:")
            for user in users_with_kitchen_access:
                role_name = user.role.value if hasattr(user.role, 'value') else str(user.role)
                print(f"  - {user.username} ({user.full_name}) - {role_name}")
            
            print("\n如需保留厨房大屏访问权限，请手动将相关用户角色改为 'kitchen_display'")
        
        # 提交更改
        db.commit()
        print("\n✅ 迁移完成！")
        
        # 6. 显示新用户登录信息
        print("\n🔑 厨房大屏用户登录信息:")
        print("  用户名: kitchen_display")
        print("  密码: kitchen123")
        print("  访问地址: /kitchen/display")
        print("\n⚠️  请及时修改默认密码！")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 迁移失败: {e}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    main()
