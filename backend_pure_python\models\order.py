from sqlalchemy import Column, Integer, String, Boolean, DateTime, Enum, Text, Numeric, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import enum
from core.database import Base


class OrderStatus(str, enum.Enum):
    """订单状态枚举 - 优化后的状态定义"""
    RESERVED = "reserved"              # 已预订（商务中心创建订单后，尚未指派服务员）
    PENDING_START = "pending_start"    # 待开始（已指派服务员，但服务员尚未发送"用餐开始"指令）
    SERVING = "serving"                # 已上菜（服务员已发送"用餐开始"指令，用餐已开始）
    COMPLETED = "completed"            # 已完成（用餐结束）
    CANCELLED = "cancelled"            # 已取消

    # 保留原有状态以兼容现有数据
    DRAFT = "draft"                    # 草稿（兼容）
    PENDING_KITCHEN = "pending_kitchen"  # 待厨房确认（兼容）
    CONFIRMED = "confirmed"            # 已确认（兼容，等同于RESERVED）
    IN_PROGRESS = "in_progress"        # 进行中（兼容，等同于SERVING）
    PENDING_PAYMENT = "pending_payment"  # 待结算（兼容）
    PAID = "paid"                      # 已结账（兼容）


class OrderType(str, enum.Enum):
    """订单类型枚举"""
    DINE_IN = "dine_in"        # 堂食
    TAKEAWAY = "takeaway"      # 外带
    DELIVERY = "delivery"      # 外送
    RESERVATION = "reservation"  # 预订


class PaymentMethod(str, enum.Enum):
    """支付方式枚举"""
    CASH = "cash"              # 现金
    CARD = "card"              # 刷卡
    WECHAT = "wechat"          # 微信支付
    ALIPAY = "alipay"          # 支付宝
    MEMBER_CARD = "member_card"  # 会员卡
    VOUCHER = "voucher"        # 代金券


class MealPeriod(str, enum.Enum):
    """用餐时段枚举"""
    BREAKFAST = "breakfast"    # 早餐
    LUNCH = "lunch"           # 午餐
    DINNER = "dinner"         # 晚餐


class DishItemStatus(str, enum.Enum):
    """菜品项状态枚举 - 简化版本"""
    PENDING_COOK = "pending_cook"        # 待制作（初始状态）
    COOKING = "cooking"                  # 制作中
    READY = "ready"                      # 制作完成（最终状态）
    CANCELLED = "cancelled"              # 已取消


class Order(Base):
    """订单模型"""
    __tablename__ = "orders"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 订单基本信息
    order_number = Column(String(50), unique=True, index=True, nullable=False, comment="订单号")
    order_type = Column(Enum(OrderType), nullable=False, default=OrderType.DINE_IN, comment="订单类型")
    status = Column(Enum(OrderStatus), nullable=False, default=OrderStatus.DRAFT, comment="订单状态")
    meal_period = Column(Enum(MealPeriod), nullable=False, default=MealPeriod.DINNER, comment="用餐时段")
    
    # 关联信息
    table_id = Column(Integer, ForeignKey("tables.id"), nullable=True, comment="餐桌ID")
    customer_id = Column(Integer, nullable=True, comment="客户ID")
    waiter_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="服务员ID")
    
    # 客户信息
    customer_name = Column(String(100), nullable=True, comment="客户姓名")
    customer_phone = Column(String(20), nullable=True, comment="客户电话")
    guest_count = Column(Integer, nullable=False, default=1, comment="用餐人数")
    
    # 预订信息
    reservation_time = Column(DateTime(timezone=True), nullable=True, comment="预订时间")
    estimated_duration = Column(Integer, nullable=True, comment="预计用餐时长(分钟)")
    
    # 金额信息
    subtotal = Column(Numeric(10, 2), nullable=False, default=0, comment="小计")
    service_charge = Column(Numeric(10, 2), nullable=False, default=0, comment="服务费")
    discount_amount = Column(Numeric(10, 2), nullable=False, default=0, comment="折扣金额")
    total_amount = Column(Numeric(10, 2), nullable=False, default=0, comment="总金额")
    dining_standard_amount = Column(Numeric(10, 2), nullable=True, comment="餐标金额")
    paid_amount = Column(Numeric(10, 2), nullable=False, default=0, comment="已付金额")
    
    # 支付信息
    payment_method = Column(Enum(PaymentMethod), nullable=True, comment="支付方式")
    payment_time = Column(DateTime(timezone=True), nullable=True, comment="支付时间")
    payment_reference = Column(String(100), nullable=True, comment="支付凭证号")
    
    # 折扣信息
    discount_type = Column(String(50), nullable=True, comment="折扣类型")
    discount_rate = Column(Numeric(5, 2), nullable=True, comment="折扣率")
    coupon_code = Column(String(50), nullable=True, comment="优惠券代码")
    
    # 特殊要求
    special_requests = Column(Text, nullable=True, comment="特殊要求")
    notes = Column(Text, nullable=True, comment="备注")
    
    # 时间信息
    ordered_at = Column(DateTime(timezone=True), nullable=True, comment="下单时间")
    confirmed_at = Column(DateTime(timezone=True), nullable=True, comment="确认时间")
    dining_start_time = Column(DateTime(timezone=True), nullable=True, comment="用餐开始时间")
    dining_end_time = Column(DateTime(timezone=True), nullable=True, comment="用餐结束时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始制作时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    table = relationship("Table", back_populates="orders")
    waiter = relationship("User", foreign_keys=[waiter_id])
    order_items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Order(id={self.id}, number='{self.order_number}', status='{self.status}')>"
    
    @property
    def is_editable(self) -> bool:
        """是否可编辑"""
        return self.status in [
            OrderStatus.DRAFT,
            OrderStatus.PENDING_KITCHEN,
            OrderStatus.CONFIRMED,
            OrderStatus.RESERVED,
            OrderStatus.PENDING_START
        ]
    
    @property
    def can_cancel(self) -> bool:
        """是否可取消"""
        return self.status not in [OrderStatus.PAID, OrderStatus.COMPLETED, OrderStatus.CANCELLED]
    
    def calculate_total(self):
        """计算订单总金额"""
        self.subtotal = sum(item.total_price for item in self.order_items if item.status != DishItemStatus.CANCELLED)
        self.total_amount = self.subtotal + self.service_charge - self.discount_amount

    def get_status_display(self) -> str:
        """获取状态显示名称 - 简化版本"""
        # 简化状态显示，只保留2个主要状态
        if self.status in [OrderStatus.RESERVED, OrderStatus.CONFIRMED, OrderStatus.DRAFT, OrderStatus.PENDING_KITCHEN]:
            return "已预订"
        elif self.status in [OrderStatus.SERVING, OrderStatus.IN_PROGRESS, OrderStatus.PENDING_START]:
            return "已上菜"
        elif self.status == OrderStatus.COMPLETED:
            return "已完成"
        elif self.status == OrderStatus.CANCELLED:
            return "已取消"
        else:
            return "已预订"  # 默认状态

    def get_meal_period_display(self) -> str:
        """获取用餐时段显示名称"""
        meal_period_map = {
            MealPeriod.BREAKFAST: "早餐",
            MealPeriod.LUNCH: "午餐",
            MealPeriod.DINNER: "晚餐"
        }
        return meal_period_map.get(self.meal_period, "晚餐")

    def can_assign_waiter(self) -> bool:
        """是否可以分配服务员"""
        return self.status in [OrderStatus.RESERVED, OrderStatus.CONFIRMED] and not self.waiter_id

    def can_start_dining(self) -> bool:
        """是否可以开始用餐"""
        return self.status == OrderStatus.PENDING_START and bool(self.waiter_id)

    def assign_waiter(self, waiter_id: int):
        """分配服务员"""
        if self.can_assign_waiter():
            self.waiter_id = waiter_id
            self.status = OrderStatus.PENDING_START
            return True
        return False

    def start_dining(self):
        """开始用餐"""
        if self.can_start_dining():
            self.status = OrderStatus.SERVING
            if not self.dining_start_time:
                from datetime import datetime
                from pytz import timezone
                CHINA_TZ = timezone('Asia/Shanghai')
                self.dining_start_time = datetime.now(CHINA_TZ)
            return True
        return False

    def get_dish_count(self):
        """获取菜品数量"""
        return len([item for item in self.order_items if item.status != DishItemStatus.CANCELLED])

    def can_be_edited_by_user(self, user):
        """检查订单是否可以被指定用户编辑"""
        from models.user import UserRole

        # 管理员可以编辑任何订单
        if user.role == UserRole.ADMIN:
            return True

        # 商务中心和餐饮经理只能编辑已预订状态的订单
        if user.role in [UserRole.BUSINESS_CENTER, UserRole.MANAGER]:
            return self.status == OrderStatus.RESERVED

        # 厨师长可以编辑已开始用餐的订单（保持原有逻辑）
        if user.role == UserRole.CHEF_MANAGER and user.has_permission("order.edit_after_start"):
            return self.status in [OrderStatus.RESERVED, OrderStatus.PENDING_START, OrderStatus.SERVING]

        return False

    def can_be_deleted_by_user(self, user):
        """检查订单是否可以被指定用户删除"""
        from models.user import UserRole

        # 管理员可以删除任何已完成或已取消的订单
        if user.role == UserRole.ADMIN:
            return self.status in [OrderStatus.RESERVED, OrderStatus.COMPLETED, OrderStatus.CANCELLED]

        # 商务中心和餐饮经理可以删除已预订和已完成的订单
        if user.role in [UserRole.BUSINESS_CENTER, UserRole.MANAGER]:
            return self.status in [OrderStatus.RESERVED, OrderStatus.COMPLETED]

        return False

    def is_in_progress(self):
        """检查订单是否正在进行中（已开始用餐但未完成）"""
        return self.status in [OrderStatus.PENDING_START, OrderStatus.SERVING]

    def get_status_display_name(self):
        """获取状态的中文显示名称"""
        status_names = {
            OrderStatus.RESERVED: "已预订",
            OrderStatus.PENDING_START: "待开始",
            OrderStatus.SERVING: "已上菜",
            OrderStatus.COMPLETED: "已完成",
            OrderStatus.CANCELLED: "已取消",
            OrderStatus.CONFIRMED: "已确认",
            OrderStatus.IN_PROGRESS: "进行中"
        }
        return status_names.get(self.status, str(self.status))

    @property
    def dining_standard(self):
        """餐标金额"""
        return self.dining_standard_amount or self.total_amount

    @property
    def special_requirements(self):
        """特殊要求"""
        return self.special_requests

    @property
    def items(self):
        """菜品项列表 - 兼容性属性"""
        return self.order_items


class OrderItem(Base):
    """订单项模型"""
    __tablename__ = "order_items"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 关联信息
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, comment="订单ID")
    dish_id = Column(Integer, ForeignKey("dishes.id"), nullable=True, comment="菜品ID")
    
    # 菜品信息快照
    dish_name = Column(String(100), nullable=False, comment="菜品名称")
    dish_price = Column(Numeric(10, 2), nullable=False, comment="菜品单价")
    dish_category = Column(String(50), nullable=True, comment="菜品分类")
    
    # 订购信息
    quantity = Column(Integer, nullable=True, default=1, comment="数量")
    unit_price = Column(Numeric(10, 2), nullable=False, comment="单价")
    total_price = Column(Numeric(10, 2), nullable=False, comment="小计")
    
    # 状态信息
    status = Column(Enum(DishItemStatus), nullable=False, default=DishItemStatus.PENDING_COOK, comment="状态")
    waiter_status = Column(String(20), nullable=True, comment="服务员状态: served(已划菜), returned(已退菜)")
    waiter_confirmed = Column(Boolean, default=False, comment="服务员是否确认已上菜")
    
    # 制作信息
    assigned_chef_id = Column(Integer, ForeignKey("users.id"), nullable=True, comment="分配的厨师ID")
    kitchen_notes = Column(Text, nullable=True, comment="厨房备注")
    special_requirements = Column(Text, nullable=True, comment="特殊要求")
    
    # 时间信息
    ordered_at = Column(DateTime(timezone=True), nullable=True, comment="下单时间")
    confirmed_at = Column(DateTime(timezone=True), nullable=True, comment="确认时间")
    started_cooking_at = Column(DateTime(timezone=True), nullable=True, comment="开始制作时间")
    ready_at = Column(DateTime(timezone=True), nullable=True, comment="制作完成时间")
    served_at = Column(DateTime(timezone=True), nullable=True, comment="上菜时间")
    
    # 催菜信息
    rush_count = Column(Integer, default=0, comment="催菜次数")
    last_rush_at = Column(DateTime(timezone=True), nullable=True, comment="最后催菜时间")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    order = relationship("Order", back_populates="order_items")
    dish = relationship("Dish", back_populates="order_items")
    assigned_chef = relationship("User", foreign_keys=[assigned_chef_id])
    
    def __repr__(self):
        return f"<OrderItem(id={self.id}, dish='{self.dish_name}', quantity={self.quantity})>"
    
    @property
    def can_rush(self) -> bool:
        """是否可以催菜"""
        if self.status != DishItemStatus.PENDING_COOK:
            return False

        # 检查催菜冷却时间（5分钟）
        if self.last_rush_at:
            from datetime import datetime, timedelta
            from pytz import timezone
            CHINA_TZ = timezone('Asia/Shanghai')
            cooldown = timedelta(minutes=5)
            return datetime.now(CHINA_TZ) - self.last_rush_at > cooldown

        return True
    
    @property
    def cooking_duration(self) -> int:
        """制作时长（分钟）"""
        if self.created_at and self.ready_at:
            duration = self.ready_at - self.created_at
            return int(duration.total_seconds() / 60)
        return 0
    
    def update_status(self, new_status: DishItemStatus):
        """更新状态并记录时间"""
        from datetime import datetime
        from pytz import timezone
        CHINA_TZ = timezone('Asia/Shanghai')

        self.status = new_status
        now = datetime.now(CHINA_TZ)

        if new_status == DishItemStatus.READY:
            self.ready_at = now
