from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from core.database import Base
from datetime import datetime
import enum


class ActionType(enum.Enum):
    """指令类型枚举"""
    SERVE = "serve"  # 上菜
    RUSH = "rush"  # 催菜
    CHANGE = "change"  # 换菜
    ADD_DRINK = "add_drink"  # 加酒水
    ADD_STAPLE = "add_staple"  # 上主食
    ADD_FRUIT = "add_fruit"  # 上水果
    SPECIAL = "special"  # 特殊服务


class WaiterAction(Base):
    """服务员指令模型"""
    __tablename__ = "waiter_actions"
    
    id = Column(Integer, primary_key=True, index=True, comment="指令ID")
    
    # 基本信息
    waiter_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="服务员ID")
    room_number = Column(String(50), nullable=False, comment="包厢号")
    action_type = Column(String(50), nullable=False, comment="指令类型")
    action_content = Column(Text, comment="指令内容")
    
    # 状态信息
    is_processed = Column(Boolean, default=False, comment="是否已处理")
    processed_by = Column(Integer, ForeignKey("users.id"), nullable=True, comment="处理人ID")
    processed_at = Column(DateTime, nullable=True, comment="处理时间")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关系（暂时不使用back_populates避免循环引用）
    waiter = relationship("User", foreign_keys=[waiter_id])
    processor = relationship("User", foreign_keys=[processed_by])

    # 语音播报记录关联
    voice_broadcast_logs = relationship("VoiceBroadcastLog", back_populates="waiter_action", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<WaiterAction(id={self.id}, waiter={self.waiter.full_name if self.waiter else 'Unknown'}, room={self.room_number}, type={self.action_type})>"
    
    @property
    def action_type_display(self):
        """获取指令类型的显示名称"""
        type_names = {
            'dining_start': '开始用餐',
            'dining_end': '结束用餐',
            'force_end': '强制结束',
            'serve_dish': '上菜',
            'rush_dish': '催菜',
            'urge_dish': '催菜',
            'clean_table': '收脏餐',
            'add_staple': '上主食',
            'add_drink': '加酒水',
            'aolong_rice': '澳龙泡饭',
            'waiter_call': '叫服务员',
            'checkout': '结账',
            'change_tableware': '换餐具',
            'add_tea': '加茶水',
            'special_service': '特殊服务',
            'takeaway': '打包',
            'serve': '上菜',
            'rush': '催菜',
            'change': '换菜',
            'add_fruit': '上水果',
            'special': '特殊服务',
            'delete_order': '删除订单',
            'batch_delete_orders': '批量删除订单'
        }
        return type_names.get(self.action_type, self.action_type)

    @property
    def waiter_name(self):
        """获取服务员姓名"""
        return self.waiter.full_name if self.waiter else "未知服务员"
