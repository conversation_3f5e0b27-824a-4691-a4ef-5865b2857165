#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
暨阳湖大酒店传菜管理系统 - 端口管理工具
"""

import os
import sys
import socket
import subprocess
import platform
import argparse
from typing import List, Optional, Tuple

class PortManager:
    """端口管理器"""
    
    @staticmethod
    def is_port_in_use(port: int, host: str = 'localhost') -> bool:
        """检查端口是否被占用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex((host, port))
                return result == 0
        except Exception:
            return False
    
    @staticmethod
    def find_process_using_port(port: int) -> List[Tuple[int, str]]:
        """查找占用端口的进程"""
        processes = []
        system = platform.system().lower()
        
        try:
            if system == 'windows':
                # Windows使用netstat
                cmd = f'netstat -ano | findstr ":{port}"'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                for line in result.stdout.splitlines():
                    if f':{port}' in line and 'LISTENING' in line:
                        parts = line.split()
                        if len(parts) >= 5:
                            pid = int(parts[-1])
                            # 获取进程名
                            try:
                                cmd2 = f'tasklist /FI "PID eq {pid}" /FO CSV /NH'
                                result2 = subprocess.run(cmd2, shell=True, capture_output=True, text=True)
                                if result2.stdout:
                                    process_name = result2.stdout.split(',')[0].strip('"')
                                    processes.append((pid, process_name))
                            except:
                                processes.append((pid, 'Unknown'))
            
            else:
                # Linux/macOS使用lsof
                cmd = f'lsof -ti:{port}'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                for line in result.stdout.splitlines():
                    if line.strip():
                        pid = int(line.strip())
                        # 获取进程名
                        try:
                            cmd2 = f'ps -p {pid} -o comm='
                            result2 = subprocess.run(cmd2, shell=True, capture_output=True, text=True)
                            process_name = result2.stdout.strip() or 'Unknown'
                            processes.append((pid, process_name))
                        except:
                            processes.append((pid, 'Unknown'))
        
        except Exception as e:
            print(f"⚠️ 查找进程失败: {e}")
        
        return processes
    
    @staticmethod
    def kill_process_by_pid(pid: int) -> bool:
        """根据PID杀死进程"""
        try:
            system = platform.system().lower()
            
            if system == 'windows':
                cmd = f'taskkill /PID {pid} /F'
            else:
                cmd = f'kill -9 {pid}'
            
            result = subprocess.run(cmd, shell=True, capture_output=True)
            return result.returncode == 0
        
        except Exception as e:
            print(f"❌ 杀死进程失败: {e}")
            return False
    
    @staticmethod
    def kill_processes_using_port(port: int) -> bool:
        """杀死占用指定端口的所有进程"""
        processes = PortManager.find_process_using_port(port)
        
        if not processes:
            print(f"✅ 端口 {port} 未被占用")
            return True
        
        print(f"🔍 发现 {len(processes)} 个进程占用端口 {port}:")
        for pid, name in processes:
            print(f"   PID: {pid}, 进程: {name}")
        
        success = True
        for pid, name in processes:
            print(f"🔧 正在杀死进程 {pid} ({name})...")
            if PortManager.kill_process_by_pid(pid):
                print(f"✅ 进程 {pid} 已杀死")
            else:
                print(f"❌ 杀死进程 {pid} 失败")
                success = False
        
        return success
    
    @staticmethod
    def find_available_port(start_port: int = 5109, max_attempts: int = 100) -> Optional[int]:
        """查找可用端口"""
        for port in range(start_port, start_port + max_attempts):
            if not PortManager.is_port_in_use(port):
                return port
        return None
    
    @staticmethod
    def validate_port(port: int) -> bool:
        """验证端口号是否有效"""
        return 1024 <= port <= 65535
    
    @staticmethod
    def get_port_info(port: int) -> dict:
        """获取端口详细信息"""
        info = {
            'port': port,
            'in_use': PortManager.is_port_in_use(port),
            'processes': PortManager.find_process_using_port(port),
            'valid': PortManager.validate_port(port)
        }
        return info
    
    @staticmethod
    def scan_ports(start_port: int, end_port: int) -> List[dict]:
        """扫描端口范围"""
        results = []
        for port in range(start_port, end_port + 1):
            if PortManager.is_port_in_use(port):
                info = PortManager.get_port_info(port)
                results.append(info)
        return results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='暨阳湖大酒店传菜管理系统 - 端口管理工具')
    parser.add_argument('--check', '-c', type=int, help='检查指定端口是否被占用')
    parser.add_argument('--kill', '-k', type=int, help='杀死占用指定端口的进程')
    parser.add_argument('--find', '-f', type=int, help='从指定端口开始查找可用端口')
    parser.add_argument('--scan', '-s', nargs=2, type=int, metavar=('START', 'END'), 
                       help='扫描端口范围 (例如: --scan 8000 8010)')
    parser.add_argument('--info', '-i', type=int, help='获取端口详细信息')
    
    args = parser.parse_args()
    
    if args.check:
        port = args.check
        if not PortManager.validate_port(port):
            print(f"❌ 端口号 {port} 无效")
            return 1
        
        if PortManager.is_port_in_use(port):
            print(f"🔴 端口 {port} 已被占用")
            processes = PortManager.find_process_using_port(port)
            if processes:
                print("占用进程:")
                for pid, name in processes:
                    print(f"   PID: {pid}, 进程: {name}")
        else:
            print(f"🟢 端口 {port} 可用")
    
    elif args.kill:
        port = args.kill
        if not PortManager.validate_port(port):
            print(f"❌ 端口号 {port} 无效")
            return 1
        
        if PortManager.kill_processes_using_port(port):
            print(f"✅ 端口 {port} 清理完成")
        else:
            print(f"❌ 端口 {port} 清理失败")
    
    elif args.find:
        start_port = args.find
        if not PortManager.validate_port(start_port):
            print(f"❌ 起始端口号 {start_port} 无效")
            return 1
        
        available_port = PortManager.find_available_port(start_port)
        if available_port:
            print(f"✅ 找到可用端口: {available_port}")
        else:
            print(f"❌ 从端口 {start_port} 开始未找到可用端口")
    
    elif args.scan:
        start_port, end_port = args.scan
        if not (PortManager.validate_port(start_port) and PortManager.validate_port(end_port)):
            print(f"❌ 端口范围无效: {start_port}-{end_port}")
            return 1
        
        if start_port > end_port:
            print(f"❌ 起始端口不能大于结束端口")
            return 1
        
        print(f"🔍 扫描端口范围 {start_port}-{end_port}...")
        used_ports = PortManager.scan_ports(start_port, end_port)
        
        if used_ports:
            print(f"发现 {len(used_ports)} 个被占用的端口:")
            for info in used_ports:
                port = info['port']
                processes = info['processes']
                print(f"   端口 {port}:")
                for pid, name in processes:
                    print(f"      PID: {pid}, 进程: {name}")
        else:
            print(f"✅ 端口范围 {start_port}-{end_port} 内无占用端口")
    
    elif args.info:
        port = args.info
        if not PortManager.validate_port(port):
            print(f"❌ 端口号 {port} 无效")
            return 1
        
        info = PortManager.get_port_info(port)
        print(f"端口 {port} 信息:")
        print(f"   有效性: {'✅ 有效' if info['valid'] else '❌ 无效'}")
        print(f"   占用状态: {'🔴 已占用' if info['in_use'] else '🟢 可用'}")
        
        if info['processes']:
            print("   占用进程:")
            for pid, name in info['processes']:
                print(f"      PID: {pid}, 进程: {name}")
    
    else:
        # 默认检查系统默认端口
        default_port = 5109
        print("🔍 检查系统默认端口...")
        
        info = PortManager.get_port_info(default_port)
        print(f"端口 {default_port} 状态: {'🔴 已占用' if info['in_use'] else '🟢 可用'}")
        
        if info['processes']:
            print("占用进程:")
            for pid, name in info['processes']:
                print(f"   PID: {pid}, 进程: {name}")
        
        if info['in_use']:
            available_port = PortManager.find_available_port(default_port + 1)
            if available_port:
                print(f"💡 建议使用端口: {available_port}")
    
    return 0

if __name__ == '__main__':
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)
