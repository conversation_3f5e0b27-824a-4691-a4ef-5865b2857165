@echo off
chcp 65001 >nul
title 暨阳湖大酒店传菜管理系统

echo.
echo ========================================
echo    暨阳湖大酒店传菜管理系统
echo ========================================
echo.

cd /d "%~dp0"

REM 设置默认端口
set DEFAULT_PORT=5109
set SYSTEM_PORT=%DEFAULT_PORT%

REM 从环境变量获取端口
if defined HOTEL_SYSTEM_PORT (
    set SYSTEM_PORT=%HOTEL_SYSTEM_PORT%
)

REM 从命令行参数获取端口
if not "%1"=="" (
    set SYSTEM_PORT=%1
)

echo 🔧 系统配置检查...
echo    目标端口: %SYSTEM_PORT%
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    echo.
    pause
    exit /b 1
)

REM 检查主程序文件是否存在
if not exist "main.py" (
    echo ❌ 错误：未找到主程序文件 main.py
    echo 请确保在正确的目录中运行此脚本
    echo.
    pause
    exit /b 1
)

REM 检查端口是否被占用
echo 🔍 检查端口 %SYSTEM_PORT% 占用情况...
netstat -an | findstr ":%SYSTEM_PORT% " >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  警告：端口 %SYSTEM_PORT% 已被占用
    echo.
    echo 💡 处理选项：
    echo    1. 自动杀死占用进程并继续启动
    echo    2. 使用其他端口启动
    echo    3. 手动处理后重新启动
    echo.
    set /p choice="请选择处理方式 (1/2/3): "

    if "!choice!"=="1" (
        echo 🔧 正在杀死占用端口 %SYSTEM_PORT% 的进程...
        for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%SYSTEM_PORT% "') do (
            taskkill /PID %%a /F >nul 2>&1
        )
        echo ✅ 端口清理完成
        timeout /t 2 >nul
    ) else if "!choice!"=="2" (
        set /a NEW_PORT=%SYSTEM_PORT%+1
        echo 🔄 使用端口 !NEW_PORT! 启动系统...
        set SYSTEM_PORT=!NEW_PORT!
    ) else (
        echo 👋 请手动处理端口冲突后重新运行脚本
        pause
        exit /b 1
    )
    echo.
)

REM 检查数据库文件是否存在，如果不存在则初始化
if not exist "paocai.db" (
    echo 📋 首次运行，正在初始化数据库...
    python init_production_db.py
    if errorlevel 1 (
        echo ❌ 数据库初始化失败
        echo.
        pause
        exit /b 1
    )
    echo ✅ 数据库初始化完成
    echo.
)

echo 🚀 启动暨阳湖大酒店传菜管理系统...
echo.
echo 📋 系统信息：
echo    访问地址: http://localhost:%SYSTEM_PORT%
echo    API文档: http://localhost:%SYSTEM_PORT%/docs
echo    使用端口: %SYSTEM_PORT%
echo.
echo 🔑 默认登录账号：
echo    系统管理员: admin / admin123
echo    餐饮经理: manager01 / manager123
echo    厨师长: chef01 / chef123
echo    打荷员: helper01 / helper123
echo    商务中心: business01 / business123
echo.
echo ⚠️  注意：请勿关闭此窗口，关闭窗口将停止系统服务
echo    如需停止系统，请按 Ctrl+C
echo.
echo 💡 提示：
echo    - 使用其他端口: start_system.bat 8002
echo    - 设置环境变量: set HOTEL_SYSTEM_PORT=8002
echo.
echo ========================================
echo.

REM 启动系统
if "%SYSTEM_PORT%"=="%DEFAULT_PORT%" (
    python main.py
) else (
    python main.py --port %SYSTEM_PORT%
)

echo.
echo 系统已停止运行
pause
