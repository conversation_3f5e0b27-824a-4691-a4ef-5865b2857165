#!/bin/bash
# -*- coding: utf-8 -*-
# 暨阳湖大酒店传菜管理系统 - Linux启动脚本

# 设置脚本编码
export LANG=zh_CN.UTF-8
export LC_ALL=zh_CN.UTF-8

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 设置默认端口
DEFAULT_PORT=5109
SYSTEM_PORT=${HOTEL_SYSTEM_PORT:-$DEFAULT_PORT}

# 从命令行参数获取端口
if [ ! -z "$1" ]; then
    SYSTEM_PORT=$1
fi

echo
echo "========================================"
echo "    暨阳湖大酒店传菜管理系统"
echo "========================================"
echo

print_info "🔧 系统配置检查..."
print_info "   目标端口: $SYSTEM_PORT"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    print_error "❌ 错误：未找到Python环境"
    print_error "请确保已安装Python 3.8或更高版本"
    echo
    exit 1
fi

# 确定Python命令
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | sed -n 's/Python \([0-9]\+\.[0-9]\+\).*/\1/p')
PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)

if [ ! -z "$PYTHON_VERSION" ]; then
    if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
        print_error "❌ 错误：Python版本过低 ($PYTHON_VERSION)"
        print_error "请安装Python 3.8或更高版本"
        echo
        exit 1
    fi
fi

print_success "✅ Python环境检查通过 ($PYTHON_VERSION)"

# 检查主程序文件是否存在
if [ ! -f "main.py" ]; then
    print_error "❌ 错误：未找到主程序文件 main.py"
    print_error "请确保在正确的目录中运行此脚本"
    echo
    exit 1
fi

# 端口有效性检查
if [ "$SYSTEM_PORT" -lt 1024 ] || [ "$SYSTEM_PORT" -gt 65535 ]; then
    print_error "❌ 错误：端口号 $SYSTEM_PORT 无效"
    print_error "请使用 1024-65535 范围内的端口"
    echo
    exit 1
fi

# 检查端口是否被占用
check_port_usage() {
    if command -v lsof &> /dev/null; then
        lsof -ti:$SYSTEM_PORT 2>/dev/null
    elif command -v netstat &> /dev/null; then
        netstat -tlnp 2>/dev/null | grep ":$SYSTEM_PORT " | awk '{print $7}' | cut -d/ -f1
    else
        # 如果没有lsof和netstat，尝试连接端口
        timeout 1 bash -c "echo >/dev/tcp/localhost/$SYSTEM_PORT" 2>/dev/null && echo "occupied"
    fi
}

print_info "🔍 检查端口 $SYSTEM_PORT 占用情况..."
PORT_PIDS=$(check_port_usage)

if [ ! -z "$PORT_PIDS" ]; then
    print_warning "⚠️  警告：端口 $SYSTEM_PORT 已被占用"
    echo
    print_info "💡 处理选项："
    print_info "   1. 自动杀死占用进程并继续启动"
    print_info "   2. 使用其他端口启动"
    print_info "   3. 手动处理后重新启动"
    echo
    
    read -p "请选择处理方式 (1/2/3): " choice
    
    case $choice in
        1)
            print_info "🔧 正在杀死占用端口 $SYSTEM_PORT 的进程..."
            if command -v lsof &> /dev/null; then
                lsof -ti:$SYSTEM_PORT | xargs -r kill -9
            else
                for pid in $PORT_PIDS; do
                    if [ "$pid" != "occupied" ]; then
                        kill -9 $pid 2>/dev/null
                    fi
                done
            fi
            print_success "✅ 端口清理完成"
            sleep 2
            ;;
        2)
            NEW_PORT=$((SYSTEM_PORT + 1))
            print_info "🔄 使用端口 $NEW_PORT 启动系统..."
            SYSTEM_PORT=$NEW_PORT
            ;;
        3)
            print_info "👋 请手动处理端口冲突后重新运行脚本"
            exit 1
            ;;
        *)
            print_error "❌ 无效选择，退出"
            exit 1
            ;;
    esac
    echo
fi

# 检查数据库文件是否存在，如果不存在则初始化
if [ ! -f "paocai.db" ]; then
    print_info "📋 首次运行，正在初始化数据库..."
    $PYTHON_CMD init_production_db.py
    if [ $? -ne 0 ]; then
        print_error "❌ 数据库初始化失败"
        echo
        exit 1
    fi
    print_success "✅ 数据库初始化完成"
    echo
fi

# 检查依赖包
print_info "📦 检查Python依赖包..."
if [ -f "requirements.txt" ]; then
    $PYTHON_CMD -c "
import sys
import importlib.util

try:
    with open('requirements.txt', 'r') as f:
        requirements = f.read().splitlines()

    missing = []
    for requirement in requirements:
        if requirement.strip() and not requirement.startswith('#'):
            # 简单的包名提取（忽略版本号）
            package_name = requirement.strip().split('==')[0].split('>=')[0].split('<=')[0].split('>')[0].split('<')[0]
            try:
                importlib.import_module(package_name.replace('-', '_'))
            except ImportError:
                missing.append(package_name)

    if missing:
        print('❌ 缺少依赖包:', ', '.join(missing))
        print('💡 请运行: pip install -r requirements.txt')
        sys.exit(1)
    else:
        print('✅ 依赖包检查通过')
except Exception as e:
    print('⚠️ 依赖包检查失败:', e)
"
    if [ $? -ne 0 ]; then
        echo
        exit 1
    fi
fi

print_success "🚀 启动暨阳湖大酒店传菜管理系统..."
echo
print_info "📋 系统信息："
print_info "   访问地址: http://localhost:$SYSTEM_PORT"
print_info "   API文档: http://localhost:$SYSTEM_PORT/docs"
print_info "   使用端口: $SYSTEM_PORT"
echo
print_info "🔑 默认登录账号："
print_info "   系统管理员: admin / admin123"
print_info "   餐饮经理: manager01 / manager123"
print_info "   厨师长: chef01 / chef123"
print_info "   打荷员: helper01 / helper123"
print_info "   商务中心: business01 / business123"
echo
print_warning "⚠️  注意：请勿关闭此终端，关闭终端将停止系统服务"
print_warning "   如需停止系统，请按 Ctrl+C"
echo
print_info "💡 提示："
print_info "   - 使用其他端口: ./start_system.sh 8002"
print_info "   - 设置环境变量: export HOTEL_SYSTEM_PORT=8002"
print_info "   - 后台运行: nohup ./start_system.sh > system.log 2>&1 &"
echo
echo "========================================"
echo

# 设置信号处理
cleanup() {
    echo
    print_info "🛑 正在停止系统..."
    # 杀死子进程
    jobs -p | xargs -r kill
    print_success "✅ 系统已停止"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 启动系统
if [ "$SYSTEM_PORT" == "$DEFAULT_PORT" ]; then
    $PYTHON_CMD main.py
else
    $PYTHON_CMD main.py --port $SYSTEM_PORT
fi

# 如果程序正常退出
echo
print_info "系统已停止运行"
