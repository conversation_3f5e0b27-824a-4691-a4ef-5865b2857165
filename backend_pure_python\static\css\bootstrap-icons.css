/* Bootstrap Icons - 完全本地化版本 */
/* 使用系统默认字体和简单字符，无需外部依赖 */

.bi::before,
[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 1em;
}

/* 常用图标 - 使用简单字符，完全本地化 */
.bi-house::before { content: "⌂"; }
.bi-person::before { content: "👤"; }
.bi-people::before { content: "👥"; }
.bi-gear::before { content: "⚙"; }
.bi-list::before { content: "≡"; }
.bi-x::before { content: "×"; }
.bi-check::before { content: "✓"; }
.bi-plus::before { content: "+"; }
.bi-dash::before { content: "−"; }
.bi-pencil::before { content: "✎"; }
.bi-trash::before { content: "🗑"; }
.bi-eye::before { content: "👁"; }
.bi-box-arrow-right::before { content: "↗"; }
.bi-door-open::before { content: "⌂"; }
.bi-hourglass-split::before { content: "⧗"; }
.bi-check-circle::before { content: "✓"; }
.bi-x-circle::before { content: "✗"; }
.bi-exclamation-triangle::before { content: "⚠"; }
.bi-info-circle::before { content: "ⓘ"; }
.bi-plus-circle::before { content: "⊕"; }
.bi-search::before { content: "⌕"; }
.bi-bell::before { content: "🔔"; }
.bi-envelope::before { content: "✉"; }
.bi-calendar::before { content: "📅"; }
.bi-clock::before { content: "⏰"; }
.bi-download::before { content: "↓"; }
.bi-upload::before { content: "↑"; }
.bi-arrow-clockwise::before { content: "↻"; }
.bi-arrow-counterclockwise::before { content: "↺"; }
.bi-arrow-left::before { content: "←"; }
.bi-arrow-right::before { content: "→"; }
.bi-arrow-up::before { content: "↑"; }
.bi-arrow-down::before { content: "↓"; }
.bi-chevron-left::before { content: "‹"; }
.bi-chevron-right::before { content: "›"; }
.bi-chevron-up::before { content: "⌃"; }
.bi-chevron-down::before { content: "⌄"; }
.bi-star::before { content: "★"; }
.bi-heart::before { content: "♥"; }
.bi-bookmark::before { content: "🔖"; }
.bi-flag::before { content: "🚩"; }
.bi-info-circle::before { content: "ℹ️"; }
.bi-exclamation-triangle::before { content: "⚠️"; }
.bi-check-circle::before { content: "✅"; }
.bi-x-circle::before { content: "❌"; }
.bi-question-circle::before { content: "❓"; }
.bi-shield::before { content: "🛡️"; }
.bi-lock::before { content: "🔒"; }
.bi-unlock::before { content: "🔓"; }
.bi-key::before { content: "🔑"; }
.bi-file::before { content: "📄"; }
.bi-folder::before { content: "📁"; }
.bi-image::before { content: "🖼️"; }
.bi-camera::before { content: "📷"; }
.bi-mic::before { content: "🎤"; }
.bi-volume-up::before { content: "🔊"; }
.bi-volume-down::before { content: "🔉"; }
.bi-volume-mute::before { content: "🔇"; }
.bi-play::before { content: "▶️"; }
.bi-pause::before { content: "⏸️"; }
.bi-stop::before { content: "⏹️"; }
.bi-skip-backward::before { content: "⏮️"; }
.bi-skip-forward::before { content: "⏭️"; }
.bi-wifi::before { content: "📶"; }
.bi-bluetooth::before { content: "🔵"; }
.bi-battery::before { content: "🔋"; }
.bi-power::before { content: "⏻"; }
.bi-refresh::before { content: "🔄"; }
.bi-sync::before { content: "🔄"; }
.bi-share::before { content: "📤"; }
.bi-link::before { content: "🔗"; }
.bi-paperclip::before { content: "📎"; }
.bi-printer::before { content: "🖨️"; }
.bi-save::before { content: "💾"; }
.bi-cloud::before { content: "☁️"; }
.bi-cloud-download::before { content: "☁️⬇️"; }
.bi-cloud-upload::before { content: "☁️⬆️"; }

/* 餐饮相关图标 */
.bi-cup::before { content: "☕"; }
.bi-cup-hot::before { content: "☕"; }
.bi-egg-fried::before { content: "🍳"; }
.bi-basket::before { content: "🧺"; }
.bi-cart::before { content: "🛒"; }
.bi-shop::before { content: "🏪"; }
.bi-building::before { content: "🏢"; }
.bi-door-open::before { content: "🚪"; }
.bi-table::before { content: "🪑"; }

/* 厨房相关图标 */
.bi-fire::before { content: "🔥"; }
.bi-thermometer::before { content: "🌡️"; }
.bi-stopwatch::before { content: "⏱️"; }
.bi-alarm::before { content: "⏰"; }

/* 状态图标 */
.bi-circle::before { content: "○"; }
.bi-circle-fill::before { content: "●"; }
.bi-square::before { content: "□"; }
.bi-square-fill::before { content: "■"; }
.bi-triangle::before { content: "△"; }
.bi-triangle-fill::before { content: "▲"; }

/* 导航图标 */
.bi-house-door::before { content: "🏠"; }
.bi-grid::before { content: "⊞"; }
.bi-grid-3x3::before { content: "⊞"; }
.bi-menu-button::before { content: "☰"; }
.bi-menu-button-wide::before { content: "☰"; }

/* 用户相关图标 */
.bi-person-circle::before { content: "👤"; }
.bi-person-plus::before { content: "👤+"; }
.bi-person-dash::before { content: "👤-"; }
.bi-person-check::before { content: "👤✓"; }
.bi-person-x::before { content: "👤✕"; }

/* 工具图标 */
.bi-tools::before { content: "🔧"; }
.bi-wrench::before { content: "🔧"; }
.bi-hammer::before { content: "🔨"; }
.bi-screwdriver::before { content: "🪛"; }

/* 通信图标 */
.bi-chat::before { content: "💬"; }
.bi-chat-dots::before { content: "💬"; }
.bi-telephone::before { content: "📞"; }
.bi-headset::before { content: "🎧"; }

/* 媒体图标 */
.bi-music-note::before { content: "🎵"; }
.bi-music-note-beamed::before { content: "🎶"; }
.bi-speaker::before { content: "🔊"; }

/* 其他常用图标 */
.bi-lightning::before { content: "⚡"; }
.bi-sun::before { content: "☀️"; }
.bi-moon::before { content: "🌙"; }
.bi-brightness-high::before { content: "☀️"; }
.bi-brightness-low::before { content: "🌙"; }