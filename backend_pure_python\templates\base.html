<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}暨阳湖大酒店传菜管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <!-- 暨阳湖增强样式 -->
    <link href="/static/css/jiyang-enhanced.css" rel="stylesheet">
    <!-- 移动端优化样式 -->
    <link href="/static/css/mobile.css" rel="stylesheet">

    <!-- 导航栏用户信息样式优化 -->
    <style>
        /* 导航栏用户信息区域 */
        .navbar-nav.align-items-center {
            flex-wrap: nowrap;
        }

        .navbar-text {
            color: #495057 !important;
            margin-bottom: 0;
            white-space: nowrap;
        }

        .navbar-text strong {
            color: #212529;
        }

        /* 响应式处理 */
        @media (max-width: 991px) {
            .navbar-nav.align-items-center {
                flex-direction: column;
                align-items: flex-start !important;
                width: 100%;
                margin-top: 1rem;
            }

            .navbar-nav.align-items-center .nav-item {
                margin: 0.25rem 0 !important;
                width: 100%;
            }

            .navbar-nav.align-items-center .nav-item .btn {
                width: 100%;
                justify-content: flex-start;
                text-align: left;
            }

            .navbar-nav.align-items-center .nav-item .nav-link {
                width: 100%;
                text-align: left;
            }

            .navbar-text {
                padding: 0.5rem 0;
                border-bottom: 1px solid #dee2e6;
                margin-bottom: 0.5rem;
                width: 100%;
            }
        }

        /* 按钮样式优化 */
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 0.375rem;
        }

        /* 确保按钮可点击 */
        .navbar-nav .btn {
            z-index: 1000;
            position: relative;
        }

        /* 用户信息徽章样式 */
        .navbar-text .badge {
            font-size: 0.75em;
        }

        /* 修复单选按钮显示问题 */
        .form-check-input[type="radio"] {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            width: 1.125em;
            height: 1.125em;
            margin-top: 0.125em;
            vertical-align: top;
            background-color: #fff;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            border: 1px solid #adb5bd;
            border-radius: 50%;
            transition: background-color 0.15s ease-in-out, background-position 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-check-input[type="radio"]:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
        }

        .form-check-input[type="radio"]:focus {
            border-color: #86b7fe;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        .form-check-input[type="radio"]:disabled {
            pointer-events: none;
            filter: none;
            opacity: 0.5;
        }

        .form-check-input[type="radio"]:disabled ~ .form-check-label {
            opacity: 0.5;
        }

        /* 确保标签可点击 */
        .form-check-label {
            cursor: pointer;
            margin-bottom: 0;
        }

        /* 单选按钮组样式优化 */
        .form-check {
            display: flex;
            align-items: center;
            min-height: 1.5rem;
            padding-left: 0;
            margin-bottom: 0.125rem;
        }

        .form-check .form-check-input {
            float: none;
            margin-left: 0;
            margin-right: 0.5rem;
        }

        /* 主题样式 */
        .theme-dark {
            background-color: #1a1a1a !important;
            color: #ffffff !important;
        }

        .theme-dark .navbar {
            background-color: #2d2d2d !important;
            border-color: #404040 !important;
        }

        .theme-dark .navbar-brand,
        .theme-dark .navbar-nav .nav-link {
            color: #ffffff !important;
        }

        .theme-dark .card {
            background-color: #2d2d2d !important;
            border-color: #404040 !important;
            color: #ffffff !important;
        }

        .theme-dark .card-header {
            background-color: #404040 !important;
            border-color: #555555 !important;
            color: #ffffff !important;
        }

        .theme-dark .form-control,
        .theme-dark .form-select {
            background-color: #404040 !important;
            border-color: #555555 !important;
            color: #ffffff !important;
        }

        .theme-dark .form-control:focus,
        .theme-dark .form-select:focus {
            background-color: #404040 !important;
            border-color: #007bff !important;
            color: #ffffff !important;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
        }

        .theme-dark .sidebar {
            background-color: #2d2d2d !important;
            border-color: #404040 !important;
        }

        .theme-dark .sidebar .nav-link {
            color: #cccccc !important;
        }

        .theme-dark .sidebar .nav-link:hover,
        .theme-dark .sidebar .nav-link.active {
            background-color: #404040 !important;
            color: #ffffff !important;
        }

        .theme-dark .table {
            color: #ffffff !important;
        }

        .theme-dark .table-striped > tbody > tr:nth-of-type(odd) > td {
            background-color: rgba(255, 255, 255, 0.05) !important;
        }

        .theme-dark .alert {
            border-color: #555555 !important;
        }

        .theme-dark .modal-content {
            background-color: #2d2d2d !important;
            color: #ffffff !important;
        }

        .theme-dark .modal-header {
            border-color: #404040 !important;
        }

        .theme-dark .modal-footer {
            border-color: #404040 !important;
        }

        /* 浅色主题（默认） */
        .theme-light {
            background-color: #ffffff !important;
            color: #000000 !important;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-building"></i>
                暨阳湖大酒店
            </a>
            
            {% if user %}
            <button class="navbar-toggler d-md-none" type="button" onclick="toggleSidebar()" title="切换侧边栏">
                <span class="navbar-toggler-icon"></span>
            </button>
            {% endif %}

            {% if user %}
            <!-- 用户信息区域 -->
            <div class="user-info">
                <div class="user-details">
                    <div class="user-name">
                        <i class="bi bi-person-circle me-1"></i>
                        {{ user.full_name }}
                    </div>
                </div>
                <div class="user-actions">
                    <!-- 主要退出按钮 -->
                    <button type="button" class="btn btn-logout btn-sm" id="logoutButton"
                            title="退出登录">
                        <i class="bi bi-box-arrow-right"></i>
                        <span>退出</span>
                    </button>

                    <!-- 备用退出链接 -->
                    <a href="#" class="btn btn-outline-secondary btn-sm ms-1"
                       onclick="event.preventDefault(); logout(); return false;"
                       title="备用退出登录">
                        <i class="bi bi-door-open"></i>
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </nav>

    <div class="container-fluid p-0">
        {% if user %}
        <!-- 侧边栏 -->
        <nav class="sidebar d-md-block">
            <div class="sidebar-content pt-3">
                    <ul class="nav flex-column">
                        {% if user.role.value == 'waiter' %}
                        <!-- 服务员专用界面 -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/waiter/menu' %}active{% endif %}" href="/waiter/menu">
                                菜单管理
                            </a>
                        </li>
                        {% elif user.role.value != 'waiter' %}
                        <!-- 其他角色界面 -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/dashboard' %}active{% endif %}" href="/dashboard">
                                工作台
                            </a>
                        </li>

                        {% if user.has_permission('table.view') %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/tables' %}active{% endif %}" href="/tables">
                                包厢管理
                            </a>
                        </li>
                        {% endif %}

                        {% if user.has_permission('order.view') %}
                        <li class="nav-item">
                            <a class="nav-link {% if '/orders' in request.url.path %}active{% endif %}" href="/orders">
                                {% if user.has_permission('order.manage') %}订单管理{% else %}订单查看{% endif %}
                            </a>
                        </li>
                        {% endif %}

                        {% if user.role.value == 'kitchen_helper' %}
                        <!-- 厨房打荷专用界面 -->
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/kitchen-helper' %}active{% endif %}" href="/kitchen-helper">
                                打荷操作
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/kitchen/display' %}active{% endif %}" href="/kitchen/display">
                                厨房大屏
                            </a>
                        </li>
                        {% elif user.role.value != 'kitchen_helper' and user.has_permission('kitchen.view') %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/kitchen-helper' %}active{% endif %}" href="/kitchen-helper">
                                打荷操作
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/kitchen/display' %}active{% endif %}" href="/kitchen/display">
                                厨房大屏
                            </a>
                        </li>
                        {% endif %}

                        {% if user.has_permission('user.manage') %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/users' %}active{% endif %}" href="/users">
                                用户管理
                            </a>
                        </li>
                        {% endif %}

                        {% if user.has_permission('waiter.authorize') %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/command-templates' %}active{% endif %}" href="/command-templates">
                                指令管理
                            </a>
                        </li>
                        {% endif %}

                        {% if user.has_permission('system.config') %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/system-settings' %}active{% endif %}" href="/system-settings">
                                系统设置
                            </a>
                        </li>

                        {% endif %}

                        <li class="nav-item mt-3">
                            <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
                                快速操作
                            </h6>
                        </li>

                        {% if user.has_permission('order.create') %}
                        <li class="nav-item">
                            <a class="nav-link" href="/orders/create">
                                新建订单
                            </a>
                        </li>
                        {% endif %}




                        {% endif %}
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- 主内容区 -->
            <main class="main-content {% if not user %}container{% endif %}">
                
                <!-- 消息提示 -->
                {% if error %}
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    {{ error }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}
                
                {% if success %}
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ success }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endif %}
                
                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>

    <!-- Bootstrap JS -->
    <script src="/static/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义 JavaScript -->
    <script>
        // 侧边栏切换功能
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.classList.toggle('show');
            }
        }

        // 点击主内容区关闭侧边栏
        document.addEventListener('DOMContentLoaded', function() {
            const mainContent = document.querySelector('.main-content');
            const sidebar = document.querySelector('.sidebar');

            if (mainContent && sidebar) {
                mainContent.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('show');
                    }
                });
            }
        });

        // 自动隐藏提示消息
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                if (window.bootstrap && window.bootstrap.Alert) {
                    var bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);

        // 确认删除
        function confirmDelete(message) {
            return confirm(message || '确定要删除吗？');
        }

        // 格式化货币
        function formatCurrency(amount) {
            return '¥' + parseFloat(amount).toFixed(2);
        }

        // 格式化日期时间
        function formatDateTime(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleString('zh-CN');
        }

        // 添加加载状态
        function showLoading(element) {
            if (element) {
                element.classList.add('loading');
            }
        }

        function hideLoading(element) {
            if (element) {
                element.classList.remove('loading');
            }
        }

        // 平滑滚动
        function smoothScrollTo(element) {
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            const sidebar = document.querySelector('.sidebar');
            if (sidebar && window.innerWidth > 768) {
                sidebar.classList.remove('show');
            }
        });


            function updateNetworkStatus() {
                const isOnline = navigator.onLine;
                const statusElement = document.getElementById('network-status');

                if (statusElement) {
                    if (isOnline) {
                        statusElement.innerHTML = '<i class="bi bi-wifi text-success"></i> 在线模式';
                        statusElement.className = 'badge bg-success';
                    } else {
                        statusElement.innerHTML = '<i class="bi bi-wifi-off text-warning"></i> 离线模式';
                        statusElement.className = 'badge bg-warning';
                    }
                }
            }

            // 监听网络状态变化
            window.addEventListener('online', updateNetworkStatus);
            window.addEventListener('offline', updateNetworkStatus);

            // 初始状态检查
            updateNetworkStatus();
        }

        // 页面加载完成后检查离线能力
        document.addEventListener('DOMContentLoaded', checkOfflineCapability);

        // 绑定退出登录按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            const logoutButton = document.getElementById('logoutButton');
            if (logoutButton) {
                console.log('✅ 找到退出登录按钮，绑定事件监听器');

                // 主要事件监听器
                logoutButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔘 退出登录按钮被点击');
                    logout();
                });

                // 备用事件监听器（双击）
                logoutButton.addEventListener('dblclick', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔘 退出登录按钮被双击（备用方法）');
                    forceLogout();
                });

                // 添加键盘支持
                logoutButton.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        console.log('⌨️ 退出登录按钮键盘触发');
                        logout();
                    }
                });
            } else {
                console.log('❌ 未找到退出登录按钮');
            }
        });

        // 强制退出登录（备用方法）
        function forceLogout() {
            console.log('🚨 强制退出登录（备用方法）');
            if (confirm('强制退出登录？')) {
                // 🔧 修复：使用统一的logout函数确保正确清除登录状态
                logout();
            }
        }

        // 全局快捷键支持（Ctrl+Shift+L 退出登录）
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.shiftKey && e.key === 'L') {
                e.preventDefault();
                console.log('⌨️ 快捷键触发退出登录 (Ctrl+Shift+L)');
                logout();
            }
        });

        // 退出登录功能 - 完整版本，确保正确清除登录状态
        function logout() {
            console.log('🚀 退出登录函数被调用');

            // 防止重复点击
            if (window.isLoggingOut) {
                console.log('⚠️ 正在退出登录中，请勿重复点击');
                return;
            }

            try {
                if (confirm('确定要退出登录吗？')) {
                    window.isLoggingOut = true;
                    console.log('✅ 用户确认退出登录');

                    // 🔧 修复：清除所有相关的cookie和本地存储
                    console.log('🧹 清除所有登录状态数据');

                    // 清除所有相关的cookie
                    document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    document.cookie = 'user_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    document.cookie = 'user_role=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    document.cookie = 'user_info=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

                    // 清除本地存储
                    try {
                        localStorage.clear();
                        sessionStorage.clear();
                        console.log('✅ 本地存储已清除');
                    } catch (storageError) {
                        console.warn('⚠️ 清除本地存储时出现警告:', storageError);
                    }

                    // 停止所有可能的定时器
                    if (typeof window.autoRefreshInterval !== 'undefined' && window.autoRefreshInterval) {
                        clearInterval(window.autoRefreshInterval);
                        console.log('✅ 自动刷新定时器已停止');
                    }

                    // 调用服务器端登出API（可选，不等待结果）
                    try {
                        fetch('/logout', {
                            method: 'POST',
                            credentials: 'include'
                        }).catch(() => {
                            // 忽略错误，因为用户可能已经离线
                            console.log('📡 服务器端登出请求已发送（忽略响应）');
                        });
                    } catch (apiError) {
                        console.log('📡 服务器端登出请求发送失败（忽略）');
                    }

                    // 跳转到登录页面
                    console.log('🔄 跳转到登录页面');
                    window.location.href = '/login';
                } else {
                    console.log('❌ 用户取消退出登录');
                }
            } catch (error) {
                console.error('❌ 退出登录函数执行出错:', error);
                // 发生任何错误都强制清除状态并跳转
                try {
                    document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                    localStorage.clear();
                    sessionStorage.clear();
                } catch (cleanupError) {
                    console.error('❌ 强制清理状态失败:', cleanupError);
                }
                alert('退出登录时发生错误，正在强制跳转到登录页面');
                window.location.href = '/login';
            }
        }

        // 通用Toast通知函数
        function showToast(type, message, duration = 5000) {
            const toastId = type + 'Toast';
            const toastBodyId = type + 'ToastBody';

            const toastElement = document.getElementById(toastId);
            const toastBody = document.getElementById(toastBodyId);

            if (toastElement && toastBody) {
                toastBody.textContent = message;

                const toast = new bootstrap.Toast(toastElement, {
                    delay: duration
                });
                toast.show();
            } else {
                // 降级到alert
                alert(message);
            }
        }

        // 便捷函数
        function showSuccess(message, duration = 5000) {
            showToast('success', message, duration);
        }

        function showError(message, duration = 5000) {
            showToast('error', message, duration);
        }

        function showInfo(message, duration = 5000) {
            showToast('info', message, duration);
        }

        // 修复单选按钮功能
        function initializeRadioButtons() {
            // 确保所有单选按钮都能正常工作
            document.querySelectorAll('input[type="radio"]').forEach(radio => {
                // 添加点击事件处理
                radio.addEventListener('change', function() {
                    // 取消同组其他单选按钮的选中状态
                    const name = this.name;
                    document.querySelectorAll(`input[type="radio"][name="${name}"]`).forEach(r => {
                        if (r !== this) {
                            r.checked = false;
                        }
                    });

                    // 确保当前按钮被选中
                    this.checked = true;

                    // 触发视觉更新
                    this.dispatchEvent(new Event('input', { bubbles: true }));
                });

                // 添加标签点击支持
                const label = document.querySelector(`label[for="${radio.id}"]`);
                if (label) {
                    label.addEventListener('click', function(e) {
                        e.preventDefault();
                        radio.click();
                    });
                }
            });
        }

        // 页面加载完成后初始化单选按钮
        document.addEventListener('DOMContentLoaded', function() {
            initializeRadioButtons();

            // 监听动态添加的单选按钮
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === 1) { // Element node
                                const radios = node.querySelectorAll ? node.querySelectorAll('input[type="radio"]') : [];
                                radios.forEach(radio => {
                                    // 重新初始化新添加的单选按钮
                                    initializeRadioButtons();
                                });
                            }
                        });
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        });
    </script>

    <!-- Toast 通知容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 11;">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <i class="bi bi-check-circle-fill me-2"></i>
                <strong class="me-auto">操作成功</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="successToastBody">
                操作已成功完成
            </div>
        </div>

        <div id="errorToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-danger text-white">
                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                <strong class="me-auto">操作失败</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="errorToastBody">
                操作失败，请重试
            </div>
        </div>

        <div id="infoToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-info text-white">
                <i class="bi bi-info-circle-fill me-2"></i>
                <strong class="me-auto">提示信息</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="infoToastBody">
                操作提示信息
            </div>
        </div>
    </div>

    {% block extra_js %}{% endblock %}
</body>
</html>
