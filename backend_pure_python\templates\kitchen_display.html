{% extends "base.html" %}

{% block title %}厨房大屏 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 强制厨房大屏黑色主题 - 覆盖所有可能的主题样式 */
    body,
    body.theme-light,
    body.theme-dark,
    html,
    html.theme-light,
    html.theme-dark {
        background: #1a1a1a !important;
        background-color: #1a1a1a !important;
        color: #FFD700 !important; /* 亮黄色字体 */
    }

    /* 厨房大屏专用：完全隐藏导航栏和侧边栏 */
    .navbar,
    .sidebar,
    .offcanvas {
        display: none !important;
    }

    .main-content,
    .main-content.theme-light,
    .main-content.theme-dark {
        background: #1a1a1a !important;
        background-color: #1a1a1a !important;
        color: #fff !important;
        margin-left: 0 !important;
        width: 100% !important;
        padding: 0 !important;
    }

    .kitchen-display {
        background: #1a1a1a !important;
        background-color: #1a1a1a !important;
        color: #fff !important;
        min-height: 100vh;
        padding: 0;
        margin: 0;
        width: 100vw;
        overflow-x: hidden;
    }

    /* 覆盖导航栏样式 */
    .navbar,
    .navbar.theme-light,
    .navbar.theme-dark {
        background: #2d2d2d !important;
        background-color: #2d2d2d !important;
        border-color: #404040 !important;
    }

    .navbar-brand,
    .navbar-nav .nav-link,
    .navbar.theme-light .navbar-brand,
    .navbar.theme-light .navbar-nav .nav-link,
    .navbar.theme-dark .navbar-brand,
    .navbar.theme-dark .navbar-nav .nav-link {
        color: #ffffff !important;
    }

    .room-column {
        background: #2d2d2d;
        border-radius: 10px;
        margin: 0 10px 20px 0;
        padding: 15px;
        border-top: 5px solid #007bff;
        min-height: 400px;
        width: 280px;
        display: inline-block;
        vertical-align: top;
        transition: all 0.3s ease;
    }

    .room-column.not-served {
        background: #404040;
        border-top-color: #6c757d;
        opacity: 0.6;
    }

    .room-column.served {
        background: #2d2d2d;
        border-top-color: #28a745;
        opacity: 1;
    }

    .dish-disabled {
        opacity: 0.5;
        cursor: not-allowed !important;
        pointer-events: none;
    }

    .dish-disabled:hover {
        transform: none !important;
        box-shadow: none !important;
    }

    .room-header {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 20px;
        color: #ffc107;
        text-align: center;
        border-bottom: 2px solid #ffc107;
        padding-bottom: 10px;
        writing-mode: horizontal-tb;
    }

    .dish-item {
        background: #3d3d3d;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 12px;
        border-left: 4px solid #28a745;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .dish-item:hover {
        transform: translateX(5px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    .dish-item.pending_cook {
        border-left-color: #6c757d;
        background: #3d3d3d;
    }

    .dish-item.cooking {
        border-left-color: #ffc107;
        background: #4a4a2d;
    }

    .dish-item.ready {
        border-left-color: #dc3545;
        background: #4a2d2d;
        animation: pulse 2s infinite;
    }

    .dish-item.served {
        border-left-color: #6c757d;
        background: #3a3a3a;
        opacity: 0.7;
    }

    .dish-item.served .dish-name {
        text-decoration: line-through;
    }

    /* 制作完成的菜品样式 */
    .dish-name.dish-ready {
        color: #888888 !important;
        text-decoration: line-through;
    }

    .ready-time {
        color: #FFD700 !important;
        font-size: 0.8rem;
        margin-top: 4px;
    }

    /* 高对比度相同菜品标识样式 */
    .dish-item.cross-room {
        position: relative;
        background: var(--dish-color, #FF4444) !important;
        color: #FFFFFF !important;
        border: 3px solid #FFFFFF !important;
        box-shadow: 0 0 20px rgba(255, 68, 68, 0.8);
    }

    .dish-item.cross-room::before {
        content: '多';
        position: absolute;
        top: -8px;
        right: -8px;
        background: #000000;
        color: #FFFF00;
        font-size: 0.7rem;
        font-weight: bold;
        padding: 2px 4px;
        border-radius: 8px;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0,0,0,0.8);
        border: 1px solid #FFFFFF;
        min-width: 16px;
        text-align: center;
    }

    .dish-item.cross-room .dish-name {
        color: #FFFFFF !important;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
        font-size: 1.2rem;
    }

    /* 高对比度颜色方案 */
    .dish-item.pending_cook {
        background: #2d5a2d !important;
        border-color: #00FF00 !important;
        color: #FFFFFF !important;
    }

    .dish-item.cooking {
        background: #5a5a2d !important;
        border-color: #FFFF00 !important;
        color: #000000 !important;
    }

    .dish-item.ready {
        background: #5a5a5a !important;
        border-color: #CCCCCC !important;
        color: #FFFFFF !important;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .dish-name {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 8px;
        color: #fff;
    }

    .dish-quantity {
        color: #adb5bd;
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .dish-status {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
        display: inline-block;
        margin-bottom: 8px;
    }

    .status-pending_cook {
        background: #6c757d;
        color: white;
    }

    .status-cooking {
        background: #ffc107;
        color: black;
    }

    .status-ready {
        background: #dc3545;
        color: white;
    }

    .status-served {
        background: #28a745;
        color: white;
    }

    /* 旧的时间显示样式已移除，使用头部中央的新样式 */

    .no-orders {
        text-align: center;
        color: #6c757d;
        font-size: 1.5rem;
        margin-top: 100px;
    }

    .commands-panel {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #2d2d2d;
        border-radius: 10px;
        padding: 15px;
        max-width: 300px;
        max-height: 200px;
        overflow-y: auto;
    }

    .command-item {
        background: #4a4a4a;
        padding: 8px;
        margin-bottom: 8px;
        border-radius: 5px;
        font-size: 0.9rem;
        cursor: pointer;
    }

    .command-room {
        color: #ffc107;
        font-weight: bold;
    }

    .command-content {
        color: #fff;
    }

    .command-time {
        color: #adb5bd;
        font-size: 0.8rem;
    }

    /* 优化后的厨房大屏头部布局 */
    .kitchen-header-optimized {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: linear-gradient(135deg, #2d2d2d, #404040);
        border-bottom: 3px solid #ffc107;
        margin-bottom: 0 !important;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }

    .header-left {
        flex: 1;
        text-align: left;
    }

    .kitchen-title-left {
        font-size: 1.8rem;
        font-weight: 700;
        color: #ffc107;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        letter-spacing: 1px;
    }

    .header-center {
        flex: 1;
        text-align: center;
    }



    .status-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 5px;
    }

    .status-item {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        font-size: 0.9rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 6px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .status-item.started {
        color: #28a745;
        background: rgba(40, 167, 69, 0.1);
        border: 1px solid #28a745;
    }

    .status-item.not-started {
        color: #ffc107;
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid #ffc107;
    }

    .current-time-center {
        font-size: 1.3rem;
        font-weight: 600;
        color: #ffffff;
        background: rgba(0,0,0,0.3);
        padding: 6px 12px;
        border-radius: 8px;
        border: 2px solid #ffc107;
        display: inline-block;
        min-width: 180px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .header-right {
        flex: 1;
        text-align: right;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 15px;
        flex-wrap: wrap;
    }

    .header-right .status-stats {
        display: flex;
        gap: 15px;
        margin: 0;
    }

    .header-right .page-indicator-inline {
        margin: 0;
    }

    .control-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .header-right .btn {
        font-weight: 600;
        border-width: 2px;
        padding: 8px 16px;
        transition: all 0.3s ease;
        margin: 0;
    }

    .header-right .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    /* 内联页码指示器样式 */
    .page-indicator-inline {
        display: inline-block;
        color: #ffc107;
        font-size: 1.1rem;
        font-weight: 600;
        background: rgba(255, 193, 7, 0.1);
        padding: 6px 12px;
        border-radius: 6px;
        border: 1px solid #ffc107;
        margin-right: 15px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        min-width: 100px;
        text-align: center;
    }

    /* 分页显示容器 */
    .paginated-rooms-container {
        flex: 1;
        overflow: hidden;
        position: relative;
    }

    .rooms-page {
        display: flex; /* 改用flex布局以更好控制包厢宽度 */
        justify-content: space-around; /* 包厢之间均匀分布 */
        align-items: flex-start;
        gap: 10px; /* 包厢之间的固定间隔 */
        padding: 5px; /* 容器内边距 */
        height: calc(100vh - 140px); /* 为头部和状态栏留出空间，页码指示器已移到头部 */
        width: 100vw; /* 使用全屏宽度 */
        overflow: hidden;
        transition: all 0.5s ease;
        box-sizing: border-box;
    }

    /* 包厢容器宽度直接由配置控制 */
    .room-column {
        width: var(--room-width, 370px) !important; /* 直接使用配置的宽度 */
        flex-shrink: 0; /* 防止包厢被压缩 */
        flex-grow: 0; /* 防止包厢被拉伸 */
    }

    /* 在较小屏幕上回退到响应式 */
    @media (max-width: 1900px) {
        .rooms-page {
            display: grid;
            grid-template-columns: repeat(var(--rooms-per-page, 5), 1fr);
            justify-content: stretch;
            gap: 5px;
        }

        .room-column {
            width: auto !important; /* 在小屏幕上使用自动宽度 */
        }
    }

    /* 包厢容器样式 - 宽度由配置控制 */
    .room-column {
        background: #2d2d2d;
        border-radius: 6px;
        border: 2px solid #666666; /* 默认灰色外框 */
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 1px 4px rgba(0,0,0,0.3);
        min-height: 0; /* 允许内容缩放 */
        height: auto; /* 高度自适应 */
        max-height: calc(100vh - 160px); /* 最大高度限制 */
        width: var(--room-width, 370px) !important; /* 直接使用配置的宽度 */
        flex-shrink: 0; /* 防止包厢被压缩 */
        flex-grow: 0; /* 防止包厢被拉伸 */
    }

    /* 已开始用餐包厢的外框 - 亮黄色边框 */
    .room-started {
        border: 2px solid #ffd700 !important; /* 亮黄色外框 */
        box-shadow: 0 0 10px rgba(255, 215, 0, 0.5) !important; /* 黄色光晕 */
    }

    /* 未开始用餐包厢的外框 - 灰色边框 */
    .room-not-started {
        border: 2px solid #666666 !important; /* 灰色外框 */
    }

    .room-column.not-served {
        background: #404040;
        border-top-color: #6c757d;
        opacity: 0.7;
    }

    .room-column.served {
        background: #2d2d2d;
        border-top-color: #28a745;
        opacity: 1;
    }

    /* 包厢标题优化 - 极致紧凑布局，更细边框 */
    .room-header {
        font-size: 1.2rem; /* 减小字体 */
        font-weight: 700;
        margin: 0;
        padding: 2px; /* 极致减少内边距 */
        color: #ffc107;
        text-align: center;
        border-bottom: 1px solid #ffc107; /* 减少边框粗细 */
        background: rgba(255, 193, 7, 0.1);
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        flex-shrink: 0;
        line-height: 1.2; /* 紧凑行高 */
    }

    /* 菜品容器 - 最大化空间利用的2列网格布局 */
    .dish-items-container {
        flex: 1;
        overflow-y: auto;
        padding: 1px; /* 极致减少内边距 */
        display: grid !important;
        grid-template-columns: 1fr 1fr !important; /* 始终2列布局 */
        gap: 2px; /* 减少间距 */
        align-content: start;
        background: #1a1a1a;
        grid-auto-rows: min-content;
    }

    /* 强制确保2列布局 */
    .room-column .dish-items-container {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 2px !important; /* 保持菜品间距 */
    }

    /* 菜品项优化 - 最大化空间利用，更细边框 */
    .dish-item {
        min-height: 35px; /* 进一步减少最小高度 */
        padding: 4px; /* 最小化内边距 */
        border-radius: 3px; /* 进一步减小圆角 */
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
        transition: all 0.2s ease;
        border: 1px solid transparent; /* 减少边框粗细 */
        position: relative;
        background: #666666; /* 默认灰色底色（未开始用餐） */
    }

    /* 已开始用餐包厢的菜品样式 - 橙色底色 */
    .room-started .dish-item {
        background: #ff8c00 !important; /* 橙色底色 */
        border: 1px solid #ff7700 !important;
        box-shadow: 0 0 8px rgba(255, 140, 0, 0.6) !important; /* 橙色光晕特效 */
    }

    /* 已开始用餐包厢的菜品文字 - 白色字体 */
    .room-started .dish-item .dish-name,
    .room-started .dish-item .dish-quantity,
    .room-started .dish-item .dish-special {
        color: #ffffff !important; /* 白色字体 */
        font-weight: 600 !important; /* 加粗以提高可读性 */
    }

    /* 未开始用餐包厢的菜品样式 - 灰色底色，无光晕 */
    .room-not-started .dish-item {
        background: #666666 !important; /* 灰色底色 */
        border: 1px solid #555555 !important;
        box-shadow: none !important; /* 移除光晕效果 */
    }

    /* 菜品名称优化 - 紧凑显示 */
    .dish-name {
        font-size: var(--dish-font-size, 0.85rem); /* 使用配置的字体大小 */
        font-weight: 600;
        line-height: 1.1; /* 更紧凑的行高 */
        word-break: break-word;
        color: #ffffff;
        margin-bottom: 1px; /* 最小化底部间距 */
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 最多显示2行 */
        -webkit-box-orient: vertical;
    }

    /* 自适应字体大小类 */
    .auto-font-size .dish-name {
        font-size: clamp(0.7rem, 2.5vw, 1.2rem) !important; /* 自适应字体：最小0.7rem，最大1.2rem */
    }

    /* 已完成菜品区域样式 */
    .completed-dishes-section {
        grid-column: 1 / -1; /* 占满整行 */
        margin-top: 8px;
        border-top: 1px solid #444;
        padding-top: 4px;
    }

    .completed-dishes-header {
        font-size: 0.7rem;
        color: #888;
        text-align: center;
        margin-bottom: 4px;
        font-weight: bold;
    }

    .completed-dishes-grid {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 2px !important;
    }

    /* 已完成菜品样式 */
    .dish-item.completed {
        background: #1a1a1a !important;
        border-color: #333 !important;
        opacity: 0.7;
        font-size: 0.7rem;
        min-height: 32px;
    }

    .dish-item.completed .dish-name {
        color: #888 !important;
    }

    .dish-item.completed .ready-time {
        font-size: 0.6rem;
        color: #666;
        margin-top: 2px;
    }

    /* 待制作菜品样式 */
    .dish-item.pending_cook {
        background: #3d3d3d;
        border-color: #28a745;
        cursor: pointer;
    }

    .dish-item.pending_cook:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
        border-color: #20c997;
    }

    /* 制作中菜品样式 */
    .dish-item.cooking {
        background: #4a4a2d;
        border-color: #ffc107;
        animation: pulse 2s infinite;
    }

    /* 相同菜品布局优化样式 */
    .multi-room-dish {
        position: relative;
        border-width: 2px !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
    }

    .multi-room-dish::before {
        content: '多';
        position: absolute;
        top: -6px;
        right: -6px;
        background: #ff6b6b;
        color: white;
        font-size: 9px;
        padding: 2px 4px;
        border-radius: 6px;
        font-weight: bold;
        z-index: 10;
        min-width: 14px;
        text-align: center;
    }

    /* 菜品布局位置指示器 */
    .dish-item[data-layout-position] {
        position: relative;
    }

    .dish-item[data-layout-position]::after {
        content: attr(data-layout-position);
        position: absolute;
        bottom: 2px;
        left: 2px;
        background: rgba(0,0,0,0.6);
        color: white;
        font-size: 8px;
        padding: 1px 4px;
        border-radius: 3px;
        font-weight: bold;
        opacity: 0.7;
    }

    /* 移除重复的包厢容器样式定义，使用上面的统一样式 */

    /* 移除冲突的样式定义，使用上面的grid布局 */

    /* 相同菜品高亮效果 */
    .dish-item:hover[data-dish-name] {
        box-shadow: 0 4px 15px rgba(0,0,0,0.3) !important;
        transform: translateY(-2px);
        z-index: 100;
    }

    /* 当鼠标悬停在某个菜品上时，高亮所有相同菜品 */
    .dish-item[data-dish-name]:hover ~ .dish-item[data-dish-name],
    .dish-item[data-dish-name]:hover {
        background-color: rgba(255, 235, 59, 0.3) !important;
        border-color: #ffc107 !important;
    }

    /* 中央通知显示区域 */
    .notification-center {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        pointer-events: none;
        width: 80%;
        max-width: 800px;
    }

    .notification-item {
        background: rgba(0, 0, 0, 0.9);
        border: 3px solid #ffc107;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 20px;
        text-align: center;
        font-size: 2.5rem;
        font-weight: bold;
        color: #ffc107;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3);
        animation: slideInScale 0.5s ease-out;
        position: relative;
        overflow: hidden;
    }

    .notification-item.instruction {
        border-color: #17a2b8;
        color: #17a2b8;
        box-shadow: 0 8px 32px rgba(23, 162, 184, 0.3);
    }

    .notification-item.dish-ready {
        border-color: #dc3545;
        color: #dc3545;
        box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
    }

    .notification-item.dining-ended {
        border-color: #28a745;
        color: #28a745;
        box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
    }

    .notification-content {
        position: relative;
        z-index: 2;
    }

    .notification-room {
        font-size: 1.8rem;
        margin-bottom: 10px;
        opacity: 0.8;
    }

    .notification-message {
        font-size: 3rem;
        line-height: 1.2;
    }

    .notification-type {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 1rem;
        opacity: 0.7;
        background: rgba(255,255,255,0.1);
        padding: 5px 10px;
        border-radius: 20px;
    }

    @keyframes slideInScale {
        0% {
            opacity: 0;
            transform: scale(0.5) translateY(-50px);
        }
        50% {
            transform: scale(1.1) translateY(0);
        }
        100% {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    @keyframes fadeOut {
        0% {
            opacity: 1;
            transform: scale(1);
        }
        100% {
            opacity: 0;
            transform: scale(0.8);
        }
    }

    .notification-item.fade-out {
        animation: fadeOut 0.5s ease-in forwards;
    }



    /* 全屏按钮样式 */
    #fullscreenBtn {
        background: rgba(255, 193, 7, 0.1) !important;
        border-color: #ffc107 !important;
        color: #ffc107 !important;
        transition: all 0.3s ease !important;
        font-size: 0.875rem !important;
        padding: 6px 12px !important;
    }

    #fullscreenBtn:hover {
        background: rgba(255, 193, 7, 0.2) !important;
        border-color: #ffcd39 !important;
        color: #ffcd39 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3) !important;
    }

    #fullscreenBtn:active {
        transform: translateY(0) !important;
        box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2) !important;
    }

    /* 全屏模式下的样式优化 */
    body:fullscreen {
        overflow: hidden;
    }

    body:-webkit-full-screen {
        overflow: hidden;
    }

    body:-moz-full-screen {
        overflow: hidden;
    }

    body:-ms-fullscreen {
        overflow: hidden;
    }

    /* 全屏模式下隐藏页面滚动条 */
    html:fullscreen,
    html:-webkit-full-screen,
    html:-moz-full-screen,
    html:-ms-fullscreen {
        overflow: hidden;
    }

    /* 底部状态栏样式 */
    .status-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 60px;
        background: linear-gradient(135deg, #000000, #333333);
        border-top: 3px solid #ffc107;
        z-index: 1000;
        overflow: hidden;
        box-shadow: 0 -4px 12px rgba(0,0,0,0.5);
    }

    .status-content {
        height: 100%;
        display: flex;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .scrolling-text {
        white-space: nowrap;
        color: #ffc107;
        font-weight: 600;
        font-size: 1.2rem; /* 默认字体大小，可通过配置调整 */
        text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
        position: absolute;
        left: 0;  /* 从左侧定位 */
        animation: scrollRightOnce var(--scroll-duration, 30s) linear forwards; /* 从左向右滚动 */
        white-space: nowrap;  /* 确保文字不换行 */
    }

    /* 滚动动画 - 从左向右滚动 */
    @keyframes scrollRightOnce {
        0% {
            transform: translateX(-100vw);  /* 从左侧屏幕外开始 */
            opacity: 1;
        }
        5% {
            opacity: 1;  /* 确保进入屏幕时完全可见 */
        }
        95% {
            transform: translateX(100vw);  /* 滚动到右侧屏幕外 */
            opacity: 1;
        }
        100% {
            transform: translateX(100vw);
            opacity: 0;  /* 完全消失 */
        }
    }

    /* 当没有内容时隐藏状态栏 */
    .status-bar.empty {
        display: none;
    }

    /* 高对比度状态栏变体 */
    .status-bar.high-contrast {
        background: linear-gradient(135deg, #000000, #1a1a1a);
        border-top-color: #ffff00;
    }

    .status-bar.high-contrast .scrolling-text {
        color: #ffff00;
        text-shadow: 2px 2px 6px rgba(0,0,0,1);
    }

    /* 厨房大屏专用：隐藏Toast通知容器 */
    .toast-container {
        display: none !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="kitchen-display">
    <!-- 优化后的页面头部布局 -->
    <div class="kitchen-header-optimized">
        <!-- 左侧：页面标题 -->
        <div class="header-left">
            <h1 class="kitchen-title-left">暨阳湖大酒店厨房管理系统-Powerd by Ping</h1>
        </div>

        <!-- 中央：实时时间 -->
        <div class="header-center">
            <div class="current-time-center" id="currentTime"></div>
        </div>

        <!-- 右侧：状态统计、页码指示器和控制按钮 -->
        <div class="header-right">
            <!-- 状态统计 -->
            <div class="status-stats">
                <span class="status-item started">
                    <i class="bi bi-check-circle-fill"></i>
                    已开始包厢<span id="startedRoomsCount">{{ started_rooms_count }}</span>个
                </span>
                <span class="status-item not-started">
                    <i class="bi bi-clock-fill"></i>
                    未开始包厢<span id="notStartedRoomsCount">{{ not_started_rooms_count }}</span>个
                </span>
            </div>

            <!-- 页码指示器 -->
            <span class="page-indicator-inline" id="pageIndicatorInline">第1页/共1页</span>

            <!-- 控制按钮组 -->
            <div class="control-buttons">
                <button class="btn btn-outline-light btn-sm me-2" onclick="window.history.back()" title="返回">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
                <button class="btn btn-outline-warning btn-sm" id="fullscreenBtn" onclick="toggleFullscreen()" title="全屏切换">
                    <i class="bi bi-fullscreen"></i> 全屏
                </button>
            </div>
        </div>
    </div>

    <!-- 页码指示器已移动到头部右侧 -->

    <!-- 分页显示容器 -->
    <div class="paginated-rooms-container" id="paginatedContainer">
        {% if orders_by_room %}
        <!-- 包厢数据将通过JavaScript进行分页处理 -->
        <div class="rooms-page" id="roomsPage">
            {% for room_name, room_data in orders_by_room.items() %}
            {% if room_data is mapping %}
                {% set dish_items = room_data.dish_items %}
                {% set is_served = room_data.is_served %}
            {% else %}
                {% set dish_items = room_data %}
                {% set is_served = false %}
            {% endif %}
            <div class="room-column {% if is_served %}served{% else %}not-served{% endif %} {% if room_data.room_info.dining_start_time %}room-started{% else %}room-not-started{% endif %}" data-room="{{ room_name }}">
                <div class="room-header">
                    {{ room_name }} - {{ room_data.room_info.guest_count or 0 }}人
                    {% if room_data.room_info.dining_start_time %}
                    <small class="text-success d-block">{{ room_data.room_info.dining_start_time }}</small>
                    {% else %}
                    <small class="text-muted d-block">未开始</small>
                    {% endif %}
                </div>

                <div class="dish-items-container">
                    <!-- 待制作的菜品 -->
                    {% set pending_dishes = dish_items | selectattr('status.value', 'equalto', 'pending_cook') | list %}
                    {% for item in pending_dishes %}
                    <div class="dish-item {{ item.status.value }} {% if room_data is mapping and not room_data.can_operate %}dish-disabled{% endif %} {% if item.dish_name in dish_color_map %}cross-room{% endif %}"
                         {% if room_data is mapping and room_data.can_operate %}onclick="toggleDishStatus({{ item.id }}, '{{ item.status.value }}', '{{ room_name }}')"{% endif %}
                         {% if item.dish_name in dish_color_map %}style="--dish-color: {{ dish_color_map[item.dish_name] }}; border-color: {{ dish_color_map[item.dish_name] }};"{% endif %}>
                        <div class="dish-name">
                            {{ item.dish_name }}
                        </div>
                    </div>
                    {% endfor %}

                    <!-- 已完成的菜品 - 显示在底部 -->
                    {% set ready_dishes = dish_items | selectattr('status.value', 'equalto', 'ready') | list %}
                    {% if ready_dishes %}
                    <div class="completed-dishes-section">
                        <div class="completed-dishes-header">已完成</div>
                        <div class="completed-dishes-grid">
                            {% for item in ready_dishes %}
                            <div class="dish-item ready completed {% if room_data is mapping and not room_data.can_operate %}dish-disabled{% endif %} {% if item.dish_name in dish_color_map %}cross-room{% endif %}"
                                 {% if room_data is mapping and room_data.can_operate %}onclick="toggleDishStatus({{ item.id }}, '{{ item.status.value }}', '{{ room_name }}')"{% endif %}
                                 {% if item.dish_name in dish_color_map %}style="--dish-color: {{ dish_color_map[item.dish_name] }}; border-color: {{ dish_color_map[item.dish_name] }};"{% endif %}>
                                <div class="dish-name dish-ready">
                                    {{ item.dish_name }}
                                </div>
                                {% if item.ready_at %}
                                <div class="ready-time">
                                    {{ item.ready_at.strftime('%H:%M') }}
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-orders">
            <i class="bi bi-inbox" style="font-size: 4rem;"></i>
            <div class="mt-3">暂无订单</div>
            <div class="text-muted">当前没有需要制作的菜品</div>
        </div>
        {% endif %}
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar" id="statusBar">
        <div class="status-content" id="statusContent">
            <div class="scrolling-text" id="scrollingText">
                <!-- 滚动文字将通过JavaScript动态更新 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 厨房大屏配置管理器
    class KitchenDisplayManager {
        constructor() {
            this.config = {
                roomsPerPage: 5,
                carouselInterval: 10000, // 默认10秒
                statusBarFontSize: 1.2,
                scrollSpeed: 30, // 默认30秒完成一次滚动
                highContrast: true
            };
            this.currentPage = 0;
            this.totalPages = 0;
            this.rooms = [];
            this.carouselTimer = null;
            this.statusMessages = [];
        }

        // 初始化分页显示
        initializePagination() {
            this.rooms = Array.from(document.querySelectorAll('.room-column'));
            this.totalPages = Math.ceil(this.rooms.length / this.config.roomsPerPage);

            if (this.totalPages <= 1) {
                // 只有一页时隐藏页码指示器
                const pageIndicator = document.getElementById('pageIndicatorInline');
                if (pageIndicator) pageIndicator.style.display = 'none';
            } else {
                const pageIndicator = document.getElementById('pageIndicatorInline');
                if (pageIndicator) pageIndicator.style.display = 'inline-block';
                this.updatePageIndicator();
                this.showPage(0);
                this.startCarousel();
            }

            console.log(`📄 分页初始化完成: ${this.rooms.length}个包厢，共${this.totalPages}页`);
        }

        // 显示指定页面
        showPage(pageIndex) {
            if (pageIndex < 0 || pageIndex >= this.totalPages) return;

            this.currentPage = pageIndex;

            // 隐藏所有包厢
            this.rooms.forEach(room => {
                room.style.display = 'none';
            });

            // 显示当前页的包厢
            const startIndex = pageIndex * this.config.roomsPerPage;
            const endIndex = Math.min(startIndex + this.config.roomsPerPage, this.rooms.length);

            for (let i = startIndex; i < endIndex; i++) {
                if (this.rooms[i]) {
                    this.rooms[i].style.display = 'flex';
                }
            }

            this.updatePageIndicator();
            console.log(`📄 显示第${pageIndex + 1}页，包厢${startIndex + 1}-${endIndex}`);
        }

        // 更新页码指示器
        updatePageIndicator() {
            const pageIndicator = document.getElementById('pageIndicatorInline');
            if (pageIndicator) {
                pageIndicator.textContent = `第${this.currentPage + 1}页/共${this.totalPages}页`;
            }
        }

        // 开始自动轮播
        startCarousel() {
            if (this.totalPages <= 1) return;

            this.stopCarousel(); // 先停止现有的轮播

            this.carouselTimer = setInterval(() => {
                const nextPage = (this.currentPage + 1) % this.totalPages;
                this.showPage(nextPage);
            }, this.config.carouselInterval);

            console.log(`🎠 开始轮播，间隔${this.config.carouselInterval}ms`);
        }

        // 停止自动轮播
        stopCarousel() {
            if (this.carouselTimer) {
                clearInterval(this.carouselTimer);
                this.carouselTimer = null;
            }
        }

        // 更新状态栏内容
        updateStatusBar(messages = []) {
            const statusBar = document.getElementById('statusBar');
            const scrollingText = document.getElementById('scrollingText');

            if (!messages || messages.length === 0) {
                statusBar.classList.add('empty');
                return;
            }

            statusBar.classList.remove('empty');

            // 合并所有消息，最新的消息在最后（右侧）
            const sortedMessages = messages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
            const combinedText = sortedMessages.map(msg => {
                return `${msg.roomNumber}包厢：${msg.message}`;
            }).join('　　　　'); // 使用全角空格分隔

            // 检查内容是否发生变化，只有内容变化时才重新开始滚动
            const currentText = scrollingText.textContent;
            if (currentText === combinedText) {
                // 内容没有变化，不重新开始滚动
                return;
            }

            // 内容发生变化，重新开始滚动
            scrollingText.style.animation = 'none';
            scrollingText.textContent = combinedText;

            // 强制重排，然后重新开始动画
            scrollingText.offsetHeight; // 触发重排
            scrollingText.style.animation = `scrollRightOnce ${this.config.scrollSpeed}s linear forwards`;

            // 应用配置
            scrollingText.style.fontSize = `${this.config.statusBarFontSize}rem`;
            scrollingText.style.setProperty('--scroll-duration', `${this.config.scrollSpeed}s`);

            if (this.config.highContrast) {
                statusBar.classList.add('high-contrast');
            } else {
                statusBar.classList.remove('high-contrast');
            }

            console.log(`📢 状态栏更新: ${messages.length}条消息，内容已变化，重新开始滚动`);
        }

        // 加载配置
        async loadConfig() {
            try {
                const response = await fetch('/api/kitchen-display-config');
                const config = await response.json();

                // 合并配置，确保正确映射API返回的配置项
                this.config = {
                    ...this.config,
                    ...config,
                    // 确保正确映射每页显示列数
                    roomsPerPage: config.kitchen_display_rooms_per_page || this.config.roomsPerPage,
                    // 确保正确映射轮播间隔（转换为毫秒）
                    carouselInterval: (config.kitchen_display_auto_flip_interval || 10) * 1000
                };

                // 应用包厢宽度配置
                this.applyRoomWidth();

                // 应用字体配置
                this.applyFontSettings();

                console.log('⚙️ 厨房大屏配置已加载:', this.config);
                return this.config;
            } catch (error) {
                console.error('加载配置失败:', error);
                return this.config; // 返回默认配置
            }
        }

        // 应用包厢宽度配置
        applyRoomWidth() {
            const roomWidth = this.config.kitchen_display_room_width || 370;
            const roomsPerPage = this.config.kitchen_display_rooms_per_page || this.config.roomsPerPage || 5;

            // 更新内部配置
            this.config.roomsPerPage = roomsPerPage;

            // 应用CSS变量
            document.documentElement.style.setProperty('--room-width', `${roomWidth}px`);
            document.documentElement.style.setProperty('--rooms-per-page', roomsPerPage);

            // 如果列数发生变化，重新初始化分页
            if (this.rooms && this.rooms.length > 0) {
                const newTotalPages = Math.ceil(this.rooms.length / roomsPerPage);
                if (newTotalPages !== this.totalPages) {
                    this.initializePagination();
                    console.log(`🔄 分页重新初始化: ${this.rooms.length}个包厢, ${roomsPerPage}列/页, 共${newTotalPages}页`);
                }
            }

            console.log(`📐 包厢宽度设置为: ${roomWidth}px, 每页显示: ${roomsPerPage}列`);
        }

        // 应用字体配置
        applyFontSettings() {
            const autoFontSize = this.config.kitchen_display_auto_font_size === 'true';
            const dishFontSize = this.config.kitchen_display_font_size || 14;

            if (autoFontSize) {
                // 启用自适应字体
                document.body.classList.add('auto-font-size');
                console.log('🔤 启用自适应字体大小');
            } else {
                // 使用固定字体大小
                document.body.classList.remove('auto-font-size');
                document.documentElement.style.setProperty('--dish-font-size', `${dishFontSize}px`);
                console.log(`🔤 固定字体大小设置为: ${dishFontSize}px`);
            }
        }

        // 获取服务员指令
        async fetchWaiterActions() {
            try {
                const response = await fetch('/api/kitchen-display/waiter-actions');
                const actions = await response.json();

                // 确保actions是数组
                const actionList = Array.isArray(actions) ? actions : [];

                // 过滤未处理的指令
                const unprocessedActions = actionList.filter(action => !action.is_processed);

                // 转换为状态栏消息格式
                const messages = unprocessedActions.map(action => {
                    // 组合命令名和内容
                    let message = action.action_type_display || action.action_type;
                    if (action.action_content && action.action_content.trim()) {
                        message = `${message}：${action.action_content}`;
                    }
                    return {
                        roomNumber: action.room_number,
                        message: message,
                        timestamp: action.created_at
                    };
                });

                console.log(`📢 获取到 ${actionList.length} 条指令，其中 ${unprocessedActions.length} 条未处理`);
                this.updateStatusBar(messages);
                return messages;
            } catch (error) {
                console.error('获取服务员指令失败:', error);
                this.updateStatusBar([]);
                return [];
            }
        }

        // 不再隐藏已完成菜品，而是确保它们正确显示在底部
        hideCompletedDishes() {
            // 移除隐藏逻辑，已完成菜品现在显示在底部
            console.log(`✅ 已完成菜品现在显示在各包厢底部`);
        }

        // 初始化厨房大屏
        async initialize() {
            console.log('🚀 初始化厨房大屏管理器...');

            // 加载配置
            await this.loadConfig();

            // 隐藏已完成菜品
            this.hideCompletedDishes();

            // 初始化分页
            this.initializePagination();

            // 获取并显示服务员指令
            await this.fetchWaiterActions();

            // 设置定时更新
            this.startPeriodicUpdate();

            console.log('✅ 厨房大屏初始化完成');
        }

        // 开始定时更新
        startPeriodicUpdate() {
            // 每5秒更新一次服务员指令
            setInterval(() => {
                this.fetchWaiterActions();
            }, 5000);

            // 每10秒隐藏已完成菜品（防止动态加载的菜品显示）
            setInterval(() => {
                this.hideCompletedDishes();
            }, 10000);

            // 每15秒进行局部数据更新（不刷新页面）
            setInterval(() => {
                this.refreshData();
            }, 15000);

            // 每30秒重新加载配置
            setInterval(() => {
                this.loadConfig().then(() => {
                    // 应用新配置
                    this.applyConfig();
                });
            }, 30000);
        }

        // 应用配置
        applyConfig() {
            // 应用包厢宽度和列数配置（这会触发分页重新初始化）
            this.applyRoomWidth();

            // 应用字体配置
            this.applyFontSettings();

            // 重新启动轮播（使用新的间隔时间）
            if (this.totalPages > 1) {
                this.stopCarousel();
                this.startCarousel();
                console.log(`🔄 轮播间隔已更新为: ${this.config.carouselInterval}ms`);
            }

            // 更新状态栏样式
            const scrollingText = document.getElementById('scrollingText');
            if (scrollingText) {
                scrollingText.style.fontSize = `${this.config.statusBarFontSize}rem`;
                scrollingText.style.setProperty('--scroll-duration', `${this.config.scrollSpeed}s`);
            }

            const statusBar = document.getElementById('statusBar');
            if (statusBar) {
                if (this.config.highContrast) {
                    statusBar.classList.add('high-contrast');
                } else {
                    statusBar.classList.remove('high-contrast');
                }
            }

            console.log('⚙️ 配置已应用');
        }

        // 局部数据更新 - 不刷新页面，保持分页状态
        async refreshData() {
            try {
                console.log('🔄 开始局部数据更新...');

                // 获取最新的厨房数据
                const response = await fetch('/kitchen/display');
                const html = await response.text();

                // 解析HTML获取新的包厢数据
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newRoomsPage = doc.querySelector('#roomsPage');

                if (newRoomsPage) {
                    // 保存当前分页状态
                    const currentPage = this.currentPage;
                    const wasCarouselRunning = this.carouselTimer !== null;

                    // 更新包厢数据
                    const currentRoomsPage = document.getElementById('roomsPage');
                    if (currentRoomsPage) {
                        currentRoomsPage.innerHTML = newRoomsPage.innerHTML;
                    }

                    // 更新状态统计
                    const newStartedCount = doc.querySelector('#startedRoomsCount');
                    const newNotStartedCount = doc.querySelector('#notStartedRoomsCount');
                    if (newStartedCount && newNotStartedCount) {
                        const currentStartedCount = document.getElementById('startedRoomsCount');
                        const currentNotStartedCount = document.getElementById('notStartedRoomsCount');
                        if (currentStartedCount) currentStartedCount.textContent = newStartedCount.textContent;
                        if (currentNotStartedCount) currentNotStartedCount.textContent = newNotStartedCount.textContent;
                    }

                    // 重新初始化分页，但保持当前页面
                    this.rooms = Array.from(document.querySelectorAll('.room-column'));
                    this.totalPages = Math.ceil(this.rooms.length / this.config.roomsPerPage);

                    // 确保当前页面不超出范围
                    if (currentPage >= this.totalPages) {
                        this.currentPage = 0;
                    } else {
                        this.currentPage = currentPage;
                    }

                    // 恢复分页显示
                    this.showPage(this.currentPage);
                    this.updatePageIndicator();

                    // 恢复轮播状态
                    if (wasCarouselRunning && this.totalPages > 1) {
                        this.startCarousel();
                    }

                    // 隐藏已完成菜品
                    this.hideCompletedDishes();

                    // 应用2列布局
                    setTimeout(() => {
                        applyFixedDishLayout();
                    }, 100);

                    console.log(`✅ 数据更新完成，当前第${this.currentPage + 1}页/共${this.totalPages}页`);
                }

                // 更新服务员指令
                await this.fetchWaiterActions();

            } catch (error) {
                console.error('❌ 局部数据更新失败:', error);
            }
        }

    }

    // 创建全局厨房大屏管理器
    const kitchenDisplayManager = new KitchenDisplayManager();

    // 添加全局函数供调试使用
    window.kitchenDisplay = kitchenDisplayManager;

    window.showPage = function(pageIndex) {
        kitchenDisplayManager.showPage(pageIndex);
    };

    window.updateConfig = function(newConfig) {
        kitchenDisplayManager.config = { ...kitchenDisplayManager.config, ...newConfig };
        kitchenDisplayManager.applyConfig();
    };

    window.refreshActions = function() {
        kitchenDisplayManager.fetchWaiterActions();
    };

    // 更新当前时间
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // 每秒更新时间
    setInterval(updateTime, 1000);
    updateTime();

    // 全屏切换功能
    function toggleFullscreen() {
        if (!document.fullscreenElement) {
            // 进入全屏
            document.documentElement.requestFullscreen().then(() => {
                updateFullscreenButton(true);
            }).catch(err => {
                console.log('进入全屏失败:', err);
            });
        } else {
            // 退出全屏
            document.exitFullscreen().then(() => {
                updateFullscreenButton(false);
            }).catch(err => {
                console.log('退出全屏失败:', err);
            });
        }
    }

    // 更新全屏按钮状态
    function updateFullscreenButton(isFullscreen) {
        const btn = document.getElementById('fullscreenBtn');
        if (btn) {
            if (isFullscreen) {
                btn.innerHTML = '<i class="bi bi-fullscreen-exit"></i> 退出全屏';
                btn.title = '退出全屏';
            } else {
                btn.innerHTML = '<i class="bi bi-fullscreen"></i> 全屏';
                btn.title = '进入全屏';
            }
        }
    }

    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', function() {
        updateFullscreenButton(!!document.fullscreenElement);
    });

    // 厨房大屏配置
    let kitchenDisplayConfig = {
        rooms_per_page: 5,
        font_size: 14,
        layout_mode: 'auto',
        grid_columns: 3,
        grid_rows: 2,
        auto_flip_interval: 8,
        auto_flip_enabled: true,
        hide_navigation: true
    };

    // 加载厨房大屏配置
    function loadKitchenDisplayConfig() {
        fetch('/api/kitchen-display-config')
            .then(response => response.json())
            .then(config => {
                kitchenDisplayConfig = {
                    rooms_per_page: config.kitchen_display_rooms_per_page || 5,
                    font_size: config.kitchen_display_font_size || 14,
                    layout_mode: config.kitchen_display_layout_mode || 'auto',
                    grid_columns: config.kitchen_display_grid_columns || 3,
                    grid_rows: config.kitchen_display_grid_rows || 2,
                    auto_flip_interval: config.kitchen_display_auto_flip_interval || 8,
                    auto_flip_enabled: config.kitchen_display_auto_flip_enabled !== false,
                    hide_navigation: config.kitchen_display_hide_navigation !== false
                };

                console.log('🔧 厨房大屏配置已加载:', kitchenDisplayConfig);

                // 应用配置
                applyKitchenDisplayConfig();
            })
            .catch(error => {
                console.error('❌ 加载厨房大屏配置失败:', error);
                // 使用默认配置
                applyKitchenDisplayConfig();
            });
    }

    // 应用厨房大屏配置
    function applyKitchenDisplayConfig() {
        // 完全隐藏导航菜单栏
        const navbar = document.querySelector('.navbar');
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');

        if (navbar) {
            navbar.style.display = 'none';
            console.log('🖥️ 导航栏已隐藏');
        }

        if (sidebar) {
            sidebar.style.display = 'none';
            console.log('🖥️ 侧边栏已隐藏');
        }

        if (mainContent) {
            mainContent.style.marginLeft = '0';
            mainContent.style.width = '100%';
            console.log('🖥️ 主内容区域已扩展到全宽');
        }

        // 应用智能布局
        applySmartLayout();

        // 应用字体大小
        applyFontSize(kitchenDisplayConfig.font_size);

        // 启用自动翻页
        if (kitchenDisplayConfig.auto_flip_enabled) {
            startAutoFlip();
        }
    }

    // 智能布局应用
    function applySmartLayout() {
        const roomsContainer = document.querySelector('.rooms-container');
        if (!roomsContainer) return;

        // 计算当前包厢数量
        const roomCount = roomsContainer.children.length;

        // 设置data属性用于CSS选择器
        roomsContainer.setAttribute('data-room-count', roomCount);

        console.log(`📐 智能布局已应用，包厢数量: ${roomCount}`);

        // 根据包厢数量调整高度
        let containerHeight;
        if (roomCount <= 3) {
            containerHeight = 'calc(100vh - 90px)';
        } else if (roomCount <= 6) {
            containerHeight = 'calc(100vh - 85px)';
        } else {
            containerHeight = 'calc(100vh - 80px)';
        }

        roomsContainer.style.height = containerHeight;
    }

    // 应用字体大小
    function applyFontSize(fontSize) {
        const style = document.createElement('style');
        style.textContent = `
            .dish-name {
                font-size: ${fontSize}px !important;
            }
            .room-header {
                font-size: ${Math.max(fontSize + 2, 16)}px !important;
            }
        `;
        document.head.appendChild(style);
        console.log(`🔤 字体大小已设置为: ${fontSize}px`);
    }

    // 移除旧的网格布局函数，已被智能布局替代

    // 固定2列菜品布局 - 始终使用2列网格布局
    function applyFixedDishLayout() {
        const roomColumns = document.querySelectorAll('.room-column');

        roomColumns.forEach(roomColumn => {
            const dishItems = roomColumn.querySelectorAll('.dish-item');
            const dishCount = dishItems.length;

            // 确保菜品容器始终使用2列网格布局
            const dishContainer = roomColumn.querySelector('.dish-items-container');
            if (dishContainer) {
                // 强制应用2列网格布局
                dishContainer.style.setProperty('display', 'grid', 'important');
                dishContainer.style.setProperty('grid-template-columns', '1fr 1fr', 'important');
                dishContainer.style.setProperty('gap', '2px', 'important');
                dishContainer.style.setProperty('grid-auto-rows', 'min-content', 'important');

                console.log(`📐 包厢 ${roomColumn.querySelector('.room-header')?.textContent} 应用2列布局，菜品数量: ${dishCount}`);
            }

            // 根据菜品数量调整字体大小
            adjustFontSizeForRoom(roomColumn, dishCount);
        });

        console.log('🎯 固定2列菜品布局已应用');
    }

    // 为单个包厢调整字体大小
    function adjustFontSizeForRoom(roomColumn, dishCount) {
        const baseFontSize = kitchenDisplayConfig.font_size;
        let adjustedFontSize = baseFontSize;

        // 根据菜品数量调整字体大小
        if (dishCount > 15) {
            adjustedFontSize = Math.max(baseFontSize - 3, 10); // 最小10px
        } else if (dishCount > 10) {
            adjustedFontSize = Math.max(baseFontSize - 2, 11); // 最小11px
        } else if (dishCount > 6) {
            adjustedFontSize = Math.max(baseFontSize - 1, 12); // 最小12px
        }

        // 应用字体大小
        const dishNames = roomColumn.querySelectorAll('.dish-name');
        dishNames.forEach(dishName => {
            dishName.style.fontSize = `${adjustedFontSize}px`;
        });

        // 调整包厢标题字体
        const roomHeader = roomColumn.querySelector('.room-header');
        if (roomHeader) {
            roomHeader.style.fontSize = `${Math.max(adjustedFontSize + 2, 14)}px`;
        }
    }

    // 监听窗口大小变化，重新应用布局
    window.addEventListener('resize', function() {
        setTimeout(() => {
            applyFixedDishLayout();
        }, 300);
    });

    // 自动翻页功能
    let autoFlipTimer = null;
    let currentPage = 0;
    let totalPages = 1;

    function startAutoFlip() {
        if (autoFlipTimer) {
            clearInterval(autoFlipTimer);
        }

        if (kitchenDisplayConfig.auto_flip_enabled && kitchenDisplayConfig.auto_flip_interval > 0) {
            autoFlipTimer = setInterval(() => {
                flipToNextPage();
            }, kitchenDisplayConfig.auto_flip_interval * 1000);

            console.log(`⏰ 自动翻页已启用，间隔: ${kitchenDisplayConfig.auto_flip_interval}秒`);
        }
    }

    function flipToNextPage() {
        // 这里实现翻页逻辑
        currentPage = (currentPage + 1) % totalPages;
        console.log(`📄 翻页到第 ${currentPage + 1} 页`);
        // TODO: 实现实际的翻页显示逻辑
    }

    // 菜品布局优化数据
    const dishLayoutMap = {{ dish_layout_map|tojson if dish_layout_map else '{}' }};
    const dishRoomMapping = {{ dish_room_mapping|tojson if dish_room_mapping else '{}' }};

    // 应用菜品布局优化
    function applyDishLayoutOptimization() {
        console.log('🎯 应用菜品布局优化');
        console.log('菜品布局映射:', dishLayoutMap);
        console.log('菜品包厢映射:', dishRoomMapping);

        // 为相同菜品添加特殊标识
        Object.keys(dishRoomMapping).forEach(dishName => {
            const roomList = dishRoomMapping[dishName];
            if (roomList.length > 1) {
                // 这个菜品出现在多个包厢中
                const dishElements = document.querySelectorAll('.dish-item');
                dishElements.forEach(element => {
                    const dishNameElement = element.querySelector('.dish-name');
                    if (dishNameElement && dishNameElement.textContent.trim() === dishName) {
                        element.classList.add('multi-room-dish');
                        element.setAttribute('data-dish-name', dishName);
                        element.setAttribute('data-room-count', roomList.length);
                    }
                });
            }
        });

        // 添加菜品位置信息
        Object.keys(dishLayoutMap).forEach(dishName => {
            const position = dishLayoutMap[dishName];
            const dishElements = document.querySelectorAll(`[data-dish-name="${dishName}"]`);
            dishElements.forEach(element => {
                element.setAttribute('data-layout-position', position);
                element.style.order = position; // 使用CSS order属性控制显示顺序
            });
        });
    }

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadKitchenDisplayConfig();
        applyDishLayoutOptimization();

        // 确保智能布局被应用
        setTimeout(() => {
            applySmartLayout();
        }, 100);
    });

    // 按ESC键恢复导航菜单（仅在配置允许时）
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !kitchenDisplayConfig.hide_navigation) {
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                navbar.style.display = navbar.style.display === 'none' ? 'block' : 'none';
                console.log('🖥️ 切换导航菜单显示状态');
            }
        }
    });

    // 切换菜品状态（划菜操作）
    function toggleDishStatus(itemId, currentStatus, roomName) {
        let newStatus;
        let actionText = '';

        switch(currentStatus) {
            case 'pending_cook':
                newStatus = 'cooking';
                actionText = '开始制作';
                break;
            case 'cooking':
                newStatus = 'ready';
                actionText = '制作完成';
                break;
            case 'ready':
                newStatus = 'served';
                actionText = '已完成';
                break;
            case 'served':
                return; // 已完成的不能再操作
            default:
                return;
        }

        if (!confirm(`确认${actionText}此菜品？`)) {
            return;
        }

        fetch(`/kitchen/items/${itemId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                status: newStatus
            })
        }).then(response => {
            if (response.ok) {
                // 获取菜品名称
                const dishElement = document.querySelector(`[onclick*="${itemId}"]`);
                const dishName = dishElement.querySelector('.dish-name').textContent.trim();

                // 如果是制作完成，保留语音播报但不显示Toast通知
                if (newStatus === 'ready') {
                    // 语音播报菜品完成
                    if ('speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance(`${roomName}${dishName}制作完成，跑菜`);
                        utterance.lang = 'zh-CN';
                        speechSynthesis.speak(utterance);
                    }
                }

                // 局部更新数据而不刷新页面
                setTimeout(() => {
                    kitchenDisplayManager.refreshData();
                }, 1000);
            } else {
                console.error('❌ 菜品状态切换失败');
            }
        });
    }

    // 处理服务员指令
    function processCommand(commandId) {
        if (confirm('确认处理此指令？')) {
            fetch(`/kitchen/process-command/${commandId}`, {
                method: 'POST'
            }).then(response => {
                if (response.ok) {
                    // 语音播报指令已处理
                    if ('speechSynthesis' in window) {
                        const utterance = new SpeechSynthesisUtterance('指令已处理');
                        utterance.lang = 'zh-CN';
                        speechSynthesis.speak(utterance);
                    }
                    // 局部更新数据而不刷新页面
                    kitchenDisplayManager.refreshData();
                } else {
                    console.error('❌ 指令处理失败');
                }
            });
        }
    }

    // 跟踪已显示的通知，避免重复显示
    let lastNotifiedActionId = null;
    let lastNotifiedDishId = null;
    let processedActionIds = new Set();  // 记录已处理的指令ID
    let processedDishIds = new Set();    // 记录已处理的菜品ID

    // 页面加载时从localStorage恢复已处理的指令ID
    function loadProcessedIds() {
        try {
            const savedActionIds = localStorage.getItem('processedActionIds');
            const savedDishIds = localStorage.getItem('processedDishIds');

            if (savedActionIds) {
                processedActionIds = new Set(JSON.parse(savedActionIds));
            }
            if (savedDishIds) {
                processedDishIds = new Set(JSON.parse(savedDishIds));
            }

            console.log(`📋 已恢复处理记录: ${processedActionIds.size}个指令, ${processedDishIds.size}个菜品`);
        } catch (error) {
            console.error('恢复处理记录失败:', error);
        }
    }

    // 保存已处理的指令ID到localStorage
    function saveProcessedIds() {
        try {
            localStorage.setItem('processedActionIds', JSON.stringify([...processedActionIds]));
            localStorage.setItem('processedDishIds', JSON.stringify([...processedDishIds]));
        } catch (error) {
            console.error('保存处理记录失败:', error);
        }
    }

    // WebSocket模拟监听（轮询方式）
    function checkForNotifications() {
        // 检查服务员指令
        fetch('/api/kitchen-display/waiter-actions')
            .then(response => response.json())
            .then(actions => {
                // 确保actions是数组
                const actionList = Array.isArray(actions) ? actions : [];

                if (actionList.length > 0) {
                    // 检查是否有新的指令
                    const latestAction = actionList[0];
                    const actionTime = new Date(latestAction.created_at);
                    const now = new Date();
                    const timeDiff = now - actionTime;

                    // 如果指令是最近5分钟内的，并且还没有处理过
                    console.log(`🔍 检查指令: ID=${latestAction.id}, 时间差=${timeDiff}ms, 已处理=${processedActionIds.has(latestAction.id)}`);
                    if (timeDiff < 300000 && !processedActionIds.has(latestAction.id)) {
                        // 显示弹出通知 - 组合命令名和内容
                        let message = latestAction.action_type_display || latestAction.action_type;
                        if (latestAction.action_content && latestAction.action_content.trim()) {
                            message = `${message}：${latestAction.action_content}`;
                        }
                        console.log(`🎉 准备显示通知: ${latestAction.room_number} - ${message}`);
                        showNotification('instruction', latestAction.room_number, message, 8000);

                        // 语音播报服务员指令（使用统一播报队列）
                        addToBroadcastQueue(`${latestAction.room_number}包厢${message}`, '服务员指令');

                        // 记录已处理的指令ID
                        processedActionIds.add(latestAction.id);
                        lastNotifiedActionId = latestAction.id;
                        saveProcessedIds();  // 保存到localStorage
                        console.log(`📢 新服务员指令通知: ${latestAction.room_number} - ${message}`);
                    } else {
                        console.log(`⏰ 指令不符合通知条件: 时间差=${timeDiff}ms, 已通知=${latestAction.id === lastNotifiedActionId}`);
                    }
                }
            })
            .catch(error => {
                console.log('检查指令失败:', error);
            });

        // 检查菜品制作完成通知
        fetch('/api/dish-ready/latest')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.dishes && data.dishes.length > 0) {
                    data.dishes.forEach(dish => {
                        const readyTime = new Date(dish.ready_at);
                        const now = new Date();
                        const timeDiff = now - readyTime;

                        // 如果菜品是最近10秒内制作完成的，且未处理过，显示Toast通知和语音播报
                        if (timeDiff < 10000 && !processedDishIds.has(dish.id)) {
                            // 显示Toast通知
                            const dishReadyMessage = `${dish.dish_name}，跑菜`;
                            showNotification('dish-ready', dish.room_number, dishReadyMessage, 5000);

                            // 语音播报菜品完成
                            if ('speechSynthesis' in window) {
                                const utterance = new SpeechSynthesisUtterance(`${dish.room_number}${dish.dish_name}，跑菜`);
                                utterance.lang = 'zh-CN';
                                speechSynthesis.speak(utterance);
                            }

                            // 记录已处理的菜品ID
                            processedDishIds.add(dish.id);
                            saveProcessedIds();  // 保存到localStorage
                            console.log(`🍽️ 菜品完成通知: ${dish.room_number} - ${dish.dish_name}`);
                        }
                    });
                }
            })
            .catch(error => {
                console.log('检查菜品完成失败:', error);
            });
    }

    // 显示中央通知
    function showNotification(type, roomNumber, message, duration = 5000) {
        console.log(`🚀 showNotification 被调用: type=${type}, roomNumber=${roomNumber}, message=${message}`);

        // 创建通知容器（如果不存在）
        let notificationCenter = document.getElementById('notificationCenter');
        if (!notificationCenter) {
            console.log(`📦 创建新的通知中心容器`);
            notificationCenter = document.createElement('div');
            notificationCenter.id = 'notificationCenter';
            notificationCenter.className = 'notification-center';
            document.body.appendChild(notificationCenter);
        } else {
            console.log(`📦 使用现有的通知中心容器`);
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification-item ${type}`;

        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-room">${roomNumber}</div>
                <div class="notification-message">${message}</div>
                <div class="notification-type">${getTypeDisplayName(type)}</div>
            </div>
        `;

        console.log(`✨ 通知元素已创建，类名: ${notification.className}`);

        // 添加到通知中心
        notificationCenter.appendChild(notification);
        console.log(`📌 通知已添加到DOM，当前通知数量: ${notificationCenter.children.length}`);

        // 自动移除通知
        setTimeout(() => {
            console.log(`⏰ 开始淡出通知: ${message}`);
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                    console.log(`🗑️ 通知已从DOM移除: ${message}`);
                }
            }, 500);
        }, duration);

        console.log(`📢 显示通知完成: ${type} - ${roomNumber} - ${message}`);
    }

    // 获取类型显示名称
    function getTypeDisplayName(type) {
        const typeNames = {
            'instruction': '服务员指令',
            'dish-ready': '菜品完成',
            'dining-ended': '用餐结束'
        };
        return typeNames[type] || type;
    }

    // 模拟WebSocket事件处理 - 显示弹出通知和语音播报
    function handleWebSocketMessage(message) {
        switch(message.type) {
            case 'waiter_instruction':
                // 显示弹出通知
                showNotification('instruction', message.room_number, message.instruction, 8000);

                // 语音播报（使用统一播报队列）
                addToBroadcastQueue(`${message.room_number}包厢${message.instruction}`, '服务员指令');
                break;

            case 'dish_ready':
                // 显示Toast通知
                const dishReadyMessage = `${message.dish_name}，跑菜`;
                showNotification('dish-ready', message.room_number, dishReadyMessage, 5000);

                // 语音播报
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(`${message.room_number}${message.dish_name}，跑菜`);
                    utterance.lang = 'zh-CN';
                    speechSynthesis.speak(utterance);
                }
                break;

            case 'dish_completion_from_helper':
                // 打荷员确认菜品完成的通知
                showNotification('dish-completion', message.room_number, message.message, 4000);

                // 语音播报
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(message.message);
                    utterance.lang = 'zh-CN';
                    speechSynthesis.speak(utterance);
                }
                break;

            case 'dining_started':
                // 显示用餐开始通知
                const startMessage = `${message.room_number}包厢开始用餐，${message.guest_count}人`;
                showNotification('dining-start', message.room_number, startMessage, 6000);

                // 语音播报
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(`${message.room_number}包厢${message.guest_count}人起菜`);
                    utterance.lang = 'zh-CN';
                    speechSynthesis.speak(utterance);
                }

                // 立即更新厨房显示数据
                setTimeout(() => {
                    kitchenDisplayManager.refreshData();
                }, 1000);
                break;

            case 'dining_ended':
                // 只进行语音播报，不显示Toast通知
                if ('speechSynthesis' in window) {
                    const utterance = new SpeechSynthesisUtterance(`${message.room_number}包厢用餐结束`);
                    utterance.lang = 'zh-CN';
                    speechSynthesis.speak(utterance);
                }
                // 结束用餐后局部更新数据
                setTimeout(() => {
                    kitchenDisplayManager.refreshData();
                }, 3000);
                break;

            case 'kitchen_refresh':
                // 局部更新数据而不刷新页面
                setTimeout(() => {
                    kitchenDisplayManager.refreshData();
                }, 1000);
                break;
        }
    }

    // 定期检查通知（模拟WebSocket）
    setInterval(checkForNotifications, 5000); // 每5秒检查一次

    // 移除自动刷新，改为局部数据更新
    // 每30秒更新数据但不刷新页面
    setInterval(function() {
        kitchenDisplayManager.refreshData();
    }, 30000);

    // 页面加载时检查一次
    setTimeout(checkForNotifications, 2000);

    // ===== 全屏功能实现 =====

    // 全屏状态管理
    const FullscreenManager = {
        // 检查是否支持全屏API
        isSupported() {
            return !!(document.documentElement.requestFullscreen ||
                     document.documentElement.webkitRequestFullscreen ||
                     document.documentElement.mozRequestFullScreen ||
                     document.documentElement.msRequestFullscreen);
        },

        // 进入全屏
        async enter() {
            try {
                const element = document.documentElement;
                if (element.requestFullscreen) {
                    await element.requestFullscreen();
                } else if (element.webkitRequestFullscreen) {
                    await element.webkitRequestFullscreen();
                } else if (element.mozRequestFullScreen) {
                    await element.mozRequestFullScreen();
                } else if (element.msRequestFullscreen) {
                    await element.msRequestFullscreen();
                }
                return true;
            } catch (error) {
                console.error('进入全屏失败:', error);
                return false;
            }
        },

        // 退出全屏
        async exit() {
            try {
                if (document.exitFullscreen) {
                    await document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    await document.webkitExitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    await document.mozCancelFullScreen();
                } else if (document.msExitFullscreen) {
                    await document.msExitFullscreen();
                }
                return true;
            } catch (error) {
                console.error('退出全屏失败:', error);
                return false;
            }
        },

        // 检查当前是否处于全屏状态
        isFullscreen() {
            return !!(document.fullscreenElement ||
                     document.webkitFullscreenElement ||
                     document.mozFullScreenElement ||
                     document.msFullscreenElement);
        },

        // 保存全屏状态到localStorage
        saveState(isFullscreen) {
            try {
                localStorage.setItem('kitchen_display_fullscreen', isFullscreen ? 'true' : 'false');
                console.log('💾 全屏状态已保存:', isFullscreen);
            } catch (error) {
                console.error('保存全屏状态失败:', error);
            }
        },

        // 从localStorage读取全屏状态
        loadState() {
            try {
                const saved = localStorage.getItem('kitchen_display_fullscreen');
                return saved === 'true';
            } catch (error) {
                console.error('读取全屏状态失败:', error);
                return false;
            }
        },

        // 更新全屏按钮状态
        updateButton() {
            const btn = document.getElementById('fullscreenBtn');
            const icon = document.getElementById('fullscreenIcon');
            const text = document.getElementById('fullscreenText');

            if (!btn || !icon || !text) return;

            if (this.isFullscreen()) {
                icon.className = 'bi bi-fullscreen-exit';
                text.textContent = '退出全屏';
                btn.title = '退出全屏模式 (F11 或 ESC)';
            } else {
                icon.className = 'bi bi-arrows-fullscreen';
                text.textContent = '全屏';
                btn.title = '切换全屏模式 (F11)';
            }
        }
    };

    // 全屏切换函数
    async function toggleFullscreen() {
        if (!FullscreenManager.isSupported()) {
            console.warn('⚠️ 您的浏览器不支持全屏功能');
            return;
        }

        try {
            if (FullscreenManager.isFullscreen()) {
                const success = await FullscreenManager.exit();
                if (success) {
                    FullscreenManager.saveState(false);
                }
            } else {
                const success = await FullscreenManager.enter();
                if (success) {
                    FullscreenManager.saveState(true);
                }
            }
        } catch (error) {
            console.error('全屏切换失败:', error);
        }
    }

    // 监听全屏状态变化事件
    function handleFullscreenChange() {
        const isFullscreen = FullscreenManager.isFullscreen();
        FullscreenManager.updateButton();
        FullscreenManager.saveState(isFullscreen);

        console.log('🖥️ 全屏状态变化:', isFullscreen ? '已进入全屏' : '已退出全屏');
    }

    // 绑定全屏状态变化事件监听器
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    // 键盘快捷键支持
    document.addEventListener('keydown', function(event) {
        // F11键切换全屏
        if (event.key === 'F11') {
            event.preventDefault();
            toggleFullscreen();
        }
        // ESC键退出全屏（浏览器默认行为，这里只是记录）
        if (event.key === 'Escape' && FullscreenManager.isFullscreen()) {
            // 浏览器会自动退出全屏，我们只需要更新状态
            setTimeout(() => {
                FullscreenManager.updateButton();
                FullscreenManager.saveState(false);
            }, 100);
        }
    });

    // 语音播报相关变量
    let processedNotifications = new Set(); // 记录已处理的通知ID
    let voiceConfig = null; // 语音配置缓存
    let broadcastQueue = []; // 统一播报队列
    let isBroadcasting = false; // 是否正在播报

    // 获取语音配置
    async function getVoiceConfig() {
        if (voiceConfig) {
            return voiceConfig;
        }

        try {
            const response = await fetch('/api/voice-config', {
                credentials: 'include'
            });
            const data = await response.json();
            voiceConfig = data;
            return data;
        } catch (error) {
            console.log('获取语音配置失败:', error);
            // 返回默认配置
            voiceConfig = {
                voice_enabled: true,
                voice_repeat_count: 2,
                voice_repeat_interval: 3,
                voice_rate: 0.8,
                voice_volume: 1.0,
                voice_pitch: 1.0
            };
            return voiceConfig;
        }
    }

    // 检查打荷员菜品完成通知（修复版本）
    function checkDishCompletionNotifications() {
        fetch('/api/kitchen/dish-completion-notifications', {
            credentials: 'include' // 包含cookies进行认证
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.notifications && data.notifications.length > 0) {
                    console.log(`📋 收到 ${data.notifications.length} 条菜品完成通知`);

                    data.notifications.forEach(notification => {
                        // 检查是否已经处理过这个通知
                        if (!processedNotifications.has(notification.id)) {
                            console.log(`🆕 处理新通知: ID=${notification.id}, 消息=${notification.message}`);

                            // 显示Toast通知
                            showDishCompletionNotification(notification);

                            // 添加到语音播报队列
                            addToDishBroadcastQueue(notification);

                            // 标记为已处理
                            processedNotifications.add(notification.id);
                            console.log(`✅ 菜品完成通知已处理: ${notification.message}`);
                        } else {
                            console.log(`⏭️ 跳过已处理通知: ID=${notification.id}`);
                        }
                    });
                } else {
                    // 只在有调试需要时输出
                    // console.log('暂无新的菜品完成通知');
                }
            })
            .catch(error => {
                console.error('检查菜品完成通知失败:', error);
            });
    }

    // 显示菜品完成通知（修复：使用标准Toast通知）
    function showDishCompletionNotification(notification) {
        // 修复：使用标准的showNotification函数，确保显示效果一致
        const message = `${notification.dish_name}，跑菜`;
        showNotification('dish-ready', notification.room_number, message, 4000);

        console.log(`🍽️ 显示菜品完成通知: ${notification.room_number} - ${message}`);
    }

    // 统一的语音播报函数
    async function addToBroadcastQueue(message, type = 'general') {
        const config = await getVoiceConfig();
        if (!config.voice_enabled) {
            return;
        }

        broadcastQueue.push({
            message: message,
            type: type,
            repeatCount: config.voice_repeat_count || 2,
            interval: (config.voice_repeat_interval || 3) * 1000,
            config: config
        });

        // 如果当前没有在播报，开始播报
        if (!isBroadcasting) {
            processBroadcastQueue();
        }
    }

    // 处理统一播报队列
    async function processBroadcastQueue() {
        if (broadcastQueue.length === 0) {
            isBroadcasting = false;
            return;
        }

        isBroadcasting = true;
        const item = broadcastQueue.shift();

        // 播报指定次数
        for (let i = 0; i < item.repeatCount; i++) {
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(item.message);
                utterance.lang = 'zh-CN';
                utterance.rate = item.config.voice_rate || 0.8;
                utterance.volume = item.config.voice_volume || 1.0;
                utterance.pitch = item.config.voice_pitch || 1.0;
                speechSynthesis.speak(utterance);

                console.log(`🔊 ${item.type}播报 (${i + 1}/${item.repeatCount}): ${item.message}`);
            }

            // 如果不是最后一次播报，等待间隔时间
            if (i < item.repeatCount - 1) {
                await new Promise(resolve => setTimeout(resolve, item.interval));
            }
        }

        // 播报完成后，继续处理队列中的下一个
        setTimeout(() => {
            processBroadcastQueue();
        }, 1000);
    }

    // 添加到菜品完成播报队列（兼容旧函数名）
    async function addToDishBroadcastQueue(notification) {
        await addToBroadcastQueue(notification.message, '菜品完成');
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', async function() {
        console.log('🚀 厨房大屏页面加载完成，开始初始化...');

        // 恢复已处理的指令记录
        loadProcessedIds();

        // 立即隐藏导航栏和侧边栏
        applyKitchenDisplayConfig();

        // 初始化厨房大屏管理器
        await kitchenDisplayManager.initialize();

        // 应用固定2列菜品布局
        setTimeout(() => {
            applyFixedDishLayout();
        }, 500);

        // 定期检查并确保2列布局
        setInterval(() => {
            applyFixedDishLayout();
        }, 10000); // 减少频率到10秒

        // 定期检查打荷员菜品完成通知
        setInterval(checkDishCompletionNotifications, 3000);

        // 延迟执行全屏相关逻辑
        setTimeout(async function() {
            const shouldBeFullscreen = FullscreenManager.loadState();

            if (shouldBeFullscreen && !FullscreenManager.isFullscreen()) {
                console.log('🔄 恢复全屏状态...');
                try {
                    // 尝试自动进入全屏
                    const success = await FullscreenManager.enter();
                    if (!success) {
                        console.log('⚠️ 自动进入全屏失败，可能需要用户交互');
                        // 全屏提示已移除，简化界面
                    }
                } catch (error) {
                    console.log('⚠️ 自动进入全屏失败:', error.message);
                    // 全屏提示已移除，简化界面
                }
            }

            // 更新按钮状态
            FullscreenManager.updateButton();
        }, 1000);
    });

    // 处理指令（保留用于兼容性，但不显示通知）
    async function processCommand(commandId) {
        try {
            const response = await fetch(`/api/waiter-actions/${commandId}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                console.log('✅ 指令处理成功');
                // 刷新状态栏显示
                kitchenDisplayManager.fetchWaiterActions();
            } else {
                console.error('❌ 指令处理失败');
            }
        } catch (error) {
            console.error('❌ 处理指令时发生错误:', error);
        }
    }

    // 全屏提示功能已移除，简化用户界面

    // 添加全局函数供调试使用
    window.enterFullscreen = () => FullscreenManager.enter();
    window.exitFullscreen = () => FullscreenManager.exit();
    window.checkFullscreenState = () => {
        console.log('当前全屏状态:', FullscreenManager.isFullscreen());
        console.log('保存的全屏状态:', FullscreenManager.loadState());
    };

</script>
{% endblock %}
