{% extends "base.html" %}

{% block title %}打荷操作台 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 仿照厨房大屏的黑色主题设计 */
    body,
    body.theme-light,
    body.theme-dark,
    html,
    html.theme-light,
    html.theme-dark {
        background: var(--helper-bg-color, #1a1a1a) !important;
        background-color: var(--helper-bg-color, #1a1a1a) !important;
        color: var(--helper-font-color, #FFD700) !important;
    }

    /* 隐藏导航栏、侧边栏和底部继承元素 */
    .navbar,
    .sidebar,
    .offcanvas,
    .toast-container,
    .toast,
    #successToast,
    #errorToast,
    #infoToast {
        display: none !important;
    }

    .main-content,
    .main-content.theme-light,
    .main-content.theme-dark {
        background: var(--helper-bg-color, #1a1a1a) !important;
        background-color: var(--helper-bg-color, #1a1a1a) !important;
        color: var(--helper-font-color, #fff) !important;
        margin-left: 0 !important;
        width: 100% !important;
        padding: 0 !important;
    }

    .kitchen-helper {
        background: var(--helper-bg-color, #1a1a1a) !important;
        background-color: var(--helper-bg-color, #1a1a1a) !important;
        color: var(--helper-font-color, #fff) !important;
        min-height: 100vh;
        padding: 0;
        margin: 0;
        width: 100vw;
        overflow-x: hidden;
        /* 平板触控优化 */
        -webkit-overflow-scrolling: touch;
        touch-action: pan-y pinch-zoom;
        user-select: none;
    }

    /* 仿照厨房大屏的头部布局 */
    .kitchen-helper-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background: linear-gradient(135deg, #2d2d2d, #404040);
        border-bottom: 3px solid #ffc107;
        margin-bottom: 0 !important;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }

    .header-left {
        flex: 1;
        text-align: left;
    }

    .kitchen-helper-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #ffc107;
        margin: 0;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        letter-spacing: 1px;
    }

    .header-center {
        flex: 1;
        text-align: center;
    }

    .current-time-center {
        font-size: 1.3rem;
        font-weight: 600;
        color: #ffffff;
        background: rgba(0,0,0,0.3);
        padding: 6px 12px;
        border-radius: 8px;
        border: 2px solid #ffc107;
        display: inline-block;
        min-width: 180px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .header-right {
        flex: 1;
        text-align: right;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-end;
        gap: 15px;
        flex-wrap: wrap;
    }

    .status-stats {
        display: flex;
        gap: 20px;
        margin-bottom: 5px;
    }

    .status-item {
        display: inline-flex;
        align-items: center;
        gap: 5px;
        font-size: 0.9rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 6px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    }

    .status-item.rooms {
        color: #6f42c1;
        background: rgba(111, 66, 193, 0.1);
        border: 1px solid #6f42c1;
    }

    .status-item.pending {
        color: #28a745;
        background: rgba(40, 167, 69, 0.1);
        border: 1px solid #28a745;
    }

    .status-item.cooking {
        color: #ffc107;
        background: rgba(255, 193, 7, 0.1);
        border: 1px solid #ffc107;
    }

    .status-item.ready {
        color: #dc3545;
        background: rgba(220, 53, 69, 0.1);
        border: 1px solid #dc3545;
    }

    .control-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .header-right .btn {
        font-weight: 600;
        border-width: 2px;
        padding: 8px 16px;
        transition: all 0.3s ease;
        margin: 0;
    }

    .header-right .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }

    /* 包厢容器布局 - 优化为灵活布局 */
    .rooms-container {
        display: flex;
        flex-wrap: wrap;
        gap: var(--room-gap, 15px);
        padding: 20px;
        min-height: calc(100vh - 140px);
        overflow-y: auto;
        align-content: flex-start;
        justify-content: flex-start;
        /* 平板滚动优化 */
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
        overscroll-behavior: contain;
    }

    /* 包厢卡片样式 - 优化为平板触控 */
    .room-column {
        background: #2d2d2d;
        border-radius: 8px;
        border: var(--border-size, 3px) solid #666666;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.4);
        min-height: 300px;
        width: calc(25% - 12px); /* 默认4列布局 */
        max-width: 400px;
        min-width: 280px;
        flex-shrink: 0;
        margin-bottom: 15px;
    }

    .room-column.room-started {
        border-color: #28a745;
        background: #2d3d2d;
    }

    .room-header {
        font-size: 1.4rem; /* 增大字体适配平板 */
        font-weight: bold;
        padding: 12px 16px; /* 增大内边距适配触控 */
        color: #2c3e50;
        text-align: center;
        border-bottom: 3px solid #ffc107;
        background: rgba(255,255,255,0.95);
        min-height: 50px; /* 确保触控区域足够大 */
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .dish-items-container {
        flex: 1;
        padding: 8px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        gap: 0;
    }

    /* 待制作菜品区域 */
    .pending-dishes-section {
        display: grid;
        grid-template-columns: 1fr 1fr; /* 每行2个菜品 */
        gap: 8px;
        margin-bottom: 15px;
    }

    /* 已完成菜品区域 */
    .completed-dishes-section {
        border-top: 2px solid #ffc107;
        padding-top: 15px;
        margin-top: 5px;
    }

    /* 已完成菜品标题 */
    .completed-dishes-header {
        font-size: 0.9rem;
        color: #ffc107;
        font-weight: 600;
        margin-bottom: 8px;
        text-align: center;
        opacity: 0.8;
    }

    /* 已完成菜品网格 */
    .completed-dishes-grid {
        display: grid;
        grid-template-columns: 1fr 1fr; /* 每行2个菜品 */
        gap: 8px;
    }

    /* 菜品项样式 - 固定尺寸优化 */
    .dish-item {
        background: #3d3d3d;
        border-radius: 8px;
        padding: 12px 16px;
        border: 3px solid #28a745;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 1.1rem;
        color: #ffffff;
        /* 固定高度设置 */
        height: 65px; /* 固定高度65px */
        min-height: 65px;
        max-height: 65px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        touch-action: manipulation;
        overflow: hidden; /* 防止内容溢出 */
    }

    .dish-item:hover,
    .dish-item:active {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0,0,0,0.4);
        border-width: 4px; /* 触控时增强视觉反馈 */
    }

    /* 触控设备优化 */
    @media (hover: none) and (pointer: coarse) {
        .dish-item:active {
            transform: scale(0.98);
            transition: transform 0.1s ease;
        }
    }

    /* 不同状态的菜品样式 */
    .dish-item.pending_cook {
        border-color: #28a745;
        background: #2d5a2d;
        color: #ffffff;
    }

    .dish-item.cooking {
        border-color: #ffc107;
        background: #5a5a2d;
        color: #000000;
        animation: pulse 2s infinite;
    }

    .dish-item.ready {
        border-color: #6c757d;
        background: #3a3a3a;
        color: #888888;
        opacity: 0.7;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
    }

    .dish-name {
        font-weight: 700;
        margin-bottom: 4px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 1rem; /* 调整字体大小适配固定高度 */
        line-height: 1.2;
        /* 长文本处理 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
    }

    /* 重复菜品标记 */
    .duplicate-marker {
        background: #ff6b6b;
        color: white;
        font-size: 0.7rem;
        font-weight: bold;
        padding: 2px 6px;
        border-radius: 50%;
        min-width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-left: 5px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        animation: duplicatePulse 2s infinite;
    }

    @keyframes duplicatePulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .dish-status {
        font-size: 0.8rem;
        opacity: 0.8;
        line-height: 1.1;
        /* 确保状态文本不会溢出 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* 已完成菜品区域 */
    .completed-dishes-section {
        margin-top: 8px;
        border-top: 1px solid #444;
        padding-top: 8px;
    }

    .completed-dishes-header {
        font-size: 0.8rem;
        color: #888;
        text-align: center;
        margin-bottom: 4px;
        font-weight: bold;
    }

    .completed-dishes-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4px;
    }

    /* 服务员指令悬浮窗 - 优化尺寸设计 */
    .waiter-actions-float {
        position: fixed;
        bottom: var(--float-bottom, 20px);
        right: var(--float-right, 20px);
        width: 280px; /* 修复：增加三分之一宽度 (210px * 1.33 ≈ 280px) */
        height: 67vh; /* 设置为页面可视高度的2/3 */
        max-height: 67vh;
        min-height: 200px;
        background: rgba(0, 0, 0, 0.92);
        border: 3px solid #ffc107;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.6);
        z-index: 1000;
        overflow: hidden;
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column-reverse;
        animation: slideUpFromBottom 0.3s ease-out;
        touch-action: manipulation;
        /* 禁用调整大小 */
        resize: none;
    }

    .float-header {
        background: linear-gradient(135deg, #2d2d2d, #404040);
        padding: 12px 18px; /* 增大内边距适配触控 */
        border-top: 2px solid #ffc107;
        border-bottom: none;
        cursor: move;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 0 0 10px 10px;
        min-height: 50px; /* 确保触控区域足够大 */
        touch-action: manipulation;
    }

    .float-title {
        color: #ffc107;
        font-weight: bold;
        font-size: 0.9rem;
    }

    .float-controls {
        display: flex;
        gap: 5px;
    }

    .float-btn {
        background: none;
        border: 2px solid #ffc107; /* 增大边框 */
        color: #ffc107;
        padding: 6px 12px; /* 增大内边距适配触控 */
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9rem; /* 增大字体 */
        min-width: 36px; /* 确保触控区域足够大 */
        min-height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        touch-action: manipulation;
        transition: all 0.2s ease;
    }

    .float-btn:hover {
        background: #ffc107;
        color: #000;
    }

    .float-content {
        flex: 1;
        overflow-y: auto;
        padding: 8px;
        border-radius: 8px 8px 0 0;
        /* 适配新的固定高度 */
        height: calc(100% - 60px); /* 减去标题栏高度 */
    }

    .action-item {
        background: rgba(255, 255, 255, 0.1);
        padding: 6px;
        margin-bottom: 4px;
        border-radius: 4px;
        border-left: 2px solid #ffc107;
        position: relative;
        /* 适配窄宽度 */
        font-size: 0.8rem;
    }

    .action-room {
        color: #ffc107;
        font-weight: bold;
        font-size: 0.9rem;
        margin-bottom: 2px;
        /* 文本换行处理 */
        word-wrap: break-word;
        line-height: 1.2;
    }

    .action-content {
        color: #fff;
        font-size: 0.8rem;
        font-weight: 500;
        margin: 2px 0;
        line-height: 1.3;
        /* 文本换行处理 */
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    .action-time {
        color: #adb5bd;
        font-size: 0.7rem;
        line-height: 1.1;
    }

    /* 确认按钮样式 */
    .action-confirm-btn {
        position: absolute;
        top: 2px;
        right: 2px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 3px;
        padding: 2px 4px;
        font-size: 0.6rem;
        cursor: pointer;
        transition: background 0.2s;
    }

    .action-confirm-btn:hover {
        background: #218838;
    }

    .action-controls {
        margin-top: 5px;
        text-align: right;
    }

    .action-controls .btn {
        font-size: 0.7rem;
        padding: 2px 8px;
        border-radius: 3px;
    }

    /* 未开始包厢悬浮窗 */
    .pending-rooms-float {
        position: fixed;
        bottom: 20px;
        left: 20px;
        width: 300px;
        max-height: 400px;
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid #17a2b8;
        border-radius: 8px;
        z-index: 999;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        cursor: move;
        user-select: none;
        animation: slideUpFromBottom 0.3s ease-out;
        display: flex;
        flex-direction: column-reverse; /* 标题栏在底部，内容在上方 */
    }

    @keyframes slideUpFromBottom {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .pending-rooms-container {
        max-height: 300px;
        overflow-y: auto;
        padding: 10px;
        border-radius: 8px 8px 0 0; /* 只有顶部圆角 */
        flex: 1;
    }

    .pending-room-item {
        background: rgba(23, 162, 184, 0.2);
        border: 1px solid #17a2b8;
        border-radius: 5px;
        padding: 8px;
        margin-bottom: 6px;
        border-left: 3px solid #17a2b8;
    }

    .pending-room-name {
        color: #17a2b8;
        font-weight: bold;
        font-size: 1.0rem;
        margin-bottom: 2px;
    }

    .pending-waiter-name {
        color: #fff;
        font-size: 0.85rem;
        margin-bottom: 2px;
    }

    .pending-room-info {
        color: #adb5bd;
        font-size: 0.75rem;
    }

    /* 浅色主题下的未开始包厢悬浮窗 */
    .theme-light .pending-rooms-float {
        background: rgba(255, 255, 255, 0.95);
        border-color: #17a2b8;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    }

    .theme-light .pending-room-item {
        background: rgba(23, 162, 184, 0.1);
        border-color: #17a2b8;
    }

    .theme-light .pending-waiter-name {
        color: #333333;
    }

    .theme-light .pending-room-info {
        color: #6c757d;
    }

    /* 响应式设计 - 优化为灵活布局 */

    /* 超大屏幕 (>1800px) - 5列 */
    @media (min-width: 1801px) {
        .room-column {
            width: calc(20% - 12px);
        }
    }

    /* 大屏幕 (1400px-1800px) - 4列 */
    @media (min-width: 1401px) and (max-width: 1800px) {
        .room-column {
            width: calc(25% - 12px);
        }
    }

    /* 中等屏幕 (1000px-1400px) - 3列 */
    @media (min-width: 1001px) and (max-width: 1400px) {
        .room-column {
            width: calc(33.333% - 10px);
        }

        .rooms-container {
            gap: 12px;
        }
    }

    /* 平板横屏 (768px-1000px) - 2列 */
    @media (min-width: 769px) and (max-width: 1000px) {
        .room-column {
            width: calc(50% - 8px);
            min-width: 320px;
        }

        .rooms-container {
            gap: 10px;
            padding: 15px;
        }

        .waiter-actions-float {
            width: 190px; /* 平板横屏适当宽度 */
            height: 60vh;
        }

        /* 平板优化：固定高度触控区域 */
        .dish-item {
            height: 70px; /* 平板上固定高度70px */
            min-height: 70px;
            max-height: 70px;
            font-size: 1.1rem; /* 调整字体适配固定高度 */
            padding: 12px 18px;
        }

        .room-header {
            font-size: 1.5rem;
            min-height: 55px;
            padding: 15px 20px;
        }
    }

    /* 平板竖屏 (600px-768px) - 1列 */
    @media (min-width: 601px) and (max-width: 768px) {
        .room-column {
            width: 100%;
            max-width: none;
            min-width: 300px;
        }

        .rooms-container {
            gap: 15px;
            padding: 15px;
        }

        .waiter-actions-float {
            width: 180px; /* 平板竖屏适当宽度 */
            height: 50vh;
            right: 15px;
        }

        /* 平板竖屏优化 */
        .pending-dishes-section,
        .completed-dishes-grid {
            grid-template-columns: 1fr 1fr; /* 保持2列菜品 */
            gap: 10px;
        }

        .dish-item {
            height: 75px; /* 平板竖屏固定高度75px */
            min-height: 75px;
            max-height: 75px;
            font-size: 1.1rem; /* 调整字体适配固定高度 */
            padding: 14px 20px;
        }

        .room-header {
            font-size: 1.6rem;
            min-height: 60px;
            padding: 18px 24px;
        }
    }

    /* 手机屏幕 (<600px) - 1列 */
    @media (max-width: 600px) {
        .room-column {
            width: 100%;
            max-width: none;
            min-width: 280px;
        }

        .rooms-container {
            gap: 12px;
            padding: 10px;
        }

        .waiter-actions-float {
            width: 160px; /* 手机端适当宽度 */
            height: 40vh;
            right: 10px;
        }

        /* 手机优化：单列菜品显示 */
        .pending-dishes-section,
        .completed-dishes-grid {
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .dish-item {
            height: 65px; /* 手机固定高度65px */
            min-height: 65px;
            max-height: 65px;
            font-size: 1rem; /* 调整字体适配固定高度 */
            padding: 12px 16px;
        }

        .room-header {
            font-size: 1.4rem;
            min-height: 50px;
            padding: 12px 16px;
        }

        /* 手机端悬浮窗优化 */
        .pending-rooms-float {
            width: 250px;
            left: 10px;
        }
    }

    /* 平板专用优化 */
    @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
        /* 平板横屏模式特殊优化 */
        .rooms-container {
            padding: 20px 30px;
        }

        .room-column {
            width: calc(33.333% - 10px);
            min-width: 300px;
        }

        .dish-item {
            min-height: 75px;
            font-size: 1.25rem;
            padding: 16px 20px;
        }

        .room-header {
            font-size: 1.6rem;
            min-height: 60px;
        }
    }

    @media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
        /* 平板竖屏模式特殊优化 */
        .rooms-container {
            padding: 15px 20px;
        }

        .room-column {
            width: calc(50% - 8px);
            min-width: 320px;
        }

        .dish-item {
            min-height: 80px;
            font-size: 1.3rem;
            padding: 18px 22px;
        }

        .room-header {
            font-size: 1.7rem;
            min-height: 65px;
        }
    }

    /* 触控设备通用优化 */
    @media (hover: none) and (pointer: coarse) {
        .dish-item {
            /* 增强触控反馈 */
            transition: all 0.15s ease;
        }

        .dish-item:active {
            transform: scale(0.97);
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        }

        .float-btn:active {
            transform: scale(0.95);
            background: #ffc107;
            color: #000;
        }

        /* 防止长按选择文本 */
        .dish-item,
        .room-header,
        .float-btn {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
        }
    }

    /* 浅色主题 */
    .theme-light {
        --helper-bg-color: #f8f9fa;
        --helper-font-color: #333333;
    }

    .theme-light .room-column {
        background: #ffffff;
        border-color: #dee2e6;
        color: #333333;
    }

    .theme-light .dish-item {
        background: #f8f9fa;
        color: #333333;
    }

    .theme-light .waiter-actions-float {
        background: rgba(255, 255, 255, 0.95);
        color: #333333;
        border-color: #007bff;
        box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    }

    .theme-light .float-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-bottom-color: #007bff;
    }

    .theme-light .float-title {
        color: #007bff;
    }

    .theme-light .float-btn {
        border-color: #007bff;
        color: #007bff;
    }

    .theme-light .float-btn:hover {
        background: #007bff;
        color: #fff;
    }

    .theme-light .action-item {
        background: rgba(0, 123, 255, 0.1);
        border-left-color: #007bff;
    }

    .theme-light .action-room {
        color: #007bff;
    }

    .theme-light .action-content {
        color: #333333;
        font-weight: bold;  /* 确保浅色主题下也加粗 */
    }

    .theme-light .action-time {
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="kitchen-helper">
    <!-- 仿照厨房大屏的头部布局 -->
    <div class="kitchen-helper-header">
        <!-- 左侧：页面标题 -->
        <div class="header-left">
            <h1 class="kitchen-helper-title">暨阳湖大酒店打荷操作台</h1>
        </div>

        <!-- 中央：实时时间 -->
        <div class="header-center">
            <div class="current-time-center" id="currentTime"></div>
        </div>

        <!-- 右侧：状态统计和控制按钮 -->
        <div class="header-right">
            <!-- 状态统计 -->
            <div class="status-stats">
                <span class="status-item rooms">
                    <i class="bi bi-house-door-fill"></i>
                    包厢<span id="roomsCount">0</span>个
                </span>
                <span class="status-item pending">
                    <i class="bi bi-clock-fill"></i>
                    待制作<span id="pendingCount">0</span>个
                </span>
                <span class="status-item cooking">
                    <i class="bi bi-arrow-clockwise"></i>
                    制作中<span id="cookingCount">0</span>个
                </span>
                <span class="status-item ready">
                    <i class="bi bi-check-circle-fill"></i>
                    已完成<span id="readyCount">0</span>个
                </span>
            </div>

            <!-- 控制按钮组 -->
            <div class="control-buttons">
                <button class="btn btn-outline-light btn-sm me-2" onclick="window.history.back()" title="返回">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
                <button class="btn btn-outline-warning btn-sm me-2" onclick="location.reload()" title="刷新">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
                <a href="/kitchen/display" class="btn btn-outline-info btn-sm" target="_blank" title="厨房大屏">
                    <i class="bi bi-display"></i> 厨房大屏
                </a>
                <a href="/dashboard" class="btn btn-outline-secondary btn-sm" title="返回主页">
                    <i class="bi bi-house"></i> 主页
                </a>
            </div>
        </div>
    </div>

    <!-- 包厢容器 -->
    <div class="rooms-container" id="roomsContainer">
        <!-- 包厢将通过JavaScript动态加载 -->
    </div>

    <!-- 服务员指令悬浮窗 -->
    <div class="waiter-actions-float" id="waiterActionsFloat">
        <div class="float-header" id="floatHeader">
            <span class="float-title">
                <i class="bi bi-megaphone-fill"></i>
                服务员指令
            </span>
            <div class="float-controls">
                <button class="float-btn" onclick="toggleFloatWindow()" title="最小化/展开">
                    <i class="bi bi-dash" id="toggleIcon"></i>
                </button>
            </div>
        </div>
        <div class="float-content" id="floatContent">
            <div id="waiterActionsContainer">
                <!-- 服务员指令将通过JavaScript动态加载 -->
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    暂无服务员指令
                </div>
            </div>
        </div>
    </div>

    <!-- 左下角未开始包厢悬浮窗 -->
    <div id="pendingRoomsFloat" class="pending-rooms-float">
        <div id="pendingFloatHeader" class="float-header">
            <div class="float-title">
                <i class="bi bi-clock-history"></i> 未开始包厢
            </div>
            <div class="float-controls">
                <button class="float-btn" onclick="togglePendingFloat()" title="最小化/展开">
                    <i id="pendingToggleIcon" class="bi bi-dash"></i>
                </button>
            </div>
        </div>
        <div id="pendingFloatContent" class="float-content">
            <div id="pendingRoomsContainer" class="pending-rooms-container">
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    暂无未开始包厢
                </div>
            </div>
        </div>
    </div>

    <!-- Toast 容器 -->
    <div id="toastContainer" style="position: fixed; top: 20px; right: 20px; z-index: 9999;"></div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // 全局变量
    let kitchenHelperConfig = {};
    let roomsData = [];
    let waiterActionsData = [];
    let pendingRoomsData = [];
    let isFloatMinimized = false;
    let isPendingFloatMinimized = false;
    let isDragging = false;
    let isPendingDragging = false;
    let dragOffset = { x: 0, y: 0 };
    let pendingDragOffset = { x: 0, y: 0 };

    // 🔧 新增：语音播报相关全局变量
    let voiceConfig = null; // 语音配置缓存
    let voiceQueue = []; // 语音播报队列
    let isVoicePlaying = false; // 语音播报状态
    let isMobileDevice = false; // 移动设备标识
    let voiceInitialized = false; // 语音API初始化状态

    // 🔧 新增：移动端语音API兼容性检查和初始化
    function initializeMobileVoiceSupport() {
        // 检测移动设备
        isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const hasVoiceSupport = 'speechSynthesis' in window;

        console.log(`📱 打荷端设备检测: 移动端=${isMobileDevice}, 语音支持=${hasVoiceSupport}`);

        if (!hasVoiceSupport) {
            console.warn('❌ 打荷端：当前浏览器不支持语音合成API');
            return false;
        }

        if (isMobileDevice) {
            console.log(`📱 打荷端：检测到移动设备，启用移动端语音优化`);

            // 移动端需要用户交互才能启用语音
            const initVoiceOnInteraction = function() {
                if (!voiceInitialized && 'speechSynthesis' in window) {
                    try {
                        // 播放一个静音的语音来激活API
                        const utterance = new SpeechSynthesisUtterance('');
                        utterance.volume = 0;
                        utterance.onstart = function() {
                            voiceInitialized = true;
                            console.log(`📱 打荷端移动端语音API已激活`);
                        };
                        utterance.onerror = function(event) {
                            console.warn(`⚠️ 打荷端移动端语音API激活失败:`, event.error);
                        };
                        speechSynthesis.speak(utterance);
                    } catch (error) {
                        console.error('❌ 打荷端移动端语音API激活异常:', error);
                    }
                }

                // 移除事件监听器，只需要激活一次
                document.removeEventListener('touchstart', initVoiceOnInteraction);
                document.removeEventListener('click', initVoiceOnInteraction);
            };

            // 监听用户交互事件
            document.addEventListener('touchstart', initVoiceOnInteraction, { once: true });
            document.addEventListener('click', initVoiceOnInteraction, { once: true });

            // 移动端特殊处理：确保语音合成器状态正常
            setInterval(() => {
                if (speechSynthesis.paused) {
                    speechSynthesis.resume();
                }
            }, 1000);
        } else {
            // 桌面端直接标记为已初始化
            voiceInitialized = true;
        }

        return true;
    }

    // 🔧 新增：获取语音配置
    async function getVoiceConfig() {
        if (voiceConfig) return voiceConfig;

        try {
            const response = await fetch('/api/voice-config', {
                credentials: 'include'  // 🔧 添加认证信息
            });
            if (response.ok) {
                voiceConfig = await response.json();
                console.log(`🔧 打荷端获取语音配置成功:`, voiceConfig);
                return voiceConfig;
            }
        } catch (error) {
            console.error('获取语音配置失败:', error);
        }

        // 返回默认配置
        const defaultConfig = {
            voice_enabled: true,
            voice_repeat_count: 2,
            voice_repeat_interval: 3,
            voice_rate: 0.8,
            voice_volume: 1.0,
            voice_pitch: 1.0
        };
        voiceConfig = defaultConfig;
        console.log(`🔧 打荷端使用默认语音配置:`, defaultConfig);
        return defaultConfig;
    }

    // 🔧 新增：监听语音配置更新事件
    function handleVoiceConfigUpdate() {
        console.log(`🔄 打荷端接收到语音配置更新通知，清除缓存`);
        voiceConfig = null; // 清除缓存，下次调用时重新获取
    }

    // 🔧 新增：语音播报队列管理（移动端优化版）
    async function addToVoiceQueue(message, type = 'instruction') {
        if (!message) {
            console.log('🔇 打荷端语音播报跳过：无文本内容');
            return;
        }

        if (!('speechSynthesis' in window)) {
            console.warn('❌ 打荷端：浏览器不支持语音合成API');
            return;
        }

        const config = await getVoiceConfig();
        if (!config.voice_enabled) {
            console.log(`🔇 语音播报已禁用，跳过: ${message}`);
            return;
        }

        // 🔧 修复：统一的播报状态管理和去重机制
        const timestamp = Date.now();
        const voiceId = `helper_${type}_${message}_${timestamp}`;

        // 检查重复播报
        const isDuplicate = voiceQueue.some(item => item.text === message);
        if (isDuplicate) {
            console.log(`🔄 打荷端跳过队列中重复播报: ${message}`);
            return;
        }

        // 检查最近3秒内的重复播报
        const recentTimeWindow = 3000; // 3秒
        const now = Date.now();
        const recentDuplicate = voiceQueue.some(item =>
            item.text === message &&
            (now - item.timestamp) < recentTimeWindow
        );

        if (recentDuplicate) {
            console.log(`🔄 打荷端跳过最近重复播报: ${message} (3秒内重复)`);
            return;
        }

        // 🔧 移动端特殊处理：检查语音API状态
        if (isMobileDevice) {
            if (!voiceInitialized) {
                console.log('📱 打荷端移动端语音API未初始化，将在用户交互后播报');
                // 仍然添加到队列，等待初始化后播报
            }

            // 移动端限制队列长度，避免积压过多
            if (voiceQueue.length > 5) {
                console.log('📱 打荷端移动端语音队列已满，移除最旧的播报');
                voiceQueue.shift();
            }
        }

        console.log(`🎵 打荷端准备添加语音播报: ${message} (类型: ${type}, 移动端: ${isMobileDevice}, 重复: ${config.voice_repeat_count}次)`);

        const voiceItem = {
            text: message,
            type: type,
            config: config,
            voiceId: voiceId,
            timestamp: timestamp,
            isMobile: isMobileDevice
        };

        voiceQueue.push(voiceItem);
        console.log(`🔊 打荷端添加语音播报到队列: ${message} (队列长度: ${voiceQueue.length})`);

        // 如果当前没有播报，立即开始播报
        if (!isVoicePlaying) {
            processVoiceQueue();
        }
    }

    // 🔧 新增：处理语音播报队列（移动端优化版）
    async function processVoiceQueue() {
        if (isVoicePlaying || voiceQueue.length === 0) {
            return;
        }

        // 🔧 移动端兼容性检查
        if (!('speechSynthesis' in window)) {
            console.warn('❌ 打荷端：浏览器不支持语音合成API，跳过播报');
            voiceQueue = []; // 清空队列
            return;
        }

        // 🔧 移动端语音API初始化检查
        if (isMobileDevice && !voiceInitialized) {
            console.warn('⚠️ 打荷端：移动端语音API未初始化，等待用户交互');
            // 延迟重试
            setTimeout(() => processVoiceQueue(), 1000);
            return;
        }

        isVoicePlaying = true;
        const voiceItem = voiceQueue.shift();

        console.log(`🔊 打荷端开始播报: ${voiceItem.text} (移动端: ${isMobileDevice}, 重复: ${voiceItem.config.voice_repeat_count}次)`);

        try {
            // 🔧 修复：确保所有设备使用相同的播报次数
            const maxRepeats = voiceItem.config.voice_repeat_count || 2;
            const interval = (voiceItem.config.voice_repeat_interval || 3) * 1000;

            // 🔧 修复：统一的语音合成器状态处理（移动端和桌面端都执行）
            if (speechSynthesis.paused) {
                speechSynthesis.resume();
            }
            // 清除可能存在的语音队列（移动端和桌面端都执行）
            speechSynthesis.cancel();

            // 等待一小段时间确保cancel完成
            await new Promise(resolve => setTimeout(resolve, 100));

            // 播报指定次数
            for (let i = 0; i < maxRepeats; i++) {
                await new Promise((resolve) => {
                    const utterance = new SpeechSynthesisUtterance(voiceItem.text);
                    utterance.lang = 'zh-CN';
                    utterance.rate = voiceItem.config.voice_rate || 0.8;
                    utterance.volume = voiceItem.config.voice_volume || 1.0;
                    utterance.pitch = voiceItem.config.voice_pitch || 1.0;

                    // 🔧 移动端优化：设置更长的超时时间
                    let timeoutId = null;
                    const timeoutDuration = isMobileDevice ? 10000 : 5000; // 移动端10秒，桌面端5秒

                    utterance.onstart = function() {
                        console.log(`🔊 打荷端播报开始 (${i + 1}/${maxRepeats}): ${voiceItem.text}`);
                        if (timeoutId) {
                            clearTimeout(timeoutId);
                            timeoutId = null;
                        }
                    };

                    utterance.onend = function() {
                        console.log(`✅ 打荷端播报完成 (${i + 1}/${maxRepeats}): ${voiceItem.text}`);
                        if (timeoutId) {
                            clearTimeout(timeoutId);
                            timeoutId = null;
                        }
                        resolve();
                    };

                    utterance.onerror = function(event) {
                        console.error(`❌ 打荷端播报错误:`, event.error);
                        if (timeoutId) {
                            clearTimeout(timeoutId);
                            timeoutId = null;
                        }
                        resolve();
                    };

                    // 🔧 移动端兼容性：设置超时保护
                    timeoutId = setTimeout(() => {
                        console.warn(`⚠️ 打荷端播报超时 (${i + 1}/${maxRepeats}): ${voiceItem.text}`);
                        speechSynthesis.cancel();
                        resolve();
                    }, timeoutDuration);

                    speechSynthesis.speak(utterance);
                });

                // 播报间隔（最后一次播报后不需要等待）
                if (i < maxRepeats - 1) {
                    console.log(`⏳ 打荷端播报间隔等待: ${interval}ms`);
                    await new Promise(resolve => setTimeout(resolve, interval));
                }
            }

            console.log(`🎵 打荷端播报完成: ${voiceItem.text} (共${maxRepeats}次, 移动端: ${isMobileDevice})`);

        } catch (error) {
            console.error('❌ 打荷端播报异常:', error);
        } finally {
            isVoicePlaying = false;

            // 继续处理队列中的下一个播报
            if (voiceQueue.length > 0) {
                setTimeout(() => processVoiceQueue(), 500);
            }
        }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadKitchenHelperConfig();
        initializeFloatWindow();
        initializePendingFloatWindow();
        restoreFloatHeight(); // 恢复悬浮窗高度设置
        initializeWaiterActionsPersistence(); // 初始化服务员指令持久化机制
        initializeMobileVoiceSupport(); // 🔧 新增：初始化移动端语音支持
        loadRoomsData();
        loadWaiterActions();
        loadPendingRooms();
        updateCurrentTime();
        initializeTouchOptimizations(); // 初始化触控优化

        // 定时更新
        setInterval(updateCurrentTime, 1000);
        setInterval(loadRoomsData, 5000);
        setInterval(loadWaiterActions, 3000);
        setInterval(loadPendingRooms, 5000);
        setInterval(checkForNotifications, 2000); // 🔧 新增：定期检查通知
        setInterval(checkForWaiterInstructions, 2000); // 🔧 新增：定期检查服务员指令（移动端语音播报）

        // 🔧 新增：页面加载时检查一次通知
        setTimeout(checkForNotifications, 1000);
    });

    // 🔧 新增：检查各种通知（用餐开始、结束、菜品完成等）
    function checkForNotifications() {
        // 检查用餐开始事件
        checkForDiningStartEvents();

        // 检查用餐结束事件
        checkForDiningEndEvents();

        // 检查菜品制作完成通知
        checkForDishReadyNotifications();
    }

    // 🔧 新增：检查用餐开始事件
    function checkForDiningStartEvents() {
        fetch('/api/dining-start/latest')
            .then(response => response.json())
            .then(data => {
                if (data.dining_started_rooms && data.dining_started_rooms.length > 0) {
                    data.dining_started_rooms.forEach(roomInfo => {
                        const message = {
                            type: 'dining_started',
                            room_number: roomInfo.room_number,
                            guest_count: roomInfo.guest_count,
                            waiter_name: roomInfo.waiter_name
                        };
                        handleWebSocketMessage(message);
                    });
                }
            })
            .catch(error => {
                // 减少网络错误的日志噪音
                if (error.name !== 'AbortError' && !error.message.includes('fetch')) {
                    console.log('检查用餐开始事件失败:', error);
                }
            });
    }

    // 🔧 新增：检查用餐结束事件
    function checkForDiningEndEvents() {
        fetch('/api/dining-end/latest')
            .then(response => response.json())
            .then(data => {
                if (data.dining_ended_rooms && data.dining_ended_rooms.length > 0) {
                    data.dining_ended_rooms.forEach(endInfo => {
                        const message = {
                            type: 'dining_ended',
                            room_number: endInfo.room_number,
                            waiter_name: endInfo.waiter_name
                        };
                        handleWebSocketMessage(message);
                    });
                }
            })
            .catch(error => {
                // 减少网络错误的日志噪音
                if (error.name !== 'AbortError' && !error.message.includes('fetch')) {
                    console.log('检查用餐结束事件失败:', error);
                }
            });
    }

    // 🔧 新增：检查菜品制作完成通知
    function checkForDishReadyNotifications() {
        fetch('/api/dish-ready/latest')
            .then(response => response.json())
            .then(data => {
                if (data.ready_dishes && data.ready_dishes.length > 0) {
                    data.ready_dishes.forEach(dish => {
                        const message = {
                            type: 'dish_ready',
                            room_number: dish.room_number,
                            dish_name: dish.dish_name
                        };
                        handleWebSocketMessage(message);
                    });
                }
            })
            .catch(error => {
                // 减少网络错误的日志噪音
                if (error.name !== 'AbortError' && !error.message.includes('fetch')) {
                    console.log('检查菜品完成通知失败:', error);
                }
            });
    }

    // 🔧 新增：检查服务员指令更新（移动端语音播报专用）
    function checkForWaiterInstructions() {
        fetch('/api/waiter-actions/latest', {
            credentials: 'include'
        })
            .then(response => response.json())
            .then(data => {
                if (data.actions && data.actions.length > 0) {
                    // 获取最近5分钟内的新指令
                    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
                    const recentActions = data.actions.filter(action => {
                        const actionTime = new Date(action.created_at);
                        return actionTime > fiveMinutesAgo && !action.is_processed;
                    });

                    recentActions.forEach(action => {
                        // 构建WebSocket消息格式
                        const message = {
                            type: 'waiter_instruction',
                            room_number: action.room_number,
                            instruction: action.action_type_display || action.action_type,
                            waiter_name: action.waiter_name,
                            timestamp: Date.now()
                        };

                        // 处理指令（触发语音播报）
                        console.log(`🎵 打荷端检测到新服务员指令: ${action.room_number}包厢${message.instruction}`);
                        handleWebSocketMessage(message);
                    });
                }
            })
            .catch(error => {
                // 减少网络错误的日志噪音
                if (error.name !== 'AbortError' && !error.message.includes('fetch')) {
                    console.log('检查服务员指令失败:', error);
                }
            });
    }

    // 初始化触控优化（包含移动端语音播报优化）
    function initializeTouchOptimizations() {
        // 防止双击缩放
        document.addEventListener('touchstart', function(e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });

        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(e) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        }, { passive: false });

        // 🔧 移动端语音播报优化：处理页面可见性变化
        if (isMobileDevice) {
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    console.log('📱 打荷端页面进入后台，保持语音播报活跃');
                    // 移动端页面进入后台时，确保语音播报继续工作
                    if ('speechSynthesis' in window && speechSynthesis.paused) {
                        speechSynthesis.resume();
                    }
                } else {
                    console.log('📱 打荷端页面回到前台，检查语音播报状态');
                    // 页面回到前台时，检查语音播报状态
                    if ('speechSynthesis' in window) {
                        if (speechSynthesis.paused) {
                            speechSynthesis.resume();
                        }
                        // 如果有待播报的内容，继续处理
                        if (voiceQueue.length > 0 && !isVoicePlaying) {
                            setTimeout(() => processVoiceQueue(), 500);
                        }
                    }
                }
            });

            // 🔧 移动端特殊处理：定期检查语音合成器状态
            setInterval(() => {
                if ('speechSynthesis' in window) {
                    if (speechSynthesis.paused) {
                        speechSynthesis.resume();
                    }
                    // 检查是否有语音播报卡住的情况
                    if (isVoicePlaying && voiceQueue.length === 0) {
                        console.log('📱 检测到语音播报状态异常，重置状态');
                        isVoicePlaying = false;
                    }
                }
            }, 2000);
        }

        // 优化滚动性能
        const roomsContainer = document.getElementById('roomsContainer');
        if (roomsContainer) {
            roomsContainer.addEventListener('scroll', function() {
                // 节流滚动事件
                clearTimeout(this.scrollTimeout);
                this.scrollTimeout = setTimeout(() => {
                    // 滚动结束后的处理
                }, 150);
            }, { passive: true });
        }
    }

    // 加载未开始包厢数据
    async function loadPendingRooms() {
        try {
            const response = await fetch('/api/kitchen/pending-rooms');
            if (response.ok) {
                const data = await response.json();
                pendingRoomsData = data.rooms || [];
                renderPendingRooms();
            }
        } catch (error) {
            console.error('加载未开始包厢数据失败:', error);
        }
    }

    // 渲染未开始包厢
    function renderPendingRooms() {
        const container = document.getElementById('pendingRoomsContainer');
        if (!container) return;

        if (pendingRoomsData.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    暂无未开始包厢
                </div>
            `;
            return;
        }

        container.innerHTML = pendingRoomsData.map(room => `
            <div class="pending-room-item">
                <div class="pending-room-name">${room.room_name}</div>
                <div class="pending-waiter-name">服务员：${room.waiter_name}</div>
                <div class="pending-room-info">${room.guest_count}人 | ${room.ordered_at}</div>
            </div>
        `).join('');
    }

    // 初始化未开始包厢悬浮窗
    function initializePendingFloatWindow() {
        const floatWindow = document.getElementById('pendingRoomsFloat');
        const floatHeader = document.getElementById('pendingFloatHeader');

        if (!floatWindow || !floatHeader) return;

        // 从localStorage恢复位置
        const savedPosition = localStorage.getItem('pendingFloatPosition');
        if (savedPosition) {
            const position = JSON.parse(savedPosition);
            floatWindow.style.bottom = position.bottom + 'px';
            floatWindow.style.left = position.left + 'px';
        }

        // 默认设置为最小化状态
        const savedMinimized = localStorage.getItem('pendingFloatMinimized');
        if (savedMinimized === null) {
            // 首次加载时默认最小化
            isPendingFloatMinimized = true;
            localStorage.setItem('pendingFloatMinimized', 'true');
        } else {
            isPendingFloatMinimized = savedMinimized === 'true';
        }

        // 应用最小化状态
        if (isPendingFloatMinimized) {
            floatWindow.classList.add('minimized');
        }

        // 拖拽功能
        floatHeader.addEventListener('mousedown', startPendingDrag);
        document.addEventListener('mousemove', pendingDrag);
        document.addEventListener('mouseup', stopPendingDrag);

        // 触摸设备支持
        floatHeader.addEventListener('touchstart', startPendingDrag);
        document.addEventListener('touchmove', pendingDrag);
        document.addEventListener('touchend', stopPendingDrag);
    }

    // 开始拖拽未开始包厢悬浮窗
    function startPendingDrag(e) {
        isPendingDragging = true;
        const floatWindow = document.getElementById('pendingRoomsFloat');
        const rect = floatWindow.getBoundingClientRect();

        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        pendingDragOffset.x = clientX - rect.left;
        pendingDragOffset.y = clientY - rect.top;

        floatWindow.style.transition = 'none';
        e.preventDefault();
    }

    // 拖拽中
    function pendingDrag(e) {
        if (!isPendingDragging) return;

        const floatWindow = document.getElementById('pendingRoomsFloat');
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        const newLeft = clientX - pendingDragOffset.x;
        const newTop = clientY - pendingDragOffset.y;

        // 边界检查
        const maxLeft = window.innerWidth - floatWindow.offsetWidth;
        const maxTop = window.innerHeight - floatWindow.offsetHeight;

        const constrainedLeft = Math.max(0, Math.min(newLeft, maxLeft));
        const constrainedTop = Math.max(0, Math.min(newTop, maxTop));

        floatWindow.style.left = constrainedLeft + 'px';
        floatWindow.style.top = constrainedTop + 'px';
        floatWindow.style.bottom = 'auto';

        e.preventDefault();
    }

    // 停止拖拽
    function stopPendingDrag() {
        if (!isPendingDragging) return;

        isPendingDragging = false;
        const floatWindow = document.getElementById('pendingRoomsFloat');
        floatWindow.style.transition = 'all 0.3s ease';

        // 保存位置
        const rect = floatWindow.getBoundingClientRect();
        const position = {
            bottom: window.innerHeight - rect.bottom,
            left: rect.left
        };
        localStorage.setItem('pendingFloatPosition', JSON.stringify(position));
    }

    // 切换未开始包厢悬浮窗显示状态
    function togglePendingFloat() {
        const floatContent = document.getElementById('pendingFloatContent');
        const toggleIcon = document.getElementById('pendingToggleIcon');

        isPendingFloatMinimized = !isPendingFloatMinimized;

        if (isPendingFloatMinimized) {
            floatContent.style.display = 'none';
            toggleIcon.className = 'bi bi-plus';
        } else {
            floatContent.style.display = 'block';
            toggleIcon.className = 'bi bi-dash';
        }
    }

    // 重置未开始包厢悬浮窗位置
    function resetPendingFloatPosition() {
        const floatWindow = document.getElementById('pendingRoomsFloat');
        floatWindow.style.bottom = '20px';
        floatWindow.style.left = '20px';
        floatWindow.style.top = 'auto';
        floatWindow.style.maxHeight = '400px'; // 重置高度

        // 清除保存的位置和高度
        localStorage.removeItem('pendingFloatPosition');
        localStorage.removeItem('pendingFloatHeight');
    }

    // 调整悬浮窗高度
    function adjustFloatHeight(type, action) {
        const floatWindow = type === 'waiter' ?
            document.getElementById('waiterActionsFloat') :
            document.getElementById('pendingRoomsFloat');

        if (!floatWindow) return;

        // 获取当前高度
        const currentHeight = parseInt(floatWindow.style.maxHeight) ||
            (type === 'waiter' ? 400 : 400);

        // 计算新高度
        let newHeight = currentHeight;
        if (action === 'increase') {
            newHeight = Math.min(currentHeight + 50, 800); // 最大800px
        } else if (action === 'decrease') {
            newHeight = Math.max(currentHeight - 50, 200); // 最小200px
        }

        // 应用新高度
        floatWindow.style.maxHeight = newHeight + 'px';

        // 保存到localStorage
        const storageKey = type === 'waiter' ? 'waiterFloatHeight' : 'pendingFloatHeight';
        localStorage.setItem(storageKey, newHeight.toString());

        console.log(`${type}悬浮窗高度调整为: ${newHeight}px`);
    }

    // 恢复悬浮窗高度设置
    function restoreFloatHeight() {
        // 恢复服务员指令悬浮窗高度
        const waiterHeight = localStorage.getItem('waiterFloatHeight');
        if (waiterHeight) {
            const waiterFloat = document.getElementById('waiterActionsFloat');
            if (waiterFloat) {
                waiterFloat.style.maxHeight = waiterHeight + 'px';
            }
        }

        // 恢复未开始包厢悬浮窗高度
        const pendingHeight = localStorage.getItem('pendingFloatHeight');
        if (pendingHeight) {
            const pendingFloat = document.getElementById('pendingRoomsFloat');
            if (pendingFloat) {
                pendingFloat.style.maxHeight = pendingHeight + 'px';
            }
        }
    }

    // 计算菜品重复次数
    function calculateDishCounts() {
        const dishCounts = {};

        // 遍历所有包厢的所有菜品
        roomsData.forEach(room => {
            const allDishes = [
                ...(room.pending_dishes || []),
                ...(room.ready_dishes || [])
            ];

            allDishes.forEach(dish => {
                const dishName = dish.name;
                if (!dishCounts[dishName]) {
                    dishCounts[dishName] = 0;
                }
                dishCounts[dishName]++;
            });
        });

        return dishCounts;
    }

    // 加载打荷页面配置
    async function loadKitchenHelperConfig() {
        try {
            const response = await fetch('/api/kitchen-helper-config');
            if (response.ok) {
                kitchenHelperConfig = await response.json();
                applyConfiguration();
            }
        } catch (error) {
            console.error('加载配置失败:', error);
            // 使用默认配置
            kitchenHelperConfig = {
                kitchen_helper_font_size: 16,
                kitchen_helper_font_color: '#ffffff',
                kitchen_helper_theme: 'dark',
                kitchen_helper_border_size: 2,
                kitchen_helper_container_width: 'auto',
                kitchen_helper_padding_top: 8,
                kitchen_helper_padding_bottom: 8,
                kitchen_helper_padding_left: 12,
                kitchen_helper_padding_right: 12,
                kitchen_helper_rooms_per_row: 5,
                kitchen_helper_room_gap: 10
            };
            applyConfiguration();
        }
    }

    // 应用配置
    function applyConfiguration() {
        const root = document.documentElement;

        // 应用CSS变量
        root.style.setProperty('--font-size', kitchenHelperConfig.kitchen_helper_font_size + 'px');
        root.style.setProperty('--font-color', kitchenHelperConfig.kitchen_helper_font_color);
        root.style.setProperty('--border-size', kitchenHelperConfig.kitchen_helper_border_size + 'px');
        root.style.setProperty('--padding-top', kitchenHelperConfig.kitchen_helper_padding_top + 'px');
        root.style.setProperty('--padding-bottom', kitchenHelperConfig.kitchen_helper_padding_bottom + 'px');
        root.style.setProperty('--padding-left', kitchenHelperConfig.kitchen_helper_padding_left + 'px');
        root.style.setProperty('--padding-right', kitchenHelperConfig.kitchen_helper_padding_right + 'px');
        root.style.setProperty('--rooms-per-row', kitchenHelperConfig.kitchen_helper_rooms_per_row);
        root.style.setProperty('--room-gap', kitchenHelperConfig.kitchen_helper_room_gap + 'px');

        // 应用主题
        if (kitchenHelperConfig.kitchen_helper_theme === 'light') {
            document.body.classList.add('theme-light');
            root.style.setProperty('--helper-bg-color', '#f8f9fa');
            root.style.setProperty('--helper-font-color', '#333333');
        } else {
            document.body.classList.add('theme-dark');
            root.style.setProperty('--helper-bg-color', '#1a1a1a');
            root.style.setProperty('--helper-font-color', '#ffffff');
        }
    }

    // 更新当前时间
    function updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });

        const timeElement = document.getElementById('currentTime');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
    }

    // 加载包厢数据
    async function loadRoomsData() {
        try {
            const response = await fetch('/api/kitchen/rooms-status', {
                credentials: 'include' // 包含cookies进行认证
            });
            if (response.ok) {
                const data = await response.json();
                roomsData = data.rooms || [];
                renderRooms();
                updateStatistics();
            }
        } catch (error) {
            console.error('加载包厢数据失败:', error);
        }
    }

    // 自然排序函数
    function naturalSort(a, b) {
        const aNum = a.room_name.match(/\d+/);
        const bNum = b.room_name.match(/\d+/);

        if (aNum && bNum) {
            // 都包含数字，按数字大小排序
            const aNumber = parseInt(aNum[0]);
            const bNumber = parseInt(bNum[0]);
            if (aNumber !== bNumber) {
                return aNumber - bNumber;
            }
            // 数字相同时按字符串排序
            return a.room_name.localeCompare(b.room_name, 'zh-CN');
        } else if (aNum) {
            // 只有a包含数字，a排在前面
            return -1;
        } else if (bNum) {
            // 只有b包含数字，b排在前面
            return 1;
        } else {
            // 都不包含数字，按字母顺序排序
            return a.room_name.localeCompare(b.room_name, 'zh-CN');
        }
    }

    // 渲染包厢
    function renderRooms() {
        const container = document.getElementById('roomsContainer');
        if (!container) return;

        // 对包厢数据进行自然排序
        const sortedRoomsData = [...roomsData].sort(naturalSort);

        if (sortedRoomsData.length === 0) {
            container.innerHTML = `
                <div style="width: 100%; text-align: center; padding: 60px 40px; color: #888; display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 300px;">
                    <i class="bi bi-info-circle" style="font-size: 4rem; margin-bottom: 30px; color: #ffc107;"></i>
                    <h3 style="color: #ffc107; margin-bottom: 15px; font-size: 1.8rem;">暂无用餐中的包厢</h3>
                    <p style="font-size: 1.2rem; line-height: 1.5; max-width: 400px;">当前没有包厢开始用餐，请等待服务员开始用餐操作</p>
                    <div style="margin-top: 20px; padding: 15px; background: rgba(255, 193, 7, 0.1); border: 1px solid #ffc107; border-radius: 8px; max-width: 500px;">
                        <p style="color: #ffc107; font-size: 1rem; margin: 0;">
                            <i class="bi bi-lightbulb"></i>
                            系统已移除分页限制，所有活跃包厢都会自动显示
                        </p>
                    </div>
                </div>
            `;
            return;
        }

        // 计算菜品重复次数
        const dishCounts = calculateDishCounts();

        // 添加说明信息
        const infoBar = `
            <div style="width: 100%; background: rgba(111, 66, 193, 0.1); border: 1px solid #6f42c1; border-radius: 8px; padding: 12px 20px; margin-bottom: 20px; display: flex; align-items: center; justify-content: center;">
                <i class="bi bi-info-circle" style="color: #6f42c1; margin-right: 10px;"></i>
                <span style="color: #6f42c1; font-size: 1rem;">
                    正在显示所有 ${sortedRoomsData.length} 个活跃包厢（按包厢号排序），支持向下滚动查看更多
                </span>
            </div>
        `;

        container.innerHTML = infoBar + sortedRoomsData.map(room => {
            const pendingDishes = room.pending_dishes || [];
            const readyDishes = room.ready_dishes || [];
            // 注意：简化版本没有cooking状态，只有pending_cook和ready

            return `
                <div class="room-column room-started">
                    <div class="room-header">
                        ${room.room_name}
                        <small style="display: block; font-size: 0.8rem; opacity: 0.8;">
                            ${room.guest_count}人 | ${room.dining_start_time || ''}
                        </small>
                    </div>
                    <div class="dish-items-container">
                        <!-- 待制作菜品区域 -->
                        ${pendingDishes.length > 0 ? `
                            <div class="pending-dishes-section">
                                ${pendingDishes.map(dish => {
                                    const dishCount = dishCounts[dish.name] || 1;
                                    const duplicateMarker = dishCount > 1 ? `<span class="duplicate-marker">${dishCount}</span>` : '';
                                    return `
                                        <div class="dish-item pending_cook" onclick="confirmDishCompletion(${dish.id}, '${dish.name}')">
                                            <div class="dish-name">
                                                ${dish.name}
                                                ${duplicateMarker}
                                            </div>
                                            <div class="dish-status">点击确认完成</div>
                                        </div>
                                    `;
                                }).join('')}
                            </div>
                        ` : ''}

                        <!-- 已完成菜品区域 -->
                        ${readyDishes.length > 0 ? `
                            <div class="completed-dishes-section">
                                <div class="completed-dishes-header">已完成菜品</div>
                                <div class="completed-dishes-grid">
                                    ${readyDishes.map(dish => {
                                        const dishCount = dishCounts[dish.name] || 1;
                                        const duplicateMarker = dishCount > 1 ? `<span class="duplicate-marker">${dishCount}</span>` : '';
                                        return `
                                            <div class="dish-item ready">
                                                <div class="dish-name">
                                                    ${dish.name}
                                                    ${duplicateMarker}
                                                </div>
                                                <div class="dish-status">已完成</div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    // 更新统计信息
    function updateStatistics() {
        let pendingCount = 0;
        let readyCount = 0;

        // 使用排序后的数据进行统计
        const sortedRoomsData = [...roomsData].sort(naturalSort);

        sortedRoomsData.forEach(room => {
            pendingCount += (room.pending_dishes || []).length;
            readyCount += (room.ready_dishes || []).length;
        });

        // 更新头部统计（简化版本没有cooking状态）
        const roomsElement = document.getElementById('roomsCount');
        const pendingElement = document.getElementById('pendingCount');
        const cookingElement = document.getElementById('cookingCount');
        const readyElement = document.getElementById('readyCount');

        if (roomsElement) roomsElement.textContent = sortedRoomsData.length;
        if (pendingElement) pendingElement.textContent = pendingCount;
        if (cookingElement) cookingElement.textContent = 0; // 简化版本没有cooking状态
        if (readyElement) readyElement.textContent = readyCount;
    }

    // 确认菜品完成 - 恢复确认对话框机制
    function confirmDishCompletion(dishId, dishName) {
        // 添加触控反馈
        const dishElement = event.target.closest('.dish-item');
        if (dishElement) {
            // 增强视觉反馈
            dishElement.style.transform = 'scale(0.95)';
            dishElement.style.borderColor = '#ffc107';

            setTimeout(() => {
                dishElement.style.transform = '';
                dishElement.style.borderColor = '#28a745';
            }, 150);
        }

        // 显示确认对话框
        const confirmed = confirm(`确认标记'${dishName}'为完成状态？`);
        if (!confirmed) {
            return; // 用户取消，不执行任何操作
        }

        // 显示加载状态
        if (dishElement) {
            const originalContent = dishElement.innerHTML;
            dishElement.innerHTML = `
                <div class="dish-name" style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${dishName}</div>
                <div class="dish-status" style="color: #ffc107;">正在标记完成...</div>
            `;

            // 用户确认后调用状态更新
            changeDishStatus(dishId, 'ready').catch(() => {
                // 如果失败，恢复原始内容并显示错误提示
                dishElement.innerHTML = originalContent;
                dishElement.style.borderColor = '#dc3545';
                setTimeout(() => {
                    dishElement.style.borderColor = '#28a745';
                }, 2000);
            });
        } else {
            changeDishStatus(dishId, 'ready');
        }
    }

    // 改变菜品状态
    async function changeDishStatus(dishId, newStatus) {
        try {
            // 在更新状态前，先获取菜品信息用于通知
            let dishInfo = null;
            if (newStatus === 'ready') {
                dishInfo = findDishInfo(dishId);
                console.log(`🔍 查找菜品信息: dishId=${dishId}, dishInfo=`, dishInfo);
            }

            const response = await fetch('/api/kitchen/dish-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // 包含cookies进行认证
                body: JSON.stringify({
                    dish_id: dishId,
                    status: newStatus
                })
            });

            if (response.ok) {
                console.log(`✅ 菜品状态更新成功: ${dishId} -> ${newStatus}`);

                // 如果是标记完成，发送厨房大屏通知
                if (newStatus === 'ready' && dishInfo) {
                    console.log(`📢 准备发送菜品完成通知:`, dishInfo);
                    await sendDishCompletionNotification(dishInfo);
                } else if (newStatus === 'ready' && !dishInfo) {
                    console.error(`❌ 无法找到菜品信息，无法发送通知: dishId=${dishId}`);
                }

                // 立即刷新数据
                await loadRoomsData();
            } else {
                const error = await response.json();
                console.error('状态更新失败:', error);
                alert('状态更新失败: ' + (error.detail || '未知错误'));
            }
        } catch (error) {
            console.error('更新菜品状态失败:', error);
            alert('网络错误，请重试');
        }
    }

    // 查找菜品信息
    function findDishInfo(dishId) {
        for (const room of roomsData) {
            const allDishes = [
                ...(room.pending_dishes || []),
                ...(room.ready_dishes || [])
            ];

            for (const dish of allDishes) {
                if (dish.id === dishId) {
                    return {
                        dishName: dish.name,
                        roomName: room.room_name
                    };
                }
            }
        }
        return null;
    }

    // 发送菜品完成通知到厨房大屏
    async function sendDishCompletionNotification(dishInfo) {
        try {
            const notificationData = {
                type: 'dish_completion',
                message: `${dishInfo.roomName}${dishInfo.dishName}，跑菜`,
                roomName: dishInfo.roomName,
                dishName: dishInfo.dishName,
                timestamp: new Date().toISOString()
            };

            console.log(`📤 发送菜品完成通知数据:`, notificationData);

            // 发送到后端，由后端通过WebSocket广播到厨房大屏
            const response = await fetch('/api/kitchen/dish-completion-notification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // 包含cookies进行认证
                body: JSON.stringify(notificationData)
            });

            console.log(`📡 API响应状态: ${response.status}`);

            if (response.ok) {
                const result = await response.json();
                console.log(`📢 菜品完成通知已发送: ${dishInfo.roomName}${dishInfo.dishName}`, result);

                // 🔧 新增：打荷端本地语音播报菜品完成
                const voiceMessage = `${dishInfo.roomName}${dishInfo.dishName}跑菜`;
                console.log(`🎵 打荷端本地播报菜品完成: ${voiceMessage}`);
                addToVoiceQueue(voiceMessage, 'dish_completion');
            } else {
                const error = await response.text();
                console.error('发送菜品完成通知失败:', response.status, error);
            }
        } catch (error) {
            console.error('发送菜品完成通知失败:', error);
        }
    }

    // 加载服务员指令 - 修复版本，确保指令持久化显示直到手动确认
    async function loadWaiterActions() {
        try {
            const response = await fetch('/api/waiter-actions/latest', {
                credentials: 'include' // 包含cookies进行认证
            });
            if (response.ok) {
                const data = await response.json();

                // 获取本地已确认的指令ID列表
                const localConfirmedIds = JSON.parse(localStorage.getItem('confirmedActionIds') || '[]');
                console.log(`📋 本地已确认指令数量: ${localConfirmedIds.length}`);

                // 修复：确保指令持久化显示
                // 1. 获取服务器返回的未处理指令
                const serverActions = (data.actions || []).filter(action => !action.is_processed);
                console.log(`📋 服务器返回未处理指令: ${serverActions.length} 条`);

                // 2. 合并现有的本地指令和新的服务器指令
                const existingActionIds = waiterActionsData.map(action => action.id);
                const newServerActions = serverActions.filter(action => !existingActionIds.includes(action.id));
                console.log(`📋 新增服务器指令: ${newServerActions.length} 条`);

                // 🔧 新增：为新指令触发语音播报
                if (newServerActions.length > 0) {
                    newServerActions.forEach(action => {
                        // 构建语音播报内容
                        let voiceMessage = '';
                        if (action.action_type === 'start_dining' || action.action_type_display === '开始用餐') {
                            const count = action.action_content || '未知';
                            voiceMessage = `${action.room_number}包厢${count}人起菜`;
                        } else {
                            const actionText = action.action_type_display || action.action_type;
                            const content = action.action_content ? action.action_content : '';
                            voiceMessage = `${action.room_number}包厢${actionText}${content}`;
                        }

                        console.log(`🎵 打荷端新指令语音播报: ${voiceMessage}`);
                        addToVoiceQueue(voiceMessage, 'instruction');
                    });
                }

                // 3. 合并指令列表，保持现有指令不被清除
                const allActions = [...waiterActionsData, ...newServerActions];

                // 4. 只过滤掉本地已确认的指令
                waiterActionsData = allActions.filter(action => !localConfirmedIds.includes(action.id));

                console.log(`📋 最终显示指令数量: ${waiterActionsData.length} 条`);
                console.log(`📋 指令详情:`, waiterActionsData.map(a => `ID:${a.id} 包厢:${a.room_number} 类型:${a.action_type_display || a.action_type}`));

                renderWaiterActions();
            } else {
                console.error('获取服务员指令失败:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('加载服务员指令失败:', error);
        }
    }

    // 渲染服务员指令
    function renderWaiterActions() {
        const container = document.getElementById('waiterActionsContainer');
        if (!container) return;

        if (waiterActionsData.length === 0) {
            container.innerHTML = `
                <div class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    暂无服务员指令
                </div>
            `;
            return;
        }

        // 最新指令在顶部显示
        const sortedActions = waiterActionsData.slice().sort((a, b) =>
            new Date(b.created_at) - new Date(a.created_at)
        );

        container.innerHTML = sortedActions.slice(0, 10).map(action => {
            // 格式化指令内容
            let displayContent = '';
            const waiterName = action.waiter_name || '服务员';

            // 特殊处理"开始用餐"指令
            if (action.action_type === 'start_dining' || action.action_type_display === '开始用餐') {
                // action_content直接包含人数数字，不需要正则匹配
                const count = action.action_content || '未知';
                displayContent = `[${waiterName}] 用餐开始：${count}人`;
            } else {
                // 其他指令的标准格式
                const actionText = action.action_type_display || action.action_type;
                const content = action.action_content ? '：' + action.action_content : '';
                displayContent = `[${waiterName}] ${actionText}${content}`;
            }

            return `
                <div class="action-item" id="action-${action.id}">
                    <button class="action-confirm-btn" onclick="confirmWaiterAction(${action.id})" title="点击确认已处理此指令" style="background-color: #28a745; color: white; border: none; border-radius: 3px; padding: 2px 6px; cursor: pointer;">✓</button>
                    <div class="action-room">${action.room_number}包厢</div>
                    <div class="action-content">${displayContent}</div>
                    <div class="action-time">${formatTime(action.created_at)}</div>
                </div>
            `;
        }).join('');
    }



    // 初始化悬浮窗
    function initializeFloatWindow() {
        const floatWindow = document.getElementById('waiterActionsFloat');
        const floatHeader = document.getElementById('floatHeader');

        // 从localStorage恢复位置
        const savedPosition = localStorage.getItem('waiterFloatPosition');
        if (savedPosition) {
            const position = JSON.parse(savedPosition);
            floatWindow.style.bottom = position.bottom + 'px';
            floatWindow.style.right = position.right + 'px';
        }

        // 恢复最小化状态
        const savedMinimized = localStorage.getItem('waiterFloatMinimized');
        if (savedMinimized === 'true') {
            isFloatMinimized = true;
            const floatContent = document.getElementById('floatContent');
            const toggleIcon = document.getElementById('toggleIcon');

            floatContent.style.display = 'none';
            floatWindow.style.height = 'auto';
            floatWindow.style.minHeight = '50px';
            toggleIcon.className = 'bi bi-plus';
        }

        // 拖拽功能
        floatHeader.addEventListener('mousedown', startDrag);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', stopDrag);

        // 触摸设备支持
        floatHeader.addEventListener('touchstart', startDrag);
        document.addEventListener('touchmove', drag);
        document.addEventListener('touchend', stopDrag);
    }

    // 开始拖拽
    function startDrag(e) {
        isDragging = true;
        const floatWindow = document.getElementById('waiterActionsFloat');
        const rect = floatWindow.getBoundingClientRect();

        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        dragOffset.x = clientX - rect.left;
        dragOffset.y = clientY - rect.top;

        floatWindow.style.transition = 'none';
        e.preventDefault();
    }

    // 拖拽中
    function drag(e) {
        if (!isDragging) return;

        const floatWindow = document.getElementById('waiterActionsFloat');
        const clientX = e.clientX || (e.touches && e.touches[0].clientX);
        const clientY = e.clientY || (e.touches && e.touches[0].clientY);

        const newLeft = clientX - dragOffset.x;
        const newTop = clientY - dragOffset.y;

        // 边界检查
        const maxLeft = window.innerWidth - floatWindow.offsetWidth;
        const maxTop = window.innerHeight - floatWindow.offsetHeight;

        const constrainedLeft = Math.max(0, Math.min(newLeft, maxLeft));
        const constrainedTop = Math.max(0, Math.min(newTop, maxTop));

        floatWindow.style.left = constrainedLeft + 'px';
        floatWindow.style.top = constrainedTop + 'px';
        floatWindow.style.right = 'auto';

        e.preventDefault();
    }

    // 停止拖拽
    function stopDrag() {
        if (!isDragging) return;

        isDragging = false;
        const floatWindow = document.getElementById('waiterActionsFloat');
        floatWindow.style.transition = 'all 0.3s ease';

        // 保存位置
        const rect = floatWindow.getBoundingClientRect();
        const position = {
            bottom: window.innerHeight - rect.bottom,
            right: window.innerWidth - rect.right
        };
        localStorage.setItem('waiterFloatPosition', JSON.stringify(position));
    }

    // 切换悬浮窗显示状态 - 修复整个窗口隐藏
    function toggleFloatWindow() {
        const floatWindow = document.getElementById('waiterActionsFloat');
        const floatContent = document.getElementById('floatContent');
        const toggleIcon = document.getElementById('toggleIcon');

        isFloatMinimized = !isFloatMinimized;

        if (isFloatMinimized) {
            // 最小化：隐藏内容，只显示标题栏
            floatContent.style.display = 'none';
            floatWindow.style.height = 'auto';
            floatWindow.style.minHeight = '50px';
            toggleIcon.className = 'bi bi-plus';
        } else {
            // 展开：显示内容，恢复原始高度
            floatContent.style.display = 'block';
            floatWindow.style.height = '67vh';
            floatWindow.style.minHeight = '200px';
            toggleIcon.className = 'bi bi-dash';
        }

        // 保存最小化状态
        localStorage.setItem('waiterFloatMinimized', isFloatMinimized.toString());
    }

    // 重置悬浮窗位置
    function resetFloatPosition() {
        const floatWindow = document.getElementById('waiterActionsFloat');
        floatWindow.style.bottom = '20px';
        floatWindow.style.right = '20px';
        floatWindow.style.top = 'auto';
        floatWindow.style.maxHeight = '400px'; // 重置高度

        // 清除保存的位置和高度
        localStorage.removeItem('waiterFloatPosition');
        localStorage.removeItem('waiterFloatHeight');
    }

    // 显示Toast提示
    function showToast(type, message) {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = `
            <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
            ${message}
        `;

        // 添加样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : '#dc3545'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 显示Toast提示
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 9999;
            font-size: 14px;
            max-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        toast.textContent = message;

        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);

        // 3秒后自动移除
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 确认服务员指令 - 修复版本，确保指令必须手动确认才消失
    async function confirmWaiterAction(actionId) {
        try {
            console.log(`🔄 正在确认指令 ID: ${actionId}`);

            // 显示确认对话框，确保打荷员主动确认
            const confirmed = confirm('确认已处理此服务员指令？');
            if (!confirmed) {
                console.log(`❌ 用户取消确认指令 ${actionId}`);
                return; // 用户取消，不执行任何操作
            }

            const response = await fetch(`/api/waiter-actions/${actionId}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include' // 包含cookies进行认证
            });

            if (response.ok) {
                console.log(`✅ 指令 ${actionId} 服务器确认成功`);

                // 修复：将已确认的指令ID保存到localStorage，确保持久化
                const confirmedIds = JSON.parse(localStorage.getItem('confirmedActionIds') || '[]');
                if (!confirmedIds.includes(actionId)) {
                    confirmedIds.push(actionId);
                    localStorage.setItem('confirmedActionIds', JSON.stringify(confirmedIds));
                    console.log(`💾 指令 ${actionId} 已保存到本地确认列表`);
                }

                // 立即从本地数据中移除已确认的指令
                const beforeCount = waiterActionsData.length;
                waiterActionsData = waiterActionsData.filter(action => action.id !== actionId);
                const afterCount = waiterActionsData.length;
                console.log(`🗑️ 从本地移除指令 ${actionId}，指令数量: ${beforeCount} → ${afterCount}`);

                renderWaiterActions();

                // 显示成功提示
                showToast('指令已确认处理', 'success');

                // 可选：显示简短的视觉反馈
                const button = document.querySelector(`button[onclick="confirmWaiterAction(${actionId})"]`);
                if (button) {
                    button.innerHTML = '<i class="bi bi-check-circle"></i> 已确认';
                    button.disabled = true;
                    button.style.backgroundColor = '#6c757d';
                    button.style.borderColor = '#6c757d';
                }
            } else {
                const error = await response.json();
                console.error(`❌ 确认指令 ${actionId} 失败:`, error);
                alert('确认失败: ' + (error.detail || '未知错误'));
            }
        } catch (error) {
            console.error('确认服务员指令失败:', error);
            alert('网络错误，请重试');
        }
    }

    // 清理过期的确认记录（避免localStorage过大）
    function cleanupExpiredConfirmations() {
        try {
            const confirmedIds = JSON.parse(localStorage.getItem('confirmedActionIds') || '[]');
            console.log(`🧹 开始清理确认记录，当前数量: ${confirmedIds.length}`);

            // 只保留最近100个确认记录，避免localStorage过大
            if (confirmedIds.length > 100) {
                const recentIds = confirmedIds.slice(-100);
                localStorage.setItem('confirmedActionIds', JSON.stringify(recentIds));
                console.log(`🧹 清理过期确认记录，保留最近 ${recentIds.length} 条`);
            } else {
                console.log(`🧹 确认记录数量正常，无需清理`);
            }
        } catch (error) {
            console.error('清理确认记录失败:', error);
        }
    }

    // 初始化服务员指令持久化机制
    function initializeWaiterActionsPersistence() {
        try {
            // 确保localStorage中有确认记录数组
            if (!localStorage.getItem('confirmedActionIds')) {
                localStorage.setItem('confirmedActionIds', '[]');
                console.log(`🔧 初始化确认记录存储`);
            }

            const confirmedIds = JSON.parse(localStorage.getItem('confirmedActionIds') || '[]');
            console.log(`🔧 当前本地确认记录数量: ${confirmedIds.length}`);

            // 清理过期记录
            cleanupExpiredConfirmations();

        } catch (error) {
            console.error('初始化服务员指令持久化机制失败:', error);
            // 重置localStorage
            localStorage.setItem('confirmedActionIds', '[]');
        }
    }

    // 调试功能：查看当前指令状态
    function debugWaiterActions() {
        const confirmedIds = JSON.parse(localStorage.getItem('confirmedActionIds') || '[]');
        console.log('=== 服务员指令调试信息 ===');
        console.log(`当前显示指令数量: ${waiterActionsData.length}`);
        console.log(`本地确认记录数量: ${confirmedIds.length}`);
        console.log('当前显示指令:', waiterActionsData);
        console.log('本地确认记录:', confirmedIds);
        console.log('========================');

        // 在控制台显示详细信息
        waiterActionsData.forEach((action, index) => {
            console.log(`指令 ${index + 1}: ID=${action.id}, 包厢=${action.room_number}, 类型=${action.action_type_display || action.action_type}, 内容=${action.action_content}`);
        });
    }

    // 🔧 新增：移动端语音播报测试函数
    function testMobileVoice() {
        console.log(`📱 开始测试移动端语音播报功能`);
        console.log(`📱 设备信息: 移动端=${isMobileDevice}, 语音初始化=${voiceInitialized}`);
        console.log(`📱 语音支持: ${'speechSynthesis' in window}`);

        if ('speechSynthesis' in window) {
            console.log(`📱 语音合成器状态: 暂停=${speechSynthesis.paused}, 正在播报=${speechSynthesis.speaking}`);
        }

        // 测试语音播报
        const testMessage = "打荷端移动设备语音播报测试";
        console.log(`📱 测试播报: ${testMessage}`);
        addToVoiceQueue(testMessage, 'test');

        return {
            isMobile: isMobileDevice,
            voiceInitialized: voiceInitialized,
            hasVoiceSupport: 'speechSynthesis' in window,
            queueLength: voiceQueue.length,
            isPlaying: isVoicePlaying
        };
    }

    // 将调试函数暴露到全局，方便在控制台调用
    window.debugWaiterActions = debugWaiterActions;
    window.testMobileVoice = testMobileVoice; // 🔧 新增：暴露移动端语音测试函数

    // 🔧 新增：WebSocket消息处理（模拟）
    function handleWebSocketMessage(message) {
        switch(message.type) {
            case 'waiter_instruction':
                // 处理服务员指令
                const instructionMessage = `${message.room_number}包厢${message.instruction}`;
                console.log(`🎵 打荷端接收到服务员指令: ${instructionMessage}`);
                addToVoiceQueue(instructionMessage, 'instruction');

                // 刷新指令显示
                setTimeout(() => {
                    loadWaiterActions();
                }, 500);
                break;

            case 'dish_ready':
                // 菜品制作完成通知
                if (message.room_number && message.dish_name) {
                    const dishMessage = `${message.room_number}${message.dish_name}跑菜`;
                    console.log(`🎵 打荷端接收到菜品完成: ${dishMessage}`);
                    addToVoiceQueue(dishMessage, 'dish_ready');
                }
                break;

            case 'dining_started':
                // 用餐开始通知
                const startMessage = `${message.room_number}包厢${message.guest_count}人起菜`;
                console.log(`🎵 打荷端接收到用餐开始: ${startMessage}`);
                addToVoiceQueue(startMessage, 'dining_start');
                break;

            case 'dining_ended':
                // 用餐结束通知
                const endMessage = `${message.room_number}包厢用餐结束`;
                console.log(`🎵 打荷端接收到用餐结束: ${endMessage}`);
                addToVoiceQueue(endMessage, 'dining_end');
                break;

            case 'voice_config_updated':
                // 🔧 新增：处理语音配置更新事件
                console.log(`🔄 打荷端接收到语音配置更新事件`);
                handleVoiceConfigUpdate();
                break;
        }
    }

    // 🔧 新增：测试语音播报统一性的函数
    async function testVoiceBroadcastUniformity() {
        console.log(`🧪 开始测试打荷端语音播报统一性`);

        const config = await getVoiceConfig();
        console.log(`🔧 当前语音配置:`, config);

        // 测试服务员指令播报
        const testMessage = "1包厢测试服务员指令播报";

        console.log(`🎵 测试播报: ${testMessage}`);
        console.log(`🔧 使用参数: 速度=${config.voice_rate}, 音量=${config.voice_volume}, 音调=${config.voice_pitch}, 重复=${config.voice_repeat_count}次, 间隔=${config.voice_repeat_interval}秒`);

        // 使用统一的播报函数
        await addToVoiceQueue(testMessage, 'test');
    }

    // 格式化时间
    function formatTime(timeString) {
        const date = new Date(timeString);
        return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
</script>
{% endblock %}
