{% extends "base.html" %}

{% block title %}系统设置{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="bi bi-gear"></i> 系统设置</h2>
                <a href="/dashboard" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> 返回
                </a>
            </div>

            <!-- 服务员界面设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-person-badge"></i> 服务员界面设置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="waiterSettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrow-clockwise"></i> 自动刷新间隔
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="waiterRefreshInterval"
                                               min="0" max="300" value="15">
                                        <span class="input-group-text">秒</span>
                                    </div>
                                    <div class="form-text">
                                        设置服务员界面自动刷新的时间间隔<br>
                                        <strong>设置为 0 表示禁用自动刷新</strong>，建议范围：10-60秒
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-toggle-on"></i> 启用自动刷新
                                    </label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="waiterRefreshEnabled" checked>
                                        <label class="form-check-label" for="waiterRefreshEnabled">
                                            启用服务员界面自动刷新功能
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存服务员设置
                        </button>
                    </form>
                </div>
            </div>

            <!-- 厨房大屏设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-display"></i> 厨房大屏设置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="kitchenDisplaySettingsForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-grid-3x3"></i> 每页显示包厢数量
                                    </label>
                                    <select class="form-select" id="kitchenRoomsPerPage">
                                        <option value="3">3个包厢</option>
                                        <option value="4">4个包厢</option>
                                        <option value="5" selected>5个包厢</option>
                                        <option value="6">6个包厢</option>
                                        <option value="7">7个包厢</option>
                                        <option value="8">8个包厢</option>
                                    </select>
                                    <div class="form-text">设置厨房大屏每页显示的包厢数量</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrows-expand"></i> 包厢容器宽度
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenRoomWidth"
                                               min="300" max="500" value="370">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">设置包厢容器的显示宽度（300-500px）</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-fonts"></i> 菜品字体大小
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenFontSize"
                                               min="10" max="16" value="14">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">设置菜品名称的字体大小（10-16px）</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-layout-three-columns"></i> 布局模式
                                    </label>
                                    <select class="form-select" id="kitchenLayoutMode">
                                        <option value="auto" selected>自动布局</option>
                                        <option value="manual">手动指定</option>
                                    </select>
                                    <div class="form-text">选择包厢网格的布局方式</div>
                                </div>
                            </div>
                        </div>

                        <div class="row" id="manualLayoutSettings" style="display: none;">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrows-expand"></i> 网格列数
                                    </label>
                                    <select class="form-select" id="kitchenGridColumns">
                                        <option value="2">2列</option>
                                        <option value="3" selected>3列</option>
                                        <option value="4">4列</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrows-expand-vertical"></i> 网格行数
                                    </label>
                                    <select class="form-select" id="kitchenGridRows">
                                        <option value="1">1行</option>
                                        <option value="2" selected>2行</option>
                                        <option value="3">3行</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-skip-end"></i> 自动翻页间隔
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenFlipInterval"
                                               min="3" max="60" value="8">
                                        <span class="input-group-text">秒</span>
                                    </div>
                                    <div class="form-text">设置厨房大屏自动翻页的时间间隔（3-60秒）</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-toggle-on"></i> 自动翻页
                                    </label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="kitchenAutoFlipEnabled" checked>
                                        <label class="form-check-label" for="kitchenAutoFlipEnabled">
                                            启用厨房大屏自动翻页功能
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="kitchenHideNavigation" checked>
                                        <label class="form-check-label" for="kitchenHideNavigation">
                                            <i class="bi bi-eye-slash"></i> 隐藏导航菜单（纯显示模式）
                                        </label>
                                    </div>
                                    <div class="form-text">启用后厨房大屏将完全隐藏导航菜单</div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存厨房大屏设置
                        </button>
                    </form>
                </div>
            </div>

            <!-- 移动端界面设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-phone"></i> 移动端界面设置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="mobileSettingsForm">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-fonts"></i> 正文字体大小
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="mobileFontNormal"
                                               min="14" max="18" value="16">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">移动端正文字体大小（14-18px）</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-type-h1"></i> 标题字体大小
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="mobileFontTitle"
                                               min="18" max="24" value="20">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">移动端标题字体大小（18-24px）</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-megaphone"></i> 指令字体大小
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="mobileFontInstruction"
                                               min="16" max="20" value="18">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">移动端指令字体大小（16-20px）</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-square"></i> 按钮最小尺寸
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="mobileButtonSize"
                                               min="40" max="60" value="44">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">移动端按钮最小尺寸（40-60px）</div>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存移动端设置
                        </button>
                    </form>
                </div>
            </div>

            <!-- 打荷操作页面设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-fire"></i> 打荷操作页面设置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="kitchenHelperSettingsForm">
                        <div class="row">
                            <!-- 字体设置 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-fonts"></i> 字体大小
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenHelperFontSize"
                                               min="12" max="24" value="16">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">设置菜品文字的字体大小（12-24px）</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-palette"></i> 字体颜色
                                    </label>
                                    <input type="color" class="form-control form-control-color" id="kitchenHelperFontColor" value="#ffffff">
                                    <div class="form-text">设置菜品文字的颜色</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- 主题设置 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-moon"></i> 页面主题
                                    </label>
                                    <select class="form-select" id="kitchenHelperTheme">
                                        <option value="dark">深色主题</option>
                                        <option value="light">浅色主题</option>
                                    </select>
                                    <div class="form-text">选择打荷页面的主题风格</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-border-style"></i> 边框大小
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenHelperBorderSize"
                                               min="1" max="5" value="2">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">设置菜品卡片的边框粗细（1-5px）</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- 布局设置 -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-grid"></i> 每行包厢数量
                                    </label>
                                    <select class="form-select" id="kitchenHelperRoomsPerRow">
                                        <option value="3">3个包厢</option>
                                        <option value="4">4个包厢</option>
                                        <option value="5">5个包厢</option>
                                        <option value="6">6个包厢</option>
                                        <option value="7">7个包厢</option>
                                        <option value="8">8个包厢</option>
                                    </select>
                                    <div class="form-text">设置每行显示的包厢数量</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrows-expand"></i> 包厢间距
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenHelperRoomGap"
                                               min="5" max="30" value="10">
                                        <span class="input-group-text">px</span>
                                    </div>
                                    <div class="form-text">设置包厢之间的间距（5-30px）</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- 内边距设置 -->
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrow-up"></i> 上边距
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenHelperPaddingTop"
                                               min="4" max="20" value="8">
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrow-down"></i> 下边距
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenHelperPaddingBottom"
                                               min="4" max="20" value="8">
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrow-left"></i> 左边距
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenHelperPaddingLeft"
                                               min="6" max="24" value="12">
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-arrow-right"></i> 右边距
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="kitchenHelperPaddingRight"
                                               min="6" max="24" value="12">
                                        <span class="input-group-text">px</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check"></i> 保存打荷页面设置
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 订单管理设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-cart"></i> 订单管理设置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="orderSettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-globe"></i> 系统时区
                                    </label>
                                    <select class="form-select" id="systemTimezone">
                                        <option value="Asia/Shanghai">中国标准时间 (UTC+8)</option>
                                        <option value="Asia/Hong_Kong">香港时间 (UTC+8)</option>
                                        <option value="Asia/Taipei">台北时间 (UTC+8)</option>
                                        <option value="UTC">协调世界时 (UTC+0)</option>
                                    </select>
                                    <div class="form-text">设置系统使用的时区</div>
                                </div>
                            </div>
                        </div>


                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存订单设置
                        </button>
                    </form>
                </div>
            </div>



            <!-- 系统初始化 - 危险操作区域 -->
            <div class="card border-danger mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i> 危险操作区域
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <h6><i class="bi bi-shield-exclamation"></i> 系统初始化</h6>
                        <p class="mb-3">
                            <strong>警告：</strong>此操作将清除所有业务数据，包括包厢、订单、用户等信息，
                            系统将恢复到全新安装状态。<strong>此操作不可逆！</strong>
                        </p>
                        <p class="mb-3">
                            <strong>保留内容：</strong>仅保留admin管理员账号和系统配置信息。
                        </p>
                        <p class="mb-0">
                            <strong>清理内容：</strong>所有包厢、订单、用户（除admin）、指令模板、操作记录等。
                        </p>
                    </div>

                    <div class="text-center">
                        <button type="button" class="btn btn-danger btn-lg" onclick="showSystemResetModal()">
                            <i class="bi bi-arrow-counterclockwise"></i> 系统初始化
                        </button>
                    </div>
                </div>
            </div>

            <!-- 界面主题设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-palette"></i> 界面主题设置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="themeSettingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-moon-stars"></i> 系统主题
                                    </label>
                                    <select class="form-select" id="systemTheme">
                                        <option value="light">浅色模式</option>
                                        <option value="dark">深色模式</option>
                                        <option value="auto">跟随系统</option>
                                    </select>
                                    <div class="form-text">选择系统整体的界面主题风格</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">
                                        <i class="bi bi-display"></i> 厨房大屏主题
                                    </label>
                                    <select class="form-select" id="kitchenTheme">
                                        <option value="dark">深色模式（推荐）</option>
                                        <option value="light">浅色模式</option>
                                    </select>
                                    <div class="form-text">厨房大屏专用主题，深色模式对眼睛更友好</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>主题说明：</strong>
                                    <ul class="mb-0 mt-2">
                                        <li><strong>浅色模式</strong>：传统的白色背景界面，适合明亮环境</li>
                                        <li><strong>深色模式</strong>：黑色背景界面，减少眼部疲劳，适合长时间使用</li>
                                        <li><strong>跟随系统</strong>：根据操作系统的主题设置自动切换</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 保存主题设置
                        </button>
                    </form>
                </div>
            </div>

            <!-- 语音播报设置快捷入口 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-volume-up"></i> 其他设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="/voice-settings" class="btn btn-outline-primary">
                                    <i class="bi bi-volume-up"></i> 语音播报设置
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="/command-templates" class="btn btn-outline-secondary">
                                    <i class="bi bi-megaphone"></i> 指令模板管理
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> 设置说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>服务员界面设置</h6>
                            <ul class="mb-3">
                                <li><strong>自动刷新间隔</strong>：控制服务员界面自动刷新的频率
                                    <ul>
                                        <li>设置为 <strong>0</strong> 表示禁用自动刷新</li>
                                        <li>建议设置为 10-60秒，避免过于频繁</li>
                                    </ul>
                                </li>
                                <li><strong>启用自动刷新</strong>：开启后服务员界面会定期自动刷新，确保信息实时更新</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>厨房界面设置</h6>
                            <ul class="mb-3">
                                <li><strong>大屏翻页间隔</strong>：控制厨房大屏自动翻页的时间，建议设置为5-15秒</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时获取当前配置
    document.addEventListener('DOMContentLoaded', function() {
        loadSystemSettings();

        // 监听布局模式变化
        document.getElementById('kitchenLayoutMode').addEventListener('change', function(e) {
            toggleManualLayoutSettings(e.target.value === 'manual');
        });
    });

    // 切换手动布局设置显示
    function toggleManualLayoutSettings(show) {
        const manualSettings = document.getElementById('manualLayoutSettings');
        if (manualSettings) {
            manualSettings.style.display = show ? 'block' : 'none';
        }
    }

    // 加载系统设置
    function loadSystemSettings() {
        fetch('/api/system-config')
            .then(response => response.json())
            .then(configs => {
                // 设置服务员界面配置
                if (configs.waiter_auto_refresh_interval) {
                    document.getElementById('waiterRefreshInterval').value = configs.waiter_auto_refresh_interval;
                }
                if (configs.waiter_auto_refresh_enabled !== undefined) {
                    document.getElementById('waiterRefreshEnabled').checked = configs.waiter_auto_refresh_enabled;
                }
                
                // 设置厨房大屏配置
                if (configs.kitchen_display_rooms_per_page) {
                    document.getElementById('kitchenRoomsPerPage').value = configs.kitchen_display_rooms_per_page;
                }
                if (configs.kitchen_display_room_width) {
                    document.getElementById('kitchenRoomWidth').value = configs.kitchen_display_room_width;
                }

                // 设置打荷页面配置
                if (configs.kitchen_helper_font_size) {
                    document.getElementById('kitchenHelperFontSize').value = configs.kitchen_helper_font_size;
                }
                if (configs.kitchen_helper_font_color) {
                    document.getElementById('kitchenHelperFontColor').value = configs.kitchen_helper_font_color;
                }
                if (configs.kitchen_helper_theme) {
                    document.getElementById('kitchenHelperTheme').value = configs.kitchen_helper_theme;
                }
                if (configs.kitchen_helper_border_size) {
                    document.getElementById('kitchenHelperBorderSize').value = configs.kitchen_helper_border_size;
                }
                if (configs.kitchen_helper_rooms_per_row) {
                    document.getElementById('kitchenHelperRoomsPerRow').value = configs.kitchen_helper_rooms_per_row;
                }
                if (configs.kitchen_helper_room_gap) {
                    document.getElementById('kitchenHelperRoomGap').value = configs.kitchen_helper_room_gap;
                }
                if (configs.kitchen_helper_padding_top) {
                    document.getElementById('kitchenHelperPaddingTop').value = configs.kitchen_helper_padding_top;
                }
                if (configs.kitchen_helper_padding_bottom) {
                    document.getElementById('kitchenHelperPaddingBottom').value = configs.kitchen_helper_padding_bottom;
                }
                if (configs.kitchen_helper_padding_left) {
                    document.getElementById('kitchenHelperPaddingLeft').value = configs.kitchen_helper_padding_left;
                }
                if (configs.kitchen_helper_padding_right) {
                    document.getElementById('kitchenHelperPaddingRight').value = configs.kitchen_helper_padding_right;
                }
                if (configs.kitchen_display_font_size) {
                    document.getElementById('kitchenFontSize').value = configs.kitchen_display_font_size;
                }
                if (configs.kitchen_display_layout_mode) {
                    document.getElementById('kitchenLayoutMode').value = configs.kitchen_display_layout_mode;
                    toggleManualLayoutSettings(configs.kitchen_display_layout_mode === 'manual');
                }
                if (configs.kitchen_display_grid_columns) {
                    document.getElementById('kitchenGridColumns').value = configs.kitchen_display_grid_columns;
                }
                if (configs.kitchen_display_grid_rows) {
                    document.getElementById('kitchenGridRows').value = configs.kitchen_display_grid_rows;
                }
                if (configs.kitchen_display_auto_flip_interval) {
                    document.getElementById('kitchenFlipInterval').value = configs.kitchen_display_auto_flip_interval;
                }
                if (configs.kitchen_display_auto_flip_enabled !== undefined) {
                    document.getElementById('kitchenAutoFlipEnabled').checked = configs.kitchen_display_auto_flip_enabled;
                }
                if (configs.kitchen_display_hide_navigation !== undefined) {
                    document.getElementById('kitchenHideNavigation').checked = configs.kitchen_display_hide_navigation;
                }

                // 设置移动端配置
                if (configs.mobile_font_size_normal) {
                    document.getElementById('mobileFontNormal').value = configs.mobile_font_size_normal;
                }
                if (configs.mobile_font_size_title) {
                    document.getElementById('mobileFontTitle').value = configs.mobile_font_size_title;
                }
                if (configs.mobile_font_size_instruction) {
                    document.getElementById('mobileFontInstruction').value = configs.mobile_font_size_instruction;
                }
                if (configs.mobile_button_min_size) {
                    document.getElementById('mobileButtonSize').value = configs.mobile_button_min_size;
                }
                
                // 设置订单管理配置
                if (configs.system_timezone) {
                    document.getElementById('systemTimezone').value = configs.system_timezone;
                }
                if (configs.room_auto_timeout_hours) {
                    document.getElementById('roomTimeout').value = configs.room_auto_timeout_hours;
                }
                if (configs.timeout_check_interval_minutes) {
                    document.getElementById('timeoutCheckInterval').value = configs.timeout_check_interval_minutes;
                }

                // 设置主题配置
                if (configs.system_theme) {
                    document.getElementById('systemTheme').value = configs.system_theme;
                }
                if (configs.kitchen_theme) {
                    document.getElementById('kitchenTheme').value = configs.kitchen_theme;
                }
            })
            .catch(error => {
                console.error('加载系统设置失败:', error);
            });
    }

    // 保存服务员设置
    document.getElementById('waiterSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const configs = {
            waiter_auto_refresh_interval: parseInt(document.getElementById('waiterRefreshInterval').value),
            waiter_auto_refresh_enabled: document.getElementById('waiterRefreshEnabled').checked
        };
        
        saveSystemConfig(configs, '服务员设置');
    });

    // 保存厨房大屏设置
    document.getElementById('kitchenDisplaySettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const configs = {
            kitchen_display_rooms_per_page: parseInt(document.getElementById('kitchenRoomsPerPage').value),
            kitchen_display_room_width: parseInt(document.getElementById('kitchenRoomWidth').value),
            kitchen_display_font_size: parseInt(document.getElementById('kitchenFontSize').value),
            kitchen_display_layout_mode: document.getElementById('kitchenLayoutMode').value,
            kitchen_display_grid_columns: parseInt(document.getElementById('kitchenGridColumns').value),
            kitchen_display_grid_rows: parseInt(document.getElementById('kitchenGridRows').value),
            kitchen_display_auto_flip_interval: parseInt(document.getElementById('kitchenFlipInterval').value),
            kitchen_display_auto_flip_enabled: document.getElementById('kitchenAutoFlipEnabled').checked,
            kitchen_display_hide_navigation: document.getElementById('kitchenHideNavigation').checked
        };

        saveSystemConfig(configs, '厨房大屏设置');
    });

    // 保存打荷页面设置
    document.getElementById('kitchenHelperSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const configs = {
            kitchen_helper_font_size: parseInt(document.getElementById('kitchenHelperFontSize').value),
            kitchen_helper_font_color: document.getElementById('kitchenHelperFontColor').value,
            kitchen_helper_theme: document.getElementById('kitchenHelperTheme').value,
            kitchen_helper_border_size: parseInt(document.getElementById('kitchenHelperBorderSize').value),
            kitchen_helper_rooms_per_row: parseInt(document.getElementById('kitchenHelperRoomsPerRow').value),
            kitchen_helper_room_gap: parseInt(document.getElementById('kitchenHelperRoomGap').value),
            kitchen_helper_padding_top: parseInt(document.getElementById('kitchenHelperPaddingTop').value),
            kitchen_helper_padding_bottom: parseInt(document.getElementById('kitchenHelperPaddingBottom').value),
            kitchen_helper_padding_left: parseInt(document.getElementById('kitchenHelperPaddingLeft').value),
            kitchen_helper_padding_right: parseInt(document.getElementById('kitchenHelperPaddingRight').value)
        };

        saveSystemConfig(configs, '打荷页面设置');
    });

    // 保存移动端设置
    document.getElementById('mobileSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const configs = {
            mobile_font_size_normal: parseInt(document.getElementById('mobileFontNormal').value),
            mobile_font_size_title: parseInt(document.getElementById('mobileFontTitle').value),
            mobile_font_size_instruction: parseInt(document.getElementById('mobileFontInstruction').value),
            mobile_button_min_size: parseInt(document.getElementById('mobileButtonSize').value)
        };

        saveSystemConfig(configs, '移动端设置');
    });

    // 保存订单设置
    document.getElementById('orderSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const configs = {
            system_timezone: document.getElementById('systemTimezone').value
        };

        saveSystemConfig(configs, '订单设置');
    });

    // 保存主题设置
    document.getElementById('themeSettingsForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const configs = {
            system_theme: document.getElementById('systemTheme').value,
            kitchen_theme: document.getElementById('kitchenTheme').value
        };

        saveSystemConfig(configs, '主题设置').then(() => {
            // 主题设置保存成功后，应用新主题
            applyTheme(configs.system_theme);
        });
    });

    // 保存系统配置
    function saveSystemConfig(configs, settingName) {
        return fetch('/api/system-config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(configs)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(`${settingName}保存成功！`);
                return data;
            } else {
                showError(`${settingName}保存失败: ` + (data.message || '未知错误'));
                throw new Error(data.message || '保存失败');
            }
        })
        .catch(error => {
            console.error('保存设置失败:', error);
            showError(`${settingName}保存失败，请重试`);
            throw error;
        });
    }

    // 应用主题
    function applyTheme(theme) {
        const body = document.body;
        const html = document.documentElement;

        // 移除现有主题类
        body.classList.remove('theme-light', 'theme-dark');
        html.classList.remove('theme-light', 'theme-dark');

        if (theme === 'dark') {
            body.classList.add('theme-dark');
            html.classList.add('theme-dark');
        } else if (theme === 'light') {
            body.classList.add('theme-light');
            html.classList.add('theme-light');
        } else if (theme === 'auto') {
            // 跟随系统主题
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            if (prefersDark) {
                body.classList.add('theme-dark');
                html.classList.add('theme-dark');
            } else {
                body.classList.add('theme-light');
                html.classList.add('theme-light');
            }
        }

        // 保存主题到localStorage
        localStorage.setItem('system_theme', theme);

        console.log('主题已应用:', theme);
    }

    // 页面加载时应用保存的主题
    document.addEventListener('DOMContentLoaded', function() {
        const savedTheme = localStorage.getItem('system_theme') || 'light';
        applyTheme(savedTheme);

        // 监听系统主题变化（当设置为auto时）
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
            const currentTheme = localStorage.getItem('system_theme');
            if (currentTheme === 'auto') {
                applyTheme('auto');
            }
        });
    });







    // 显示系统初始化模态框
    function showSystemResetModal() {
        console.log('🔍 显示系统初始化模态框');

        const modalElement = document.getElementById('systemResetModal');
        if (!modalElement) {
            console.error('❌ 找不到systemResetModal元素');
            return;
        }

        const modal = new bootstrap.Modal(modalElement);
        modal.show();

        console.log('✅ 模态框已显示');

        // 重置表单
        document.getElementById('resetConfirmText').value = '';
        document.getElementById('resetConfirmCheck').checked = false;
        document.getElementById('confirmResetBtn').disabled = true;

        // 添加取消按钮的事件监听器
        const cancelBtn = modalElement.querySelector('[data-bs-dismiss="modal"]');
        if (cancelBtn) {
            console.log('✅ 找到取消按钮，添加调试监听器');
            cancelBtn.addEventListener('click', function() {
                console.log('🔍 取消按钮被点击');
            });
        } else {
            console.error('❌ 找不到取消按钮');
        }

        // 监听输入变化
        const confirmText = document.getElementById('resetConfirmText');
        const confirmCheck = document.getElementById('resetConfirmCheck');
        const confirmBtn = document.getElementById('confirmResetBtn');

        function checkConfirmation() {
            const textValid = confirmText.value === 'RESET SYSTEM';
            const checkValid = confirmCheck.checked;
            confirmBtn.disabled = !(textValid && checkValid);
        }

        confirmText.addEventListener('input', checkConfirmation);
        confirmCheck.addEventListener('change', checkConfirmation);
    }

    // 关闭系统初始化模态框
    function closeSystemResetModal() {
        console.log('🔍 关闭系统初始化模态框');

        const modalElement = document.getElementById('systemResetModal');
        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
                console.log('✅ 模态框已关闭');
            } else {
                console.log('⚠️ 模态框实例不存在，尝试直接隐藏');
                modalElement.style.display = 'none';
                modalElement.classList.remove('show');
                document.body.classList.remove('modal-open');

                // 移除backdrop
                const backdrop = document.querySelector('.modal-backdrop');
                if (backdrop) {
                    backdrop.remove();
                }
            }
        } else {
            console.error('❌ 找不到systemResetModal元素');
        }
    }

    // 执行系统初始化
    function executeSystemReset() {
        const confirmText = document.getElementById('resetConfirmText').value;
        const confirmCheck = document.getElementById('resetConfirmCheck').checked;

        if (confirmText !== 'RESET SYSTEM' || !confirmCheck) {
            alert('请完成所有确认步骤');
            return;
        }

        if (!confirm('这是最后一次确认！确定要执行系统初始化吗？')) {
            return;
        }

        // 禁用按钮，显示加载状态
        const confirmBtn = document.getElementById('confirmResetBtn');
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 正在初始化...';

        fetch('/api/system-reset', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include'
        })
        .then(response => {
            console.log('🔍 系统初始化响应状态:', response.status);

            if (!response.ok) {
                return response.text().then(text => {
                    console.error('❌ 系统初始化HTTP错误:', text);
                    throw new Error(`HTTP ${response.status}: ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                alert('系统初始化成功！页面将自动跳转到登录页面。');
                // 清除所有本地存储
                localStorage.clear();
                sessionStorage.clear();
                // 跳转到登录页面
                window.location.href = '/login';
            } else {
                alert('系统初始化失败: ' + (data.message || '未知错误'));
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="bi bi-arrow-counterclockwise"></i> 确认初始化';
            }
        })
        .catch(error => {
            console.error('系统初始化失败:', error);
            alert('系统初始化失败，请重试');
            confirmBtn.disabled = false;
            confirmBtn.innerHTML = '<i class="bi bi-arrow-counterclockwise"></i> 确认初始化';
        });
    }
</script>

<!-- 系统初始化模态框 -->
<div class="modal fade" id="systemResetModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content border-danger">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle"></i> 系统初始化确认
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6><i class="bi bi-shield-exclamation"></i> 最后警告</h6>
                    <p class="mb-2">您即将执行系统初始化操作，此操作将：</p>
                    <ul class="mb-2">
                        <li><strong>删除所有包厢信息</strong></li>
                        <li><strong>删除所有订单和订单项</strong></li>
                        <li><strong>删除所有用户账号（除admin外）</strong></li>
                        <li><strong>删除所有指令模板</strong></li>
                        <li><strong>删除所有操作记录</strong></li>
                        <li><strong>重置所有自增ID序列</strong></li>
                    </ul>
                    <p class="mb-0 text-danger">
                        <strong>此操作不可逆！请确保您已经备份了重要数据！</strong>
                    </p>
                </div>

                <div class="mb-3">
                    <label for="resetConfirmText" class="form-label">
                        <strong>请输入确认文字：<span class="text-danger">RESET SYSTEM</span></strong>
                    </label>
                    <input type="text" class="form-control" id="resetConfirmText"
                           placeholder="请输入：RESET SYSTEM" autocomplete="off">
                    <div class="form-text text-muted">
                        必须完全匹配才能执行初始化操作
                    </div>
                </div>

                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="resetConfirmCheck">
                    <label class="form-check-label" for="resetConfirmCheck">
                        我已了解此操作的后果，并确认要执行系统初始化
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" onclick="closeSystemResetModal()">
                    <i class="bi bi-x-circle"></i> 取消
                </button>
                <button type="button" class="btn btn-danger" id="confirmResetBtn" onclick="executeSystemReset()" disabled>
                    <i class="bi bi-arrow-counterclockwise"></i> 确认初始化
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
