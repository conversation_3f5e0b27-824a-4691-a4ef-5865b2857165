<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>🚨 缓存测试页面 - 版本 2025-06-29 20:36 🚨</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
            border: 5px solid red;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 缓存测试页面 🚨</h1>
        <div class="info">
            <strong>版本:</strong> 2025-06-29 20:36<br>
            <strong>目的:</strong> 测试JavaScript是否能正常加载和执行<br>
            <strong>时间:</strong> <span id="currentTime"></span>
        </div>
        
        <div class="status success" id="jsStatus">
            ✅ JavaScript已成功加载！
        </div>
        
        <div>
            <button onclick="testNotificationAPI()">测试通知API</button>
            <button onclick="testConsoleLog()">测试控制台日志</button>
            <button onclick="clearLog()">清除日志</button>
        </div>
        
        <div id="log"></div>
    </div>

    <script>
        // 🚨🚨🚨 立即执行的JavaScript代码 🚨🚨🚨
        console.log('🚨🚨🚨 缓存测试页面JavaScript加载 - 版本 2025-06-29 20:36 🚨🚨🚨');
        console.log('📍 页面URL:', window.location.href);
        console.log('⏰ 加载时间:', new Date().toLocaleString());
        console.log('📄 页面标题:', document.title);
        
        // 立即显示弹窗
        alert('🚨 缓存测试页面JavaScript已加载 - 版本 2025-06-29 20:36 🚨');
        
        // 更新页面时间
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }
        
        // 日志函数
        function addLog(message) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
            console.log(message);
        }
        
        // 测试通知API
        function testNotificationAPI() {
            addLog('🔍 开始测试通知API...');
            fetch('/api/kitchen/dish-completion-notifications', {
                credentials: 'include'
            })
            .then(response => {
                addLog(`📡 API响应状态: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addLog(`📋 API返回数据: ${JSON.stringify(data, null, 2)}`);
                if (data.success && data.notifications) {
                    addLog(`✅ 成功获取 ${data.notifications.length} 条通知`);
                } else {
                    addLog('⚠️ 没有获取到通知数据');
                }
            })
            .catch(error => {
                addLog(`❌ API请求失败: ${error.message}`);
            });
        }
        
        // 测试控制台日志
        function testConsoleLog() {
            const testMessage = `🧪 控制台测试 - ${new Date().toLocaleString()}`;
            console.log(testMessage);
            addLog(`📝 已输出控制台日志: ${testMessage}`);
        }
        
        // 清除日志
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            addLog('🧹 日志已清除');
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            addLog('🚀 缓存测试页面初始化完成');
            addLog('🎯 JavaScript版本: 2025-06-29 20:36');
            addLog('📱 用户代理: ' + navigator.userAgent);
        });
        
        // 强制显示版本信息
        document.body.setAttribute('data-js-version', '2025-06-29-20:36');
        document.body.style.border = '5px solid red';
    </script>
</body>
</html>
