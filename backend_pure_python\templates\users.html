{% extends "base.html" %}

{% block title %}用户管理 - 暨阳湖大酒店传菜管理系统{% endblock %}

{% block extra_css %}
<style>
    /* 自定义厨房大屏用户角色徽章颜色 */
    .bg-purple {
        background-color: #6f42c1 !important;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-people"></i>
        用户管理
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-refresh btn-sm" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise"></i>
                刷新
            </button>
            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="bi bi-plus"></i>
                添加用户
            </button>
        </div>
    </div>
</div>

<!-- 用户列表 -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0">用户列表</h5>
    </div>
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>用户名</th>
                        <th>姓名</th>
                        <th>角色</th>
                        <th>部门</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user_item in users %}
                    <tr>
                        <td>{{ user_item.username }}</td>
                        <td>{{ user_item.full_name or '-' }}</td>
                        <td>
                            <span class="badge bg-{% if user_item.role.value == 'admin' %}danger{% elif user_item.role.value == 'manager' %}warning{% elif user_item.role.value == 'waiter' %}primary{% elif user_item.role.value == 'chef_manager' %}success{% elif user_item.role.value == 'kitchen_helper' %}info{% elif user_item.role.value == 'business_center' %}dark{% elif user_item.role.value == 'kitchen_display' %}purple{% else %}secondary{% endif %}">
                                {% if user_item.role.value == 'admin' %}系统管理员
                                {% elif user_item.role.value == 'manager' %}餐饮经理
                                {% elif user_item.role.value == 'waiter' %}服务员
                                {% elif user_item.role.value == 'chef_manager' %}厨师长
                                {% elif user_item.role.value == 'kitchen_helper' %}厨房打荷
                                {% elif user_item.role.value == 'business_center' %}商务中心
                                {% elif user_item.role.value == 'kitchen_display' %}厨房大屏用户
                                {% else %}{{ user_item.role.value }}
                                {% endif %}
                            </span>
                        </td>
                        <td>{{ user_item.department or '-' }}</td>
                        <td>
                            {% if user_item.is_active %}
                            <span class="badge bg-success">正常</span>
                            {% else %}
                            <span class="badge bg-danger">禁用</span>
                            {% endif %}
                        </td>
                        <td>{{ user_item.created_at.strftime('%Y-%m-%d') if user_item.created_at else '-' }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                {% set can_edit = user.role.value == 'admin' or (user.role.value == 'manager' and user_item.role.value != 'admin') %}
                                {% set can_delete = user_item.id != user.id and (user.role.value == 'admin' or (user.role.value == 'manager' and user_item.role.value != 'admin' and user_item.username != 'admin')) %}

                                {% if can_edit %}
                                <button type="button" class="btn btn-outline-primary"
                                        onclick="editUser({{ user_item.id }})">
                                    <i class="bi bi-pencil"></i>
                                    编辑
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-outline-secondary" disabled
                                        title="无权限编辑此用户">
                                    <i class="bi bi-pencil"></i>
                                    编辑
                                </button>
                                {% endif %}

                                {% if can_delete %}
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="deleteUser({{ user_item.id }})">
                                    <i class="bi bi-trash"></i>
                                    删除
                                </button>
                                {% elif user_item.role.value == 'admin' %}
                                <button type="button" class="btn btn-outline-secondary" disabled
                                        title="系统管理员账户受保护，无法删除">
                                    <i class="bi bi-shield"></i>
                                    保护
                                </button>
                                {% elif user_item.id == user.id %}
                                <button type="button" class="btn btn-outline-secondary" disabled
                                        title="无法删除自己">
                                    <i class="bi bi-x"></i>
                                    禁止
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-people text-muted" style="font-size: 4rem;"></i>
            <h4 class="text-muted mt-3">暂无用户</h4>
            <p class="text-muted">点击上方按钮添加用户</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">用户名 *</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="full_name" class="form-label">姓名 *</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="role" class="form-label">角色 *</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">请选择角色</option>
                            {% for role in manageable_roles %}
                                {% if role != 'admin' %}
                                    <option value="{{ role }}">
                                        {% if role == 'manager' %}餐饮经理
                                        {% elif role == 'waiter' %}服务员
                                        {% elif role == 'kitchen_helper' %}厨房打荷
                                        {% elif role == 'chef_manager' %}厨师长
                                        {% elif role == 'business_center' %}商务中心
                                        {% elif role == 'kitchen_display' %}厨房大屏用户
                                        {% else %}{{ role }}
                                        {% endif %}
                                    </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                        {% if user.role.value == 'manager' %}
                        <div class="form-text text-muted">
                            <i class="bi bi-info-circle"></i>
                            餐饮经理权限：可管理除系统管理员外的所有用户角色
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">密码 *</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3" id="table-assignment" style="display: none;">
                        <label for="assigned_tables" class="form-label">分配餐桌</label>
                        <input type="text" class="form-control" id="assigned_tables" name="assigned_tables"
                               placeholder="例如: A01,A02,A03">
                        <small class="text-muted">多个餐桌用逗号分隔</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary" id="addUserBtn">
                        <i class="bi bi-plus-circle"></i> 添加
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 编辑用户模态框 -->
<div class="modal fade" id="editUserModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <form id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" id="editUserId" name="user_id">
                    <div class="mb-3">
                        <label for="editUsername" class="form-label">用户名 *</label>
                        <input type="text" class="form-control" id="editUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="editFullName" class="form-label">姓名 *</label>
                        <input type="text" class="form-control" id="editFullName" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="editRole" class="form-label">角色 *</label>
                        <select class="form-select" id="editRole" name="role" required>
                            <option value="">请选择角色</option>
                            {% for role in manageable_roles %}
                                <option value="{{ role }}">
                                    {% if role == 'admin' %}系统管理员
                                    {% elif role == 'manager' %}餐饮经理
                                    {% elif role == 'waiter' %}服务员
                                    {% elif role == 'kitchen_helper' %}厨房打荷
                                    {% elif role == 'chef_manager' %}厨师长
                                    {% elif role == 'business_center' %}商务中心
                                    {% elif role == 'kitchen_display' %}厨房大屏用户
                                    {% else %}{{ role }}
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                        {% if user.role.value == 'manager' %}
                        <div class="form-text text-muted">
                            <i class="bi bi-exclamation-triangle"></i>
                            注意：无法编辑系统管理员账户
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="editPassword" class="form-label">新密码 (留空则不修改)</label>
                        <input type="password" class="form-control" id="editPassword" name="password" autocomplete="new-password">
                    </div>
                    <div class="mb-3">
                        <label for="editStatus" class="form-label">状态</label>
                        <select class="form-select" id="editStatus" name="status">
                            <option value="ACTIVE">激活</option>
                            <option value="INACTIVE">停用</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="editCancelBtn">
                        <i class="bi bi-x-circle"></i> 取消
                    </button>
                    <button type="submit" class="btn btn-primary" id="editSaveBtn">
                        <i class="bi bi-check-circle"></i> 保存修改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 授权服务员模态框 -->
<div class="modal fade" id="authorizeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">授权服务员</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">服务员姓名</label>
                    <input type="text" class="form-control" id="waiterName" readonly>
                </div>
                <div class="mb-3">
                    <label for="assignedTables" class="form-label">分配包厢 *</label>
                    <div class="row">
                        {% for table in tables %}
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input class="form-check-input table-checkbox" type="checkbox"
                                       value="{{ table.number }}" id="table_{{ table.id }}">
                                <label class="form-check-label" for="table_{{ table.id }}">
                                    {{ table.number }}
                                    {% if table.name %}({{ table.name }}){% endif %}
                                </label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <small class="text-muted">请选择该服务员负责的包厢</small>
                </div>
                <div class="mb-3">
                    <label for="selectedTables" class="form-label">已选包厢</label>
                    <input type="text" class="form-control" id="selectedTables" readonly
                           placeholder="将显示已选择的包厢">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="confirmAuthorize()">确认授权</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // 角色选择变化时显示/隐藏餐桌分配
    document.getElementById('role').addEventListener('change', function() {
        const tableAssignment = document.getElementById('table-assignment');
        if (this.value === 'waiter') {
            tableAssignment.style.display = 'block';
        } else {
            tableAssignment.style.display = 'none';
        }
    });
    
    let currentUserId = null;

    // 处理新建用户表单提交
    document.getElementById('addUserForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const addBtn = document.getElementById('addUserBtn');
        const originalText = addBtn.innerHTML;

        // 显示加载状态
        addBtn.disabled = true;
        addBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 创建中...';

        const formData = new FormData(this);

        fetch('/users/create', {
            method: 'POST',
            credentials: 'include',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message || '用户创建成功');

                // 关闭模态框
                const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                if (modal) {
                    modal.hide();
                }

                // 重置表单
                this.reset();

                // 刷新页面数据（可选择性刷新）
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showError(data.message || '创建用户失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('创建用户失败，请重试');
        })
        .finally(() => {
            // 恢复按钮状态
            addBtn.disabled = false;
            addBtn.innerHTML = originalText;
        });
    });

    // 显示授权模态框
    function showAuthorizeModal(userId, userName) {
        currentUserId = userId;
        document.getElementById('waiterName').value = userName;

        // 清空之前的选择
        document.querySelectorAll('.table-checkbox').forEach(cb => cb.checked = false);
        document.getElementById('selectedTables').value = '';

        new bootstrap.Modal(document.getElementById('authorizeModal')).show();
    }

    // 确认授权
    function confirmAuthorize() {
        const selectedTables = [];
        document.querySelectorAll('.table-checkbox:checked').forEach(cb => {
            selectedTables.push(cb.value);
        });

        if (selectedTables.length === 0) {
            alert('请至少选择一个包厢');
            return;
        }

        fetch('/users/' + currentUserId + '/authorize', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                assigned_tables: selectedTables.join(',')
            })
        }).then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message || '授权成功');
                bootstrap.Modal.getInstance(document.getElementById('authorizeModal')).hide();

                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showError(data.message || '授权失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('授权失败，请重试');
        });
    }

    // 取消授权
    function revokeUser(userId) {
        if (confirm('确定要取消授权这个服务员吗？')) {
            fetch('/users/' + userId + '/revoke', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message || '取消授权成功');
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    showError(data.message || '取消授权失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('取消授权失败，请重试');
            });
        }
    }

    // 监听包厢选择变化
    document.querySelectorAll('.table-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const selectedTables = [];
            document.querySelectorAll('.table-checkbox:checked').forEach(cb => {
                selectedTables.push(cb.value);
            });
            document.getElementById('selectedTables').value = selectedTables.join(', ');
        });
    });
    
    function editUser(userId) {
        // 获取用户信息
        fetch('/api/users/' + userId, {
            credentials: 'include'
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const user = data.user;
                    const currentUserRole = '{{ user.role.value }}';

                    // 权限检查：餐饮经理无法编辑系统管理员账户
                    if (currentUserRole === 'manager' && user.role === 'admin') {
                        showError('餐饮经理无权编辑系统管理员账户');
                        return;
                    }

                    // 填充表单
                    document.getElementById('editUserId').value = user.id;
                    document.getElementById('editUsername').value = user.username || '';
                    document.getElementById('editFullName').value = user.full_name || '';
                    document.getElementById('editRole').value = user.role || '';
                    document.getElementById('editStatus').value = user.status || 'ACTIVE';
                    document.getElementById('editPassword').value = '';

                    // 如果是系统管理员账户，禁用某些字段
                    const isAdminUser = user.role === 'admin';
                    const isCurrentUserManager = currentUserRole === 'manager';

                    if (isAdminUser && isCurrentUserManager) {
                        document.getElementById('editRole').disabled = true;
                        document.getElementById('editStatus').disabled = true;
                        document.getElementById('editUsername').disabled = true;
                    } else {
                        document.getElementById('editRole').disabled = false;
                        document.getElementById('editStatus').disabled = false;
                        document.getElementById('editUsername').disabled = false;
                    }

                    // 显示模态框
                    const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
                    modal.show();
                } else {
                    showError('获取用户信息失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('获取用户信息失败，请重试');
            });
    }

    // 处理编辑用户表单提交
    document.getElementById('editUserForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const saveBtn = document.getElementById('editSaveBtn');
        const originalText = saveBtn.innerHTML;

        // 显示加载状态
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';

        const formData = new FormData(this);
        const userId = formData.get('user_id');

        const updateData = {
            username: formData.get('username'),
            full_name: formData.get('full_name'),
            role: formData.get('role'),
            status: formData.get('status')
        };

        // 如果输入了新密码，则包含密码字段
        const password = formData.get('password');
        if (password && password.trim()) {
            updateData.password = password;
        }

        fetch('/users/' + userId + '/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(updateData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message || '用户更新成功');

                const modal = bootstrap.Modal.getInstance(document.getElementById('editUserModal'));
                if (modal) {
                    modal.hide();
                }

                // 延迟刷新页面数据
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showError(data.message || '更新失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('更新失败，请重试');
        })
        .finally(() => {
            // 恢复按钮状态
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        });
    });
    
    function deleteUser(userId) {
        // 先获取用户信息进行权限检查
        fetch('/api/users/' + userId, {
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const user = data.user;
                const currentUserRole = '{{ user.role.value }}';

                // 权限检查：餐饮经理无法删除系统管理员账户
                if (currentUserRole === 'manager' && user.role === 'admin') {
                    showError('餐饮经理无权删除系统管理员账户');
                    return;
                }

                // 系统管理员账户受保护
                if (user.role === 'admin') {
                    showError('系统管理员账户受保护，无法删除');
                    return;
                }

                // 确认删除
                if (confirm(`确定要删除用户 "${user.full_name}" (${user.username}) 吗？`)) {
                    fetch('/users/' + userId + '/delete', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        credentials: 'include'
                    }).then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            showSuccess(data.message || '删除成功');
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        } else {
                            showError(data.message || '删除失败');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showError('删除失败，请重试');
                    });
                }
            } else {
                showError('获取用户信息失败: ' + (data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('获取用户信息失败，请重试');
        });
    }
</script>
{% endblock %}
