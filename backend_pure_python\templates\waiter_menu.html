<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>服务员操作 - 暨阳湖大酒店</title>
    <link href="/static/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/css/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/jiyang-enhanced.css" rel="stylesheet">
    <link href="/static/css/waiter-mobile.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-size: 16px;
        }
        .mobile-header {
            background: linear-gradient(135deg, var(--jiyang-primary), var(--jiyang-secondary));
            color: white;
            padding: 1rem;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .action-btn {
            height: 80px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 15px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }
        .action-btn i {
            font-size: 24px;
            margin-right: 10px;
        }
        .menu-container {
            padding: 20px 15px;
        }
        .dish-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border-left: 4px solid #007bff;
        }
        .dish-status {
            font-size: 14px;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }
        .status-ready {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-cooking {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status-served {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .dish-actions {
            margin-top: 10px;
        }
        .dish-btn {
            padding: 8px 15px;
            margin: 3px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
        }
        .room-badge {
            background: linear-gradient(45deg, var(--jiyang-primary), var(--jiyang-secondary));
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 14px;
        }

        /* 浏览器风格标签页样式 */
        .browser-tabs {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1030;
            background: #f1f3f4;
            padding: 0;
            border: none;
            margin: 0;
            overflow-x: auto;
            overflow-y: hidden;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: 50px; /* 固定高度 */
        }

        .browser-tabs::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: #dadce0;
            z-index: 1;
        }

        .browser-tabs .nav-item {
            display: inline-block;
            margin: 0;
        }

        .browser-tabs .nav-link {
            position: relative;
            display: inline-flex;
            align-items: center;
            min-width: 120px;
            max-width: 200px;
            height: 36px;
            padding: 0 16px;
            margin: 8px 0 0 0;
            background: #e8eaed;
            color: #5f6368;
            border: none;
            border-radius: 8px 8px 0 0;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
            z-index: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 梯形效果 */
        .browser-tabs .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -8px;
            width: calc(100% + 16px);
            height: 100%;
            background: inherit;
            border-radius: 8px 8px 0 0;
            transform: perspective(12px) rotateX(2deg);
            z-index: -1;
        }

        .browser-tabs .nav-link:hover {
            background: #f1f3f4;
            color: #3c4043;
            transform: translateY(-1px);
        }

        .browser-tabs .nav-link:hover::before {
            background: #f1f3f4;
        }

        .browser-tabs .nav-link.active {
            background: white;
            color: var(--jiyang-primary);
            font-weight: 600;
            z-index: 3;
            margin-top: 0;
            height: 44px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .browser-tabs .nav-link.active::before {
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .browser-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--jiyang-primary);
            z-index: 4;
        }

        .browser-tabs .nav-link i {
            margin-right: 6px;
            font-size: 16px;
            flex-shrink: 0;
        }

        /* 标签页内容区域 */
        .browser-tab-content {
            background: white;
            border: 1px solid #dadce0;
            border-top: none;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
            margin-top: 60px; /* 为固定的标签页导航栏留出空间 */
            min-height: auto; /* 确保内容区域高度自适应 */
        }

        /* 标签页面板样式 */
        .browser-tab-content .tab-pane {
            display: none; /* 默认隐藏所有面板 */
            padding: 0; /* 移除默认padding，让内容区域自己控制间距 */
        }

        /* 激活的标签页面板 */
        .browser-tab-content .tab-pane.active {
            display: block; /* 只显示激活的面板 */
        }

        /* 确保内容从顶部开始显示 */
        .browser-tab-content .tab-pane.show.active {
            animation: fadeIn 0.2s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 菜品清单区域样式 */
        .dish-list-section {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
        }

        /* 标签页分隔线效果 */
        .browser-tabs .nav-link:not(.active)::after {
            content: '';
            position: absolute;
            top: 8px;
            right: 0;
            width: 1px;
            height: 20px;
            background: #dadce0;
            opacity: 0.6;
        }

        .browser-tabs .nav-link:not(.active):last-child::after,
        .browser-tabs .nav-link:not(.active) + .nav-item .nav-link.active::after,
        .browser-tabs .nav-link.active + .nav-item .nav-link:not(.active)::after {
            display: none;
        }

        /* 包厢信息区域样式 */
        .room-info-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 2px solid #dee2e6;
            padding: 15px 20px; /* 减少垂直padding */
        }

        .room-info-title {
            color: var(--jiyang-primary);
            font-weight: 700;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .room-info-title i {
            margin-right: 10px;
            font-size: 20px;
        }

        /* 用餐控制区域样式 */
        .dining-control-section {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-bottom: 2px solid #ffc107;
            padding: 15px 20px; /* 减少垂直padding */
        }

        .dining-control-title {
            color: #856404;
            font-weight: 700;
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .dining-control-title i {
            margin-right: 10px;
            font-size: 18px;
        }

        /* 指令操作区域样式 */
        .command-section {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            border-bottom: 2px solid #17a2b8;
            padding: 15px 20px; /* 减少垂直padding */
        }

        .command-title {
            color: #0c5460;
            font-weight: 700;
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .command-title i {
            margin-right: 10px;
            font-size: 18px;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--jiyang-primary);
            color: white;
            border: none;
            font-size: 24px;
            box-shadow: 0 4px 15px rgba(242, 117, 10, 0.3);
            z-index: 1000;
        }

        .dish-progress {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            text-align: center;
            min-width: 120px;
            z-index: 1000;
        }
        .room-section {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background: #f8f9fa;
        }

        .room-header {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #dee2e6;
        }

        .dish-info {
            flex: 1;
        }

        .dish-name {
            font-weight: 600;
            font-size: 1.1rem;
            color: #212529;
            margin-bottom: 4px;
        }

        .dish-details {
            margin-bottom: 8px;
        }

        .dish-status {
            margin-bottom: 0;
            margin-top: 8px;
        }

        .dish-status .badge {
            font-size: 0.75rem;
            padding: 4px 8px;
            margin-right: 6px;
            border-radius: 4px;
        }

        /* 菜品状态颜色优化 */
        .badge.bg-secondary {
            background-color: #6c757d !important;
        }

        .badge.bg-warning {
            background-color: #fd7e14 !important;
        }

        .badge.bg-success {
            background-color: #198754 !important;
        }

        .badge.bg-primary {
            background-color: #0d6efd !important;
        }

        .dish-served {
            background-color: #d4edda !important;
            border-color: #c3e6cb !important;
        }

        .dish-returned {
            background-color: #f8d7da !important;
            border-color: #f5c6cb !important;
        }

        /* 菜品卡片样式 */
        .dish-card {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .dish-card:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }

        .room-actions {
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
        }

        .progress {
            height: 8px;
        }

        /* 浏览器标签页动画效果 */
        @keyframes tabActivate {
            from {
                transform: translateY(2px);
                opacity: 0.8;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .browser-tabs .nav-link.active {
            animation: tabActivate 0.2s ease-out;
        }

        /* 标签页文本样式 */
        .tab-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .action-btn {
                height: 70px;
                font-size: 16px;
            }
            .dish-card {
                padding: 12px;
            }
            .dish-actions {
                flex-direction: column;
                gap: 4px;
                min-width: 100px;
            }

            /* 移动端浏览器标签页优化 */
            .browser-tabs {
                padding: 0 8px;
                height: 45px; /* 移动端稍微降低高度 */
            }

            /* 移动端内容区域顶部边距调整 */
            .browser-tab-content {
                margin-top: 55px; /* 移动端调整边距 */
            }

            .browser-tabs .nav-link {
                min-width: 100px;
                max-width: 140px;
                height: 32px;
                padding: 0 12px;
                font-size: 13px;
                margin-top: 6px;
            }

            .browser-tabs .nav-link.active {
                height: 38px;
                margin-top: 0;
            }

            .browser-tabs .nav-link i {
                font-size: 14px;
                margin-right: 4px;
            }

            .room-info-section,
            .dining-control-section,
            .command-section {
                padding: 15px;
            }

            .room-info-title,
            .dining-control-title,
            .command-title {
                font-size: 15px;
            }

            .room-actions .btn {
                font-size: 14px;
                padding: 8px 16px;
            }

            .room-header {
                padding: 15px !important;
            }

            .room-badge {
                font-size: 13px;
                padding: 4px 10px;
            }
        }

        @media (max-width: 576px) {
            /* 超小屏幕浏览器标签页优化 */
            .browser-tabs {
                padding: 0 4px;
                scrollbar-width: none; /* Firefox */
                -ms-overflow-style: none; /* IE/Edge */
                height: 40px; /* 超小屏幕进一步降低高度 */
            }

            /* 超小屏幕内容区域顶部边距调整 */
            .browser-tab-content {
                margin-top: 50px; /* 超小屏幕调整边距 */
            }

            .browser-tabs::-webkit-scrollbar {
                display: none; /* Chrome/Safari */
            }

            .browser-tabs .nav-link {
                min-width: 80px;
                max-width: 120px;
                height: 30px;
                padding: 0 8px;
                font-size: 12px;
                margin-top: 4px;
            }

            .browser-tabs .nav-link.active {
                height: 34px;
                margin-top: 0;
            }

            .browser-tabs .nav-link i {
                font-size: 12px;
                margin-right: 3px;
            }

            .tab-text {
                font-size: 11px;
            }

            .room-actions {
                flex-direction: column;
                gap: 10px;
            }

            .room-actions .d-flex {
                flex-direction: column;
                align-items: stretch !important;
                gap: 15px;
            }

            .progress {
                width: 100% !important;
            }

            /* 标签页内容区域移动端优化 */
            .browser-tab-content {
                border-radius: 0 0 4px 4px;
            }

            .room-info-section,
            .dining-control-section,
            .command-section {
                padding: 12px;
            }
        }

        /* 标签页切换平滑过渡 */
        .tab-pane {
            transition: opacity 0.3s ease-in-out;
        }

        .tab-pane:not(.show) {
            opacity: 0;
        }

        .tab-pane.show {
            opacity: 1;
        }

        /* 标签页悬停效果增强 */
        .browser-tabs .nav-link:hover:not(.active) {
            background: #f8f9fa;
            transform: translateY(-1px);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }

        .browser-tabs .nav-link:hover:not(.active)::before {
            background: #f8f9fa;
        }

        /* 激活标签页的特殊效果 */
        .browser-tabs .nav-link.active {
            position: relative;
            overflow: visible;
        }

        .browser-tabs .nav-link.active::before {
            box-shadow:
                0 2px 8px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
        }
    </style>
</head>
<body>
    <!-- 移动端头部 -->
    <div class="mobile-header">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-1">
                    {{ user.full_name }}
                </h4>
                {% if user.assigned_tables %}
                <small>负责包厢: {{ user.assigned_tables }}</small>
                {% else %}
                <small class="text-warning">未分配包厢</small>
                {% endif %}
            </div>
            <div>
                <a href="/logout" class="btn btn-logout btn-sm">
                    <i class="bi bi-box-arrow-right"></i>
                    退出
                </a>
            </div>
        </div>
    </div>

    <!-- 指令按钮区域 -->
    <div class="menu-container">
        <div class="row g-3 mb-4" id="commandButtons">
            <!-- 动态加载指令按钮 -->
        </div>

        <!-- 包厢信息和菜品清单 -->
        {% if assigned_room_orders %}

        {% set room_count = assigned_room_orders|length %}

        {% if room_count == 1 %}
        <!-- 单包厢模式：直接显示包厢内容 -->
        {% for room_number, order_items in assigned_room_orders.items() %}
        {% set room_data = room_info.get(room_number, {}) %}

        <!-- 包厢基本信息 -->
        <div class="card mb-4 border-primary">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-house-door"></i>
                    {{ room_number }}包厢信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <strong>用餐标准:</strong> {{ room_data.dining_standard or 0 }}元/桌
                    </div>
                    <div class="col-md-4">
                        <strong>预定人数:</strong> {{ room_data.guest_count or 0 }}人
                    </div>
                    <div class="col-md-4">
                        <strong>下单时间:</strong> {{ room_data.order_time or '-' }}
                    </div>
                </div>
                {% if room_data.special_requirements %}
                <div class="mt-2">
                    <strong>特殊要求:</strong>
                    <span class="text-warning">{{ room_data.special_requirements }}</span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 用餐开始指令区（置顶） -->
        <div class="card mb-3 border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="bi bi-play-circle"></i>
                    {{ room_number }} 用餐控制
                </h6>
            </div>
            <div class="card-body">
                <div id="diningControl-{{ room_number }}" class="text-center">
                    <!-- 动态加载用餐控制按钮 -->
                </div>
            </div>
        </div>

        <!-- 单包厢指令操作区 -->
        <div class="card mb-4 border-success">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="bi bi-gear"></i>
                    {{ room_number }} 指令操作
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-2" id="commandButtons-{{ room_number }}">
                    <!-- 动态加载指令按钮 -->
                </div>
                <div id="commandStatus-{{ room_number }}" class="mt-2 text-center text-muted" style="display: none;">
                    <small><i class="bi bi-info-circle"></i> 请先点击"开始用餐"按钮</small>
                </div>
            </div>
        </div>

        <!-- 单包厢菜品清单 -->
        <div class="card mb-4 border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-list-check"></i>
                    {{ room_number }}包厢菜品清单
                </h6>
            </div>
            <div class="card-body">
                <div class="dish-list-container">
                    {% for item in order_items %}
                    <div class="dish-card {% if item.waiter_status == 'served' %}dish-served{% elif item.waiter_status == 'returned' %}dish-returned{% endif %}"
                         data-room="{{ room_number }}" data-dish-id="{{ item.id }}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="dish-info">
                                <div class="dish-name">{{ item.dish_name }}</div>
                                <div class="dish-details">
                                    {% if item.special_requirements %}
                                    <small class="text-warning">备注: {{ item.special_requirements }}</small>
                                    {% endif %}
                                </div>
                                <div class="dish-status">
                                    {% if item.status.value == 'pending_cook' %}
                                    <span class="badge bg-secondary">待制作</span>
                                    {% elif item.status.value == 'cooking' %}
                                    <span class="badge bg-info">制作中</span>
                                    {% elif item.status.value == 'ready' %}
                                    <span class="badge bg-warning">制作完成</span>
                                    {% elif item.status.value == 'served' %}
                                    <span class="badge bg-success">厨房已出菜</span>
                                    {% endif %}

                                    {% if item.waiter_status == 'served' %}
                                    <span class="badge bg-success ms-1">已划菜</span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="dish-actions">
                                {% if item.status.value == 'served' and not item.waiter_confirmed %}
                                <button type="button" class="btn btn-success btn-sm me-1"
                                        onclick="confirmDishServed({{ item.id }}, '{{ room_number }}')" title="确认已上菜">
                                    确认上菜
                                </button>
                                {% elif item.status.value in ['ready'] and not item.waiter_status %}
                                <button type="button" class="btn btn-warning btn-sm me-1"
                                        onclick="markDish({{ item.id }}, 'served', '{{ room_number }}')" title="划菜">
                                    划菜
                                </button>
                                {% elif item.waiter_confirmed %}
                                <span class="badge bg-success">已确认上菜</span>
                                {% elif item.waiter_status == 'served' %}
                                <span class="badge bg-info">已划菜</span>
                                {% else %}
                                <small class="text-muted">等待制作完成</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- 单包厢操作按钮区域 -->
                <div class="room-actions mt-4 p-3 bg-light border-top rounded">
                    {% set room_items = order_items %}
                    {% set total_items = room_items|length %}
                    {% set unprocessed_items = room_items|selectattr('status.value', 'in', ['pending_cook', 'cooking', 'ready'])|list %}
                    {% set unprocessed_count = unprocessed_items|length %}
                    {% set processed_count = total_items - unprocessed_count %}

                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if total_items > 0 and unprocessed_count == 0 %}
                            <span class="text-success fw-bold">
                                <i class="bi bi-check-circle-fill"></i>
                                {{ room_number }}包厢所有菜品已处理完毕
                            </span>
                            {% else %}
                            <div class="progress" style="width: 200px; height: 8px;">
                                <div class="progress-bar bg-success" role="progressbar"
                                     style="width: {{ (processed_count / total_items * 100) if total_items > 0 else 0 }}%">
                                </div>
                            </div>
                            <small class="text-muted mt-1 d-block">
                                {{ room_number }}包厢还有{{ unprocessed_count }}道菜未处理 ({{ processed_count }}/{{ total_items }})
                            </small>
                            {% endif %}
                        </div>

                        <div>
                            {% if total_items > 0 and unprocessed_count == 0 %}
                            <button type="button" class="btn btn-success btn-lg"
                                    onclick="finishRoomDining('{{ room_number }}')">
                                <i class="bi bi-check-circle"></i>
                                {{ room_number }}包厢用餐结束
                            </button>
                            {% else %}
                            <button type="button" class="btn btn-outline-secondary" disabled>
                                <i class="bi bi-clock"></i>
                                等待菜品处理完毕
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}

        {% else %}
        <!-- 多包厢模式：使用浏览器风格标签页布局 -->
        <div class="mb-4">
            <!-- 浏览器风格标签页导航 -->
            <ul class="nav browser-tabs" id="roomTabs" role="tablist">
                {% for room_number in assigned_room_orders.keys() %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link {% if loop.first %}active{% endif %}"
                            id="tab-{{ loop.index }}"
                            data-bs-toggle="tab"
                            data-bs-target="#room-{{ loop.index }}"
                            type="button"
                            role="tab"
                            aria-controls="room-{{ loop.index }}"
                            aria-selected="{% if loop.first %}true{% else %}false{% endif %}"
                            title="{{ room_number }}包厢">
                        <i class="bi bi-house-door-fill"></i>
                        <span class="tab-text">{{ room_number }}</span>
                    </button>
                </li>
                {% endfor %}
            </ul>

            <!-- 浏览器风格标签页内容 -->
            <div class="browser-tab-content" id="roomTabContent">
                {% for room_number, order_items in assigned_room_orders.items() %}
                {% set room_data = room_info.get(room_number, {}) %}
                <div class="tab-pane fade {% if loop.first %}show active{% endif %}"
                     id="room-{{ loop.index }}"
                     role="tabpanel"
                     aria-labelledby="tab-{{ loop.index }}">

                    <!-- 包厢信息区域 -->
                    <div class="room-info-section">
                        <div class="room-info-title">
                            <i class="bi bi-info-circle-fill"></i>
                            {{ room_number }}包厢信息
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <strong>用餐标准:</strong> {{ room_data.dining_standard or 0 }}元/桌
                            </div>
                            <div class="col-md-4">
                                <strong>预定人数:</strong> {{ room_data.guest_count or 0 }}人
                            </div>
                            <div class="col-md-4">
                                <strong>下单时间:</strong> {{ room_data.order_time or '-' }}
                            </div>
                        </div>
                        {% if room_data.special_requirements %}
                        <div class="mt-3">
                            <strong>特殊要求:</strong>
                            <span class="text-warning fw-bold">{{ room_data.special_requirements }}</span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- 用餐控制区域 -->
                    <div class="dining-control-section">
                        <div class="dining-control-title">
                            <i class="bi bi-play-circle-fill"></i>
                            {{ room_number }}包厢用餐控制
                        </div>
                        <div id="diningControl-{{ room_number }}" class="text-center">
                            <!-- 动态加载用餐控制按钮 -->
                        </div>
                    </div>

                    <!-- 指令操作区域 -->
                    <div class="command-section">
                        <div class="command-title">
                            <i class="bi bi-gear-fill"></i>
                            {{ room_number }}包厢指令操作
                        </div>
                        <div class="row g-2" id="commandButtons-{{ room_number }}">
                            <!-- 动态加载指令按钮 -->
                        </div>
                        <div id="commandStatus-{{ room_number }}" class="mt-3 text-center text-muted" style="display: none;">
                            <small><i class="bi bi-info-circle"></i> 请先点击"开始用餐"按钮</small>
                        </div>
                    </div>

                    <!-- 当前标签页的菜品清单 -->
                    <div class="dish-list-section">
                        <div class="command-title">
                            <i class="bi bi-list-check"></i>
                            {{ room_number }}包厢菜品清单
                        </div>

                        <div class="dish-list-container mt-3">
                            {% for item in order_items %}
                            <div class="dish-card {% if item.waiter_status == 'served' %}dish-served{% elif item.waiter_status == 'returned' %}dish-returned{% endif %}"
                                 data-room="{{ room_number }}" data-dish-id="{{ item.id }}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="dish-info">
                                        <div class="dish-name">{{ item.dish_name }}</div>
                                        <div class="dish-details">
                                            {% if item.special_requirements %}
                                            <small class="text-warning">备注: {{ item.special_requirements }}</small>
                                            {% endif %}
                                        </div>
                                        <div class="dish-status">
                                            {% if item.status.value == 'pending_cook' %}
                                            <span class="badge bg-secondary">待制作</span>
                                            {% elif item.status.value == 'cooking' %}
                                            <span class="badge bg-info">制作中</span>
                                            {% elif item.status.value == 'ready' %}
                                            <span class="badge bg-warning">制作完成</span>
                                            {% elif item.status.value == 'served' %}
                                            <span class="badge bg-success">厨房已出菜</span>
                                            {% endif %}

                                            {% if item.waiter_status == 'served' %}
                                            <span class="badge bg-success ms-1">已划菜</span>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="dish-actions">
                                        {% if item.status.value == 'served' and not item.waiter_confirmed %}
                                        <button type="button" class="btn btn-success btn-sm me-1"
                                                onclick="confirmDishServed({{ item.id }}, '{{ room_number }}')" title="确认已上菜">
                                            确认上菜
                                        </button>
                                        {% elif item.status.value in ['ready'] and not item.waiter_status %}
                                        <button type="button" class="btn btn-warning btn-sm me-1"
                                                onclick="markDish({{ item.id }}, 'served', '{{ room_number }}')" title="划菜">
                                            划菜
                                        </button>
                                        {% elif item.waiter_confirmed %}
                                        <span class="badge bg-success">已确认上菜</span>
                                        {% elif item.waiter_status == 'served' %}
                                        <span class="badge bg-info">已划菜</span>
                                        {% else %}
                                        <small class="text-muted">等待制作完成</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- 当前包厢的操作按钮区域 -->
                        <div class="room-actions mt-4 p-3 bg-light border-top rounded">
                            {% set room_items = order_items %}
                            {% set total_items = room_items|length %}
                            {% set unprocessed_items = room_items|selectattr('status.value', 'in', ['pending_cook', 'cooking', 'ready'])|list %}
                            {% set unprocessed_count = unprocessed_items|length %}
                            {% set processed_count = total_items - unprocessed_count %}

                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% if total_items > 0 and unprocessed_count == 0 %}
                                    <span class="text-success fw-bold">
                                        <i class="bi bi-check-circle-fill"></i>
                                        {{ room_number }}包厢所有菜品已处理完毕
                                    </span>
                                    {% else %}
                                    <div class="progress" style="width: 200px; height: 8px;">
                                        <div class="progress-bar bg-success" role="progressbar"
                                             style="width: {{ (processed_count / total_items * 100) if total_items > 0 else 0 }}%">
                                        </div>
                                    </div>
                                    <small class="text-muted mt-1 d-block">
                                        {{ room_number }}包厢还有{{ unprocessed_count }}道菜未处理 ({{ processed_count }}/{{ total_items }})
                                    </small>
                                    {% endif %}
                                </div>

                                <div>
                                    {% if total_items > 0 and unprocessed_count == 0 %}
                                    <button type="button" class="btn btn-success btn-lg"
                                            onclick="finishRoomDining('{{ room_number }}')">
                                        <i class="bi bi-check-circle"></i>
                                        {{ room_number }}包厢用餐结束
                                    </button>
                                    {% else %}
                                    <button type="button" class="btn btn-outline-secondary" disabled>
                                        <i class="bi bi-clock"></i>
                                        等待菜品处理完毕
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}



        {% else %}
        <div class="alert alert-info">
            <h6><i class="bi bi-info-circle"></i> 暂无分配的包厢</h6>
            <p class="mb-0">请联系餐饮经理为您分配包厢。</p>
        </div>
        {% endif %}

        <hr class="my-4">

        <!-- 操作说明 -->
        <div class="alert alert-secondary">
            <h6><i class="bi bi-info-circle"></i> 操作说明</h6>
            <ul class="mb-0">
                <li>制作完成的菜品可以进行"划菜"操作</li>
                <li>划菜：确认菜品已送达客人</li>
                <li>所有菜品处理完毕后可以结束用餐</li>
                <li>下方按钮用于发送指令到厨房</li>
                <li>如需退菜，请联系厨房打荷人员操作</li>
            </ul>
        </div>
    </div>

    <!-- 菜品进度显示 - 左下角 -->
    <div class="dish-progress" id="dishProgress">
        <div class="progress-text">菜品进度：0/0</div>
        <div class="remaining-text">还有0道菜未处理</div>
    </div>

    <!-- 刷新按钮 - 右下角 -->
    <button type="button" class="refresh-btn" onclick="location.reload()" title="刷新页面">
        刷新
    </button>

    <script src="/static/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let previousReadyDishes = new Set();
        let autoRefreshInterval;
        let isPageVisible = true;

        // 页面加载时获取指令模板
        document.addEventListener('DOMContentLoaded', function() {
            loadDiningControls();
            loadCommandTemplates();
            startAutoRefresh();
            initPageVisibility();
            initializeButtonStates();
            updateDishProgress();
            startAuthStatusCheck();  // 启动权限状态检查
            startDishReadyListener(); // 启动菜品完成监听
            initVisibilityChangeHandler(); // 初始化页面可见性处理
            initBrowserTabs(); // 初始化浏览器风格标签页
        });

        // 初始化浏览器风格标签页
        function initBrowserTabs() {
            const tabLinks = document.querySelectorAll('.browser-tabs .nav-link');
            const tabPanes = document.querySelectorAll('.browser-tab-content .tab-pane');

            // 为每个标签页添加点击事件
            tabLinks.forEach((tab, index) => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();

                    // 移除所有标签页的激活状态
                    tabLinks.forEach(t => {
                        t.classList.remove('active');
                        t.setAttribute('aria-selected', 'false');
                    });

                    // 隐藏所有内容面板
                    tabPanes.forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });

                    // 激活当前标签页
                    this.classList.add('active');
                    this.setAttribute('aria-selected', 'true');

                    // 显示对应的内容面板
                    const targetId = this.getAttribute('data-bs-target');
                    const targetPane = document.querySelector(targetId);
                    if (targetPane) {
                        targetPane.classList.add('show', 'active');
                        console.log(`显示内容面板: ${targetId}`);

                        // 切换标签页后滚动到标签页导航栏位置
                        setTimeout(() => {
                            const tabContainer = document.querySelector('.browser-tabs');
                            if (tabContainer) {
                                // 滚动到标签页导航栏的位置，留出一些空间
                                const tabRect = tabContainer.getBoundingClientRect();
                                const scrollTop = window.pageYOffset + tabRect.top - 10;

                                window.scrollTo({
                                    top: scrollTop,
                                    behavior: 'smooth'
                                });
                            }
                        }, 100);
                    } else {
                        console.error(`找不到内容面板: ${targetId}`);
                    }

                    // 添加激活动画
                    this.style.animation = 'none';
                    setTimeout(() => {
                        this.style.animation = 'tabActivate 0.2s ease-out';
                    }, 10);

                    // 确保激活的标签页可见
                    this.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'center'
                    });

                    console.log(`切换到标签页: ${this.querySelector('.tab-text').textContent}`);
                });

                // 添加键盘导航支持
                tab.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }

                    // 左右箭头键切换标签页
                    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                        e.preventDefault();
                        const currentIndex = Array.from(tabLinks).indexOf(this);
                        let nextIndex;

                        if (e.key === 'ArrowLeft') {
                            nextIndex = currentIndex > 0 ? currentIndex - 1 : tabLinks.length - 1;
                        } else {
                            nextIndex = currentIndex < tabLinks.length - 1 ? currentIndex + 1 : 0;
                        }

                        tabLinks[nextIndex].focus();
                        tabLinks[nextIndex].click();
                    }
                });
            });

            // 确保标签页容器支持水平滚动
            const tabContainer = document.querySelector('.browser-tabs');
            if (tabContainer) {
                // 检查是否需要滚动
                if (tabContainer.scrollWidth > tabContainer.clientWidth) {
                    tabContainer.style.overflowX = 'auto';

                    // 确保激活的标签页可见
                    const activeTab = tabContainer.querySelector('.nav-link.active');
                    if (activeTab) {
                        activeTab.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'center'
                        });
                    }
                }
            }

            // 初始化时确保第一个标签页和内容面板是激活的
            if (tabLinks.length > 0 && tabPanes.length > 0) {
                // 确保第一个标签页是激活的
                tabLinks[0].classList.add('active');
                tabLinks[0].setAttribute('aria-selected', 'true');

                // 确保第一个内容面板是激活的
                tabPanes[0].classList.add('show', 'active');

                console.log(`初始化标签页: ${tabLinks[0].querySelector('.tab-text').textContent}`);
            }
        }

        // 切换到指定标签页
        function switchToTab(tabIndex) {
            const tabLinks = document.querySelectorAll('.browser-tabs .nav-link');
            if (tabIndex >= 0 && tabIndex < tabLinks.length) {
                // 触发点击事件，这会自动处理标签页和内容面板的切换
                tabLinks[tabIndex].click();

                // 确保标签页滚动到可见位置
                tabLinks[tabIndex].scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest',
                    inline: 'center'
                });
            }
        }

        // 根据包厢号切换到对应标签页
        function switchToRoomTab(roomNumber) {
            const tabLinks = document.querySelectorAll('.browser-tabs .nav-link');
            tabLinks.forEach((tab, index) => {
                const tabText = tab.querySelector('.tab-text');
                if (tabText && tabText.textContent.trim() === roomNumber) {
                    switchToTab(index);
                    return;
                }
            });
        }

        // 调试函数：检查标签页状态
        function debugTabState() {
            console.log('=== 标签页状态调试 ===');

            const tabLinks = document.querySelectorAll('.browser-tabs .nav-link');
            const tabPanes = document.querySelectorAll('.browser-tab-content .tab-pane');

            console.log(`标签页数量: ${tabLinks.length}`);
            console.log(`内容面板数量: ${tabPanes.length}`);

            tabLinks.forEach((tab, index) => {
                const isActive = tab.classList.contains('active');
                const tabText = tab.querySelector('.tab-text')?.textContent || '未知';
                const targetId = tab.getAttribute('data-bs-target');
                console.log(`标签页 ${index}: ${tabText} - 激活: ${isActive} - 目标: ${targetId}`);
            });

            tabPanes.forEach((pane, index) => {
                const isActive = pane.classList.contains('active');
                const isShow = pane.classList.contains('show');
                const paneId = pane.id;
                console.log(`内容面板 ${index}: ${paneId} - 激活: ${isActive} - 显示: ${isShow}`);
            });

            console.log('=== 调试结束 ===');
        }

        // 在控制台中暴露调试函数
        window.debugTabState = debugTabState;

        // 获取当前激活的标签页索引
        function getCurrentTabIndex() {
            const tabLinks = document.querySelectorAll('.browser-tabs .nav-link');
            for (let i = 0; i < tabLinks.length; i++) {
                if (tabLinks[i].classList.contains('active')) {
                    return i;
                }
            }
            return 0;
        }

        // 根据包厢号获取对应的标签页索引
        function getCurrentRoomIndex(roomNumber) {
            const tabLinks = document.querySelectorAll('.browser-tabs .nav-link .tab-text');
            for (let i = 0; i < tabLinks.length; i++) {
                if (tabLinks[i].textContent.trim() === roomNumber) {
                    return i;
                }
            }
            return -1; // 返回-1表示未找到（可能是单包厢模式）
        }

        // 只更新指定包厢的数据，不影响标签页状态
        function updateRoomDishDisplayOnly(roomNumber) {
            console.log(`🔄 更新包厢 ${roomNumber} 的数据`);

            // 获取指定包厢的菜品数据 - 修复URL编码问题
            fetch(`/waiter/menu/${encodeURIComponent(roomNumber)}`, {
                credentials: 'include'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.dishes) {
                    // 找到对应包厢的菜品容器
                    const roomIndex = Array.from(document.querySelectorAll('.browser-tabs .nav-link .tab-text'))
                        .findIndex(tab => tab.textContent.trim() === roomNumber);

                    if (roomIndex >= 0) {
                        const dishContainer = document.querySelector(`#room-${roomIndex + 1} .dish-list`);
                        if (dishContainer) {
                            // 更新菜品列表
                            updateDishListContent(dishContainer, data.dishes, roomNumber);
                            console.log(`✅ 包厢 ${roomNumber} 数据更新完成`);
                        }
                    }
                }
            })
            .catch(error => {
                console.error(`❌ 更新包厢 ${roomNumber} 数据失败:`, error);
            });
        }

        // 更新菜品列表内容
        function updateDishListContent(container, dishes, roomNumber) {
            if (!container) return;

            // 清空现有内容
            container.innerHTML = '';

            if (dishes.length === 0) {
                container.innerHTML = '<div class="text-center text-muted py-3">暂无菜品</div>';
                return;
            }

            // 重新生成菜品列表
            dishes.forEach(dish => {
                const dishElement = createDishElement(dish, roomNumber);
                container.appendChild(dishElement);
            });
        }

        // 创建菜品元素
        function createDishElement(dish, roomNumber) {
            const dishDiv = document.createElement('div');
            dishDiv.className = 'dish-item mb-2 p-2 border rounded';
            dishDiv.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${dish.dish_name}</strong>
                        <span class="badge bg-${getStatusColor(dish.status)} ms-2">${getStatusText(dish.status)}</span>
                    </div>
                    <div class="btn-group btn-group-sm">
                        ${dish.status === 'ready' ? `<button class="btn btn-success btn-sm" onclick="confirmDish(${dish.id})">确认上桌</button>` : ''}
                        ${dish.status === 'pending' ? `<button class="btn btn-warning btn-sm" onclick="rushDish(${dish.id})">催菜</button>` : ''}
                    </div>
                </div>
            `;
            return dishDiv;
        }

        // 获取状态颜色
        function getStatusColor(status) {
            const colors = {
                'pending': 'warning',
                'cooking': 'info',
                'ready': 'success',
                'served': 'secondary'
            };
            return colors[status] || 'secondary';
        }

        // 获取状态文本
        function getStatusText(status) {
            const texts = {
                'pending': '待制作',
                'cooking': '制作中',
                'ready': '已完成',
                'served': '已上桌'
            };
            return texts[status] || status;
        }

        // 获取当前激活标签页对应的包厢号
        function getCurrentActiveRoomNumber() {
            const activeTab = document.querySelector('.browser-tabs .nav-link.active');
            if (activeTab) {
                const tabText = activeTab.querySelector('.tab-text');
                if (tabText) {
                    return tabText.textContent.trim();
                }
            }

            // 如果是单包厢模式，从页面中提取包厢号
            const singleRoomTitle = document.querySelector('.card-header h5');
            if (singleRoomTitle) {
                const titleText = singleRoomTitle.textContent;
                const match = titleText.match(/(.+?)包厢信息/);
                if (match) {
                    return match[1].trim();
                }
            }

            return '';
        }

        // 更新特定包厢的菜品显示
        function updateRoomDishDisplay(roomNumber) {
            if (!roomNumber) return;

            // 查找该包厢的菜品容器
            const dishCards = document.querySelectorAll(`[data-room="${roomNumber}"] .dish-card`);
            dishCards.forEach(card => {
                const dishId = card.getAttribute('data-dish-id');
                if (dishId) {
                    // 重新获取菜品状态并更新显示
                    updateSingleDishDisplay(dishId, roomNumber);
                }
            });
        }

        // 更新单个菜品的显示状态
        function updateSingleDishDisplay(dishId, roomNumber) {
            fetch(`/api/dish-status/${dishId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const dishCard = document.querySelector(`[data-dish-id="${dishId}"]`);
                        if (dishCard) {
                            // 更新菜品状态显示
                            const statusElement = dishCard.querySelector('.dish-status');
                            const actionsElement = dishCard.querySelector('.dish-actions');

                            if (statusElement && actionsElement) {
                                updateDishStatusElements(statusElement, actionsElement, data.dish, roomNumber);
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('获取菜品状态失败:', error);
                });
        }

        // 更新菜品状态元素
        function updateDishStatusElements(statusElement, actionsElement, dish, roomNumber) {
            // 更新状态标签
            let statusHtml = '';
            switch(dish.status) {
                case 'pending_cook':
                    statusHtml = '<span class="badge bg-secondary">待制作</span>';
                    break;
                case 'cooking':
                    statusHtml = '<span class="badge bg-info">制作中</span>';
                    break;
                case 'ready':
                    statusHtml = '<span class="badge bg-warning">制作完成</span>';
                    break;
                case 'served':
                    statusHtml = '<span class="badge bg-success">厨房已出菜</span>';
                    break;
            }

            if (dish.waiter_status === 'served') {
                statusHtml += '<span class="badge bg-success ms-1">已划菜</span>';
            }

            statusElement.innerHTML = statusHtml;

            // 更新操作按钮
            let actionsHtml = '';
            if (dish.status === 'served' && !dish.waiter_confirmed) {
                actionsHtml = `<button type="button" class="btn btn-success btn-sm me-1"
                                onclick="confirmDishServed(${dish.id}, '${roomNumber}')" title="确认已上菜">
                                确认上菜
                              </button>`;
            } else if (dish.status === 'ready' && !dish.waiter_status) {
                actionsHtml = `<button type="button" class="btn btn-warning btn-sm me-1"
                                onclick="markDish(${dish.id}, 'served', '${roomNumber}')" title="划菜">
                                划菜
                              </button>`;
            } else if (dish.waiter_confirmed) {
                actionsHtml = '<span class="badge bg-success">已确认上菜</span>';
            } else if (dish.waiter_status === 'served') {
                actionsHtml = '<span class="badge bg-info">已划菜</span>';
            } else {
                actionsHtml = '<small class="text-muted">等待制作完成</small>';
            }

            actionsElement.innerHTML = actionsHtml;
        }

        // 页面可见性变化处理
        function initVisibilityChangeHandler() {
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    console.log('📱 页面隐藏，降低轮询频率');
                    consecutiveEmptyChecks += 5; // 快速降低频率
                    adjustPollingFrequency();
                } else {
                    console.log('📱 页面显示，恢复正常轮询');
                    consecutiveEmptyChecks = 0; // 重置计数器
                    adjustPollingFrequency();
                    // 页面重新可见时立即检查一次
                    setTimeout(checkForDishReady, 1000);
                }
            });
        }

        // 页面可见性检测
        function initPageVisibility() {
            document.addEventListener('visibilitychange', function() {
                isPageVisible = !document.hidden;
                if (isPageVisible) {
                    // 页面变为可见时立即刷新
                    checkForNewReadyDishes();
                }
            });
        }

        // 开始自动刷新
        function startAutoRefresh() {
            // 每12秒检查一次新的待上菜菜品
            autoRefreshInterval = setInterval(checkForNewReadyDishes, 12000);
        }

        // 启动权限状态检查
        function startAuthStatusCheck() {
            // 每10秒检查一次权限状态
            setInterval(checkAuthStatus, 10000);
        }

        // 启动菜品完成监听（优化版本）
        let dishReadyCheckInterval = null;
        let lastCheckTime = 0;
        let consecutiveEmptyChecks = 0;

        function startDishReadyListener() {
            // 智能轮询：根据活动状态调整频率
            const baseInterval = 8000; // 基础间隔8秒
            dishReadyCheckInterval = setInterval(checkForDishReady, baseInterval);
        }

        // 检查是否有新的菜品完成（优化版本）
        function checkForDishReady() {
            const now = Date.now();

            // 防止重复请求（最小间隔5秒）
            if (now - lastCheckTime < 5000) {
                return;
            }

            // 获取当前包厢号
            const roomNumber = getCurrentRoomNumber();
            if (!roomNumber) {
                consecutiveEmptyChecks++;
                adjustPollingFrequency();
                return;
            }

            // 检查页面是否可见（页面不可见时降低频率）
            if (document.hidden) {
                consecutiveEmptyChecks++;
                adjustPollingFrequency();
                return;
            }

            lastCheckTime = now;

            // 检查是否有新的菜品完成
            fetch(`/api/check-dish-ready/${encodeURIComponent(roomNumber)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.has_new_ready_dishes) {
                        console.log(`🍽️ 检测到${roomNumber}包厢有新菜品完成，更新显示`);
                        showSuccess(`${roomNumber}包厢有新菜品制作完成！`);

                        // 只更新对应包厢的菜品显示，而不是刷新整个页面
                        updateRoomDishDisplay(roomNumber);
                        updateDishProgress();

                        consecutiveEmptyChecks = 0; // 重置计数器
                    } else {
                        consecutiveEmptyChecks++;
                        adjustPollingFrequency();
                    }
                })
                .catch(error => {
                    // 静默处理错误，避免影响用户体验
                    console.log('检查菜品状态失败:', error);
                    consecutiveEmptyChecks++;
                    adjustPollingFrequency();
                });
        }

        // 动态调整轮询频率
        function adjustPollingFrequency() {
            if (!dishReadyCheckInterval) return;

            let newInterval = 8000; // 默认8秒

            if (consecutiveEmptyChecks > 10) {
                // 连续10次无新菜品，降低到15秒
                newInterval = 15000;
            } else if (consecutiveEmptyChecks > 20) {
                // 连续20次无新菜品，降低到30秒
                newInterval = 30000;
            }

            // 重新设置定时器
            clearInterval(dishReadyCheckInterval);
            dishReadyCheckInterval = setInterval(checkForDishReady, newInterval);
        }

        // 获取当前包厢号
        function getCurrentRoomNumber() {
            // 优先从当前激活的标签页获取包厢号
            const activeRoomNumber = getCurrentActiveRoomNumber();
            if (activeRoomNumber) {
                return activeRoomNumber;
            }

            // 如果是单包厢模式，从页面标题获取
            const titleElement = document.querySelector('h2');
            if (titleElement && titleElement.textContent) {
                const match = titleElement.textContent.match(/(.+?)包厢/);
                return match ? match[1] : null;
            }

            // 从包厢信息卡片获取
            const cardTitle = document.querySelector('.card-header h5');
            if (cardTitle && cardTitle.textContent) {
                const match = cardTitle.textContent.match(/(.+?)包厢信息/);
                return match ? match[1] : null;
            }

            return null;
        }

        // 检查权限状态
        function checkAuthStatus() {
            fetch('/api/check-auth-status')
                .then(response => {
                    if (response.status === 401) {
                        // 权限已被撤销，强制退出
                        forceLogout('权限已被撤销，请重新登录');
                        return;
                    }
                    return response.json();
                })
                .then(data => {
                    if (data && !data.is_authorized) {
                        // 权限已被撤销，强制退出
                        forceLogout('服务员权限已被撤销，请重新登录');
                    }
                })
                .catch(error => {
                    console.error('权限状态检查失败:', error);
                    // 如果是401错误，强制退出
                    if (error.message && error.message.includes('401')) {
                        forceLogout('权限已被撤销，请重新登录');
                    }
                });
        }

        // 强制退出登录
        function forceLogout(message) {
            // 显示提示信息
            alert(message);

            // 清除所有相关的cookie和本地存储
            document.cookie = 'access_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'session_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'user_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
            document.cookie = 'user_role=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';

            // 清除本地存储
            localStorage.clear();
            sessionStorage.clear();

            // 停止所有定时器
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }

            // 跳转到登录页面
            window.location.href = '/login';
        }

        // 检查新的待上菜菜品
        function checkForNewReadyDishes() {
            if (!isPageVisible) return; // 页面不可见时不检查

            fetch('/waiter/check-ready-dishes')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.ready_dishes) {
                        const currentReadyDishes = new Set(data.ready_dishes.map(dish => dish.id));

                        // 找出新的待上菜菜品
                        const newReadyDishes = [...currentReadyDishes].filter(id => !previousReadyDishes.has(id));

                        if (newReadyDishes.length > 0) {
                            // 播放提示音
                            playNotificationSound();

                            // 按包厢分组显示通知
                            const newDishesGroupedByRoom = {};
                            data.ready_dishes.filter(dish => newReadyDishes.includes(dish.id))
                                .forEach(dish => {
                                    const roomNumber = dish.room_number || '未知包厢';
                                    if (!newDishesGroupedByRoom[roomNumber]) {
                                        newDishesGroupedByRoom[roomNumber] = [];
                                    }
                                    newDishesGroupedByRoom[roomNumber].push(dish);
                                });

                            // 为每个包厢显示通知
                            Object.keys(newDishesGroupedByRoom).forEach(roomNumber => {
                                const roomDishes = newDishesGroupedByRoom[roomNumber];
                                showNewDishNotificationForRoom(roomNumber, roomDishes.length);

                                // 更新对应包厢的菜品显示
                                updateRoomDishDisplay(roomNumber);
                            });

                            // 更新总体菜品状态显示
                            updateDishProgress();
                        }

                        previousReadyDishes = currentReadyDishes;
                    }
                })
                .catch(error => {
                    console.error('检查待上菜菜品失败:', error);
                });
        }

        // 播放提示音
        function playNotificationSound() {
            try {
                // 创建音频上下文
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();

                // 创建振荡器
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                // 连接节点
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                // 设置音频参数
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime); // 800Hz
                oscillator.type = 'sine';

                // 设置音量
                gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                // 播放音效
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.5);

                // 播放两次
                setTimeout(() => {
                    const oscillator2 = audioContext.createOscillator();
                    const gainNode2 = audioContext.createGain();

                    oscillator2.connect(gainNode2);
                    gainNode2.connect(audioContext.destination);

                    oscillator2.frequency.setValueAtTime(1000, audioContext.currentTime);
                    oscillator2.type = 'sine';

                    gainNode2.gain.setValueAtTime(0.3, audioContext.currentTime);
                    gainNode2.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);

                    oscillator2.start(audioContext.currentTime);
                    oscillator2.stop(audioContext.currentTime + 0.5);
                }, 600);

            } catch (error) {
                console.error('播放提示音失败:', error);
            }
        }

        // 显示新菜品通知
        function showNewDishNotification(count) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = 'alert alert-warning alert-dismissible fade show position-fixed';
            notification.style.cssText = `
                top: 70px;
                right: 20px;
                z-index: 9999;
                min-width: 250px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: 2px solid #ff6b35;
                background: linear-gradient(45deg, #ff6b35, #f7931e);
                color: white;
                font-weight: bold;
            `;

            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-bell-fill me-2" style="font-size: 1.2rem;"></i>
                    <div>
                        <strong>新菜品待上菜！</strong><br>
                        <small>有 ${count} 道菜品制作完成</small>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 5秒后自动消失
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 显示特定包厢的新菜品通知
        function showNewDishNotificationForRoom(roomNumber, count) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = 'alert alert-warning alert-dismissible fade show position-fixed';
            notification.style.cssText = `
                top: ${70 + (document.querySelectorAll('.position-fixed.alert').length * 80)}px;
                right: 20px;
                z-index: 9999;
                min-width: 280px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border: 2px solid #ff6b35;
                background: linear-gradient(45deg, #ff6b35, #f7931e);
                color: white;
                font-weight: bold;
            `;

            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="bi bi-bell-fill me-2" style="font-size: 1.2rem;"></i>
                    <div>
                        <strong>${roomNumber}包厢新菜品待上菜！</strong><br>
                        <small>有 ${count} 道菜品制作完成</small>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // 5秒后自动消失
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // 加载用餐控制按钮
        function loadDiningControls() {
            // 获取所有包厢的用餐控制容器
            const containers = document.querySelectorAll('[id^="diningControl-"]');

            containers.forEach(container => {
                const roomNumber = container.id.replace('diningControl-', '');
                loadDiningControlForRoom(container, roomNumber);
            });
        }

        // 为特定包厢加载用餐控制按钮
        function loadDiningControlForRoom(container, roomNumber) {
            // 检查该包厢是否已开始用餐
            fetch(`/waiter/check-dining-status/${encodeURIComponent(roomNumber)}`)
                .then(response => {
                    if (!response.ok) {
                        // 如果是401未授权，可能是登录过期
                        if (response.status === 401) {
                            console.warn(`包厢${roomNumber}用餐状态检查返回401，登录可能过期`);
                            // 显示登录过期提示
                            container.innerHTML = `
                                <div class="alert alert-warning mb-2">
                                    <i class="bi bi-exclamation-triangle"></i>
                                    登录状态可能已过期
                                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="window.location.reload()">
                                        刷新页面
                                    </button>
                                </div>
                            `;
                            // 默认启用按钮，让用户可以尝试操作
                            enableCommandButtons(roomNumber);
                            return null; // 返回null表示不需要进一步处理
                        }
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data === null) return; // 跳过处理（401情况）

                    console.log(`🔍 包厢${roomNumber}用餐状态检查结果:`, data);

                    // 检查数据有效性
                    if (data && data.success !== undefined && data.success && data.dining_started) {
                        // 已开始用餐，显示用餐时间和结束按钮
                        container.innerHTML = `
                            <div class="alert alert-success mb-2">
                                <i class="bi bi-check-circle"></i>
                                用餐已开始 - ${data.dining_start_time || ''}
                            </div>
                            <button type="button" class="btn btn-danger btn-lg w-100 end-dining-btn"
                                    onclick="endDining('${roomNumber}')">
                                <i class="bi bi-stop-circle-fill"></i>
                                结束用餐
                            </button>
                        `;
                        // 启用其他指令按钮
                        enableCommandButtons(roomNumber);
                        console.log(`✅ 包厢${roomNumber}已开始用餐，启用操作按钮`);
                    } else if (data && data.success !== undefined) {
                        // API正常返回但用餐未开始，显示开始用餐按钮
                        container.innerHTML = `
                            <button type="button" class="btn btn-danger btn-lg w-100 start-dining-btn"
                                    onclick="startDining('${roomNumber}')">
                                <i class="bi bi-play-circle"></i>
                                开始用餐
                            </button>
                            <div class="alert alert-warning mt-2 mb-0">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>请先点击"开始用餐"按钮</strong><br>
                                <small>输入用餐人数后，其他操作按钮才会启用</small>
                            </div>
                        `;
                        // 禁用其他指令按钮
                        disableCommandButtons(roomNumber);
                        console.log(`⚠️ 包厢${roomNumber}未开始用餐，禁用操作按钮`);
                    } else {
                        // API返回异常数据，默认启用按钮
                        container.innerHTML = `
                            <div class="alert alert-info mb-2">
                                <i class="bi bi-info-circle"></i>
                                状态检查异常，请手动确认用餐状态
                            </div>
                        `;
                        enableCommandButtons(roomNumber);
                        console.warn(`包厢${roomNumber}用餐状态检查返回异常数据，默认启用按钮`);
                    }
                })
                .catch(error => {
                    console.error('检查用餐状态失败:', error);
                    // 默认启用按钮，让后端来验证
                    container.innerHTML = `
                        <div class="alert alert-warning mb-2">
                            <i class="bi bi-exclamation-triangle"></i>
                            状态检查失败，请手动确认用餐状态
                        </div>
                    `;
                    enableCommandButtons(roomNumber);
                    console.warn(`包厢${roomNumber}用餐状态检查失败，默认启用按钮`);
                });
        }

        // 开始用餐
        function startDining(roomNumber) {
            const guestCount = prompt(`${roomNumber}包厢开始用餐\n\n请输入用餐人数:`, '');

            if (guestCount === null) {
                return; // 用户取消
            }

            const count = parseInt(guestCount);
            if (isNaN(count) || count <= 0) {
                alert('请输入有效的用餐人数');
                return;
            }

            // 发送用餐开始指令
            fetch('/waiter/send-command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    room_number: roomNumber,
                    action_type: 'dining_start',
                    action_content: count.toString()
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`${roomNumber}包厢用餐开始成功！\n用餐人数：${count}人`);

                    // 同步更新人数到所有相关界面
                    syncGuestCountToAllInterfaces(roomNumber, count);

                    // 立即启用指令按钮，不等待重新加载
                    enableCommandButtons(roomNumber);

                    // 延迟重新加载用餐控制按钮，确保后端状态已更新
                    setTimeout(() => {
                        const container = document.getElementById(`diningControl-${roomNumber}`);
                        if (container) {
                            loadDiningControlForRoom(container, roomNumber);
                        }
                    }, 1000);
                } else {
                    alert(data.message || '用餐开始失败');
                }
            })
            .catch(error => {
                console.error('发送用餐开始指令失败:', error);
                alert('发送指令失败，请重试');
            });
        }

        // 结束用餐
        function endDining(roomNumber) {
            console.log(`🔚 开始结束包厢 ${roomNumber} 的用餐`);

            // 检查是否有未处理的菜品 - 只检查当前包厢的菜品
            const currentRoomIndex = getCurrentRoomIndex(roomNumber);
            let dishItems;

            if (currentRoomIndex >= 0) {
                // 多包厢模式：只检查当前包厢标签页的菜品
                const currentTabPane = document.querySelector(`#room-${currentRoomIndex + 1}`);
                if (currentTabPane) {
                    dishItems = currentTabPane.querySelectorAll('.dish-card');
                } else {
                    dishItems = [];
                }
            } else {
                // 单包厢模式：检查所有菜品
                dishItems = document.querySelectorAll('.dish-card');
            }

            let unfinishedDishes = 0;
            let unfinishedDishNames = [];

            dishItems.forEach(item => {
                const statusElement = item.querySelector('.dish-status');
                if (statusElement) {
                    const status = statusElement.textContent.trim();
                    if (status === '待上菜' || status === '制作中' || status === '待制作' || status === '正在制作') {
                        unfinishedDishes++;
                        const dishNameElement = item.querySelector('.dish-name');
                        if (dishNameElement) {
                            unfinishedDishNames.push(dishNameElement.textContent.trim());
                        }
                    }
                }
            });

            console.log(`📊 包厢 ${roomNumber} 未完成菜品数量: ${unfinishedDishes}`);

            if (unfinishedDishes > 0) {
                alert(`包厢 ${roomNumber} 还有未划菜的菜品，无法结束用餐\n\n未完成菜品：\n${unfinishedDishNames.join('\n')}`);
                return;
            }

            if (!confirm(`确认结束 ${roomNumber}包厢 的用餐服务吗？\n\n结束后该包厢将变为空闲状态。`)) {
                return;
            }

            // 发送结束用餐指令
            fetch('/waiter/end-dining', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    room_number: roomNumber
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 立即禁用所有按钮
                    disableCommandButtons(roomNumber);

                    // 检查是否需要强制退出登录
                    if (data.should_logout) {
                        // 显示退出登录提示
                        showSuccess(`${roomNumber}包厢用餐服务已结束！正在强制退出登录...`);

                        // 强制跳转到登录页面
                        setTimeout(() => {
                            console.log('🚪 服务员完成所有包厢服务，强制退出登录');
                            // 清除所有本地存储
                            localStorage.clear();
                            sessionStorage.clear();
                            // 强制跳转到登录页面
                            window.location.replace('/login');
                        }, 2000);
                    } else {
                        // 如果还有其他包厢，只是更新当前包厢的状态
                        showSuccess(`${roomNumber}包厢用餐服务已结束！`);

                        // 保存当前标签页状态
                        const savedTabIndex = currentTabIndex;
                        console.log(`💾 保存当前标签页索引: ${savedTabIndex}`);

                        // 重新加载用餐控制按钮，而不是刷新整个页面
                        const container = document.getElementById(`diningControl-${roomNumber}`);
                        if (container) {
                            loadDiningControlForRoom(container, roomNumber);
                        }

                        // 只更新当前包厢的菜品状态，不影响其他包厢
                        updateRoomDishDisplayOnly(roomNumber);

                        // 确保标签页状态不变
                        setTimeout(() => {
                            const currentActiveIndex = getCurrentTabIndex();
                            if (currentActiveIndex !== savedTabIndex) {
                                console.log(`🔄 标签页状态已改变，恢复到: ${savedTabIndex}`);
                                switchToTab(savedTabIndex);
                            }
                        }, 100);
                    }
                } else {
                    alert(data.message || '结束用餐失败');
                }
            })
            .catch(error => {
                console.error('结束用餐失败:', error);
                alert('操作失败，请重试');
            });
        }

        // 启用指令按钮
        function enableCommandButtons(roomNumber) {
            const container = document.getElementById(`commandButtons-${roomNumber}`);
            const statusDiv = document.getElementById(`commandStatus-${roomNumber}`);

            if (container) {
                const buttons = container.querySelectorAll('button');
                buttons.forEach(button => {
                    button.disabled = false;
                    button.classList.remove('btn-secondary', 'disabled-state');
                    button.title = ''; // 清除禁用提示

                    // 恢复原有颜色类
                    if (button.textContent.includes('催菜')) {
                        button.classList.add('btn-warning');
                    } else if (button.textContent.includes('上菜')) {
                        button.classList.add('btn-success');
                    } else if (button.textContent.includes('收脏餐')) {
                        button.classList.add('btn-secondary');
                    } else {
                        button.classList.add('btn-info');
                    }
                });
            }

            if (statusDiv) {
                statusDiv.style.display = 'none';
            }
        }

        // 禁用指令按钮（旧版本，保持向后兼容）
        function disableCommandButtons(roomNumber) {
            const container = document.getElementById(`commandButtons-${roomNumber}`) ||
                            document.getElementById('commandButtons');

            if (container) {
                disableCommandButtonsInContainer(container, roomNumber);
            }
        }

        // 在指定容器中禁用指令按钮（除开始用餐外）
        function disableCommandButtonsInContainer(container, roomNumber) {
            const buttons = container.querySelectorAll('button');
            buttons.forEach(button => {
                const onclick = button.getAttribute('onclick');
                const buttonText = button.textContent.trim();

                // 只保留开始用餐按钮可用 - 更严格的检查
                const isDiningStartButton = (onclick && onclick.includes('dining_start')) ||
                                          buttonText.includes('开始用餐') ||
                                          buttonText.includes('用餐开始');

                if (!isDiningStartButton) {
                    // 完全禁用按钮
                    button.disabled = true;
                    button.classList.add('disabled');
                    button.style.opacity = '0.5';
                    button.style.cursor = 'not-allowed';
                    button.title = `${roomNumber}包厢尚未开始用餐，请先点击"开始用餐"按钮`;

                    // 保存原始的onclick属性，然后移除
                    if (onclick) {
                        button.setAttribute('data-original-onclick', onclick);
                        button.removeAttribute('onclick');
                    }

                    // 添加新的点击事件处理器，阻止所有点击
                    const preventClickHandler = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        e.stopImmediatePropagation();
                        alert(`${roomNumber}包厢尚未开始用餐，请先点击"开始用餐"按钮`);
                        return false;
                    };

                    // 移除之前的事件监听器（如果有）
                    button.removeEventListener('click', button._preventClickHandler);

                    // 添加新的事件监听器
                    button.addEventListener('click', preventClickHandler, true);
                    button._preventClickHandler = preventClickHandler; // 保存引用以便后续移除
                }
            });

            // 添加提示信息
            if (!container.querySelector('.dining-status-warning')) {
                const warning = document.createElement('div');
                warning.className = 'dining-status-warning alert alert-warning mt-2 text-center';
                warning.innerHTML = `<small>⚠️ ${roomNumber}包厢尚未开始用餐，请先点击"开始用餐"按钮</small>`;
                container.appendChild(warning);
            }
        }

        // 加载指令模板
        function loadCommandTemplates() {
            fetch('/api/command-templates')
                .then(response => response.json())
                .then(templates => {
                    // 获取所有包厢的指令按钮容器
                    const containers = document.querySelectorAll('[id^="commandButtons-"]');

                    if (containers.length === 0) {
                        // 如果没有包厢特定的容器，使用通用容器（向后兼容）
                        const generalContainer = document.getElementById('commandButtons');
                        if (generalContainer) {
                            loadCommandsForContainer(generalContainer, templates, '');
                        }
                    } else {
                        // 为每个包厢容器加载指令按钮
                        containers.forEach(container => {
                            const roomNumber = container.id.replace('commandButtons-', '');
                            loadCommandsForContainer(container, templates, roomNumber);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载指令模板失败:', error);
                    // 如果加载失败，显示默认按钮
                    loadDefaultButtons();
                })
                .finally(() => {
                    // 加载完按钮后检查用餐状态
                    checkDiningStatusForAllRooms();
                });
        }

        // 为特定容器加载指令按钮
        function loadCommandsForContainer(container, templates, roomNumber) {
            container.innerHTML = '';

            templates.forEach(template => {
                // 使用模板的输入设定
                const needsInput = template.allow_input;
                const buttonHtml = `
                    <div class="col-4">
                        <button type="button" class="btn btn-${getButtonColor(template.category)} action-btn w-100"
                                onclick="${needsInput ? `showInputDialog('${template.code}', '${template.name}', '${template.input_placeholder || ''}', ${template.input_required || false}, '${roomNumber}')` : `sendCommand('${template.code}', '', '${roomNumber}')`}">
                            ${template.name}
                        </button>
                    </div>
                `;
                container.innerHTML += buttonHtml;
            });
        }

        // 获取按钮颜色
        function getButtonColor(category) {
            const colors = {
                'service': 'success',
                'urgent': 'warning',
                'food': 'info',
                'general': 'secondary'
            };
            return colors[category] || 'primary';
        }

        // 获取按钮图标
        function getButtonIcon(code) {
            const icons = {
                'serve_dish': 'arrow-up-circle',
                'rush_dish': 'clock',
                'clean_table': 'trash',
                'add_staple': 'bowl',
                'add_drink': 'cup',
                'aolong_rice': 'egg-fried',
                'dining_start': 'play-circle'
            };
            return icons[code] || 'gear';
        }

        // 显示输入对话框
        function showInputDialog(code, name, placeholder = '', required = false, roomNumber = '') {
            // 使用传入的提示文字，如果没有则使用默认值
            let promptText = placeholder || '请输入内容';
            let defaultValue = '';

            // 为特定指令提供默认值
            switch(code) {
                case 'add_staple':
                    defaultValue = '米饭';
                    break;
                case 'add_drink':
                    defaultValue = '';
                    break;
                default:
                    defaultValue = '';
            }

            // 显示包厢信息
            const roomInfo = roomNumber ? ` - ${roomNumber}包厢` : '';
            const content = prompt(`${name}${roomInfo}\n\n${promptText}`, defaultValue);

            if (content === null) {
                return; // 用户取消
            }

            // 检查是否必须输入
            if (required && !content.trim()) {
                alert('此指令需要输入内容');
                return;
            }

            sendCommandToSpecificRoom(code, content.trim(), roomNumber);
        }

        // 加载默认按钮（备用）
        function loadDefaultButtons() {
            const container = document.getElementById('commandButtons');
            container.innerHTML = `
                <div class="col-4">
                    <button type="button" class="btn btn-success action-btn w-100" onclick="sendCommand('serve_dish')">
                        上菜
                    </button>
                </div>
                <div class="col-4">
                    <button type="button" class="btn btn-warning action-btn w-100" onclick="sendCommand('rush_dish')">
                        催菜
                    </button>
                </div>
                <div class="col-4">
                    <button type="button" class="btn btn-secondary action-btn w-100" onclick="sendCommand('clean_table')">
                        收脏餐
                    </button>
                </div>
                <div class="col-4">
                    <button type="button" class="btn btn-info action-btn w-100" onclick="sendCommand('add_staple')">
                        上主食
                    </button>
                </div>
                <div class="col-4">
                    <button type="button" class="btn btn-primary action-btn w-100" onclick="sendCommand('add_drink')">
                        加酒水
                    </button>
                </div>
                <div class="col-4">
                    <button type="button" class="btn btn-dark action-btn w-100" onclick="sendCommand('special')">
                        特殊服务
                    </button>
                </div>
            `;
        }
        // 确认收菜
        function confirmReceive(itemId) {
            if (confirm('确认收到此菜品？')) {
                fetch('/waiter/confirm-receive/' + itemId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    if (response.ok) {
                        showSuccess('确认收菜成功！');
                        // 局部更新而不是整页刷新
                        setTimeout(() => {
                            updateDishProgress();
                            updateDishItemStatus(dishId, 'confirmed', '');
                        }, 500);
                    } else {
                        showError('操作失败');
                    }
                });
            }
        }

        // 上菜
        function serveDish(itemId) {
            if (confirm('确认上菜？')) {
                sendDishCommand(itemId, 'serve', '上菜完成');
            }
        }

        // 催菜
        function rushDish(itemId) {
            const reason = prompt('催菜原因（可选）：');
            sendDishCommand(itemId, 'rush', reason || '催菜');
        }

        // 上主食
        function addStaple(itemId) {
            const staple = prompt('请输入主食名称：', '米饭');
            if (staple) {
                sendDishCommand(itemId, 'add_staple', staple);
            }
        }

        // 发送指令到特定包厢
        function sendCommandToSpecificRoom(commandCode, content = '', roomNumber = '') {
            if (!roomNumber) {
                // 如果没有指定包厢，使用原有逻辑发送到所有包厢
                sendCommand(commandCode, content);
                return;
            }

            // 前端验证：如果不是用餐开始指令，检查用餐状态
            if (commandCode !== 'dining_start') {
                // 先检查用餐状态
                fetch(`/waiter/check-dining-status/${encodeURIComponent(roomNumber)}`)
                    .then(response => {
                        if (!response.ok) {
                            // 如果是401未授权，可能是登录过期
                            if (response.status === 401) {
                                console.warn('用餐状态检查返回401，登录可能过期');
                                // 显示提示并刷新页面重新登录
                                if (confirm('登录状态可能已过期，是否刷新页面重新登录？')) {
                                    window.location.reload();
                                    return;
                                } else {
                                    // 用户选择不刷新，直接尝试发送指令
                                    console.warn('用户选择不刷新，直接尝试发送指令');
                                    proceedWithCommand();
                                    return;
                                }
                            }
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data && data.success !== undefined) {
                            // API正常返回数据
                            if (!data.success || !data.dining_started) {
                                alert(`${roomNumber}包厢尚未开始用餐，请先点击"开始用餐"按钮`);
                                return;
                            }
                            // 用餐已开始，继续发送指令
                            proceedWithCommand();
                        } else {
                            // API返回异常数据，直接尝试发送指令
                            console.warn('用餐状态检查返回异常数据，直接尝试发送指令');
                            proceedWithCommand();
                        }
                    })
                    .catch(error => {
                        console.error('检查用餐状态失败:', error);
                        // 如果检查失败，直接尝试发送指令，让后端来验证
                        console.warn('用餐状态检查失败，直接尝试发送指令，让后端验证');
                        proceedWithCommand();
                    });
            } else {
                // 用餐开始指令，直接发送
                proceedWithCommand();
            }

            function proceedWithCommand() {
                // 显示确认对话框
                const actionName = getActionDisplayName(commandCode);
                const confirmMessage = `确认向 ${roomNumber}包厢 发送指令：${actionName}${content ? '\n内容：' + content : ''}`;
                if (!confirm(confirmMessage)) {
                    return;
                }

                // 记录当前激活的标签页
                const currentTabIndex = getCurrentTabIndex();

                sendCommandToRoom(roomNumber, commandCode, content).then(success => {
                    if (success) {
                        showSuccess('指令发送成功！');
                        // 播放语音提示，包含包厢名称
                        const voiceText = getVoiceTextWithRoom([roomNumber], commandCode, content);
                        playVoice(voiceText);

                        // 延迟1.5秒后刷新数据，但保持在当前标签页
                        setTimeout(() => {
                            // 保存当前标签页状态
                            const savedTabIndex = currentTabIndex;

                            // 只刷新当前包厢的数据，避免页面跳转
                            updateRoomDishDisplayOnly(roomNumber);

                            // 确保保持在当前标签页
                            setTimeout(() => {
                                const tabLinks = document.querySelectorAll('.browser-tabs .nav-link');
                                const tabPanes = document.querySelectorAll('.browser-tab-content .tab-pane');

                                if (tabLinks.length > savedTabIndex && tabPanes.length > savedTabIndex) {
                                    // 移除所有激活状态
                                    tabLinks.forEach(tab => {
                                        tab.classList.remove('active');
                                        tab.setAttribute('aria-selected', 'false');
                                    });
                                    tabPanes.forEach(pane => {
                                        pane.classList.remove('show', 'active');
                                    });

                                    // 激活指定的标签页
                                    tabLinks[savedTabIndex].classList.add('active');
                                    tabLinks[savedTabIndex].setAttribute('aria-selected', 'true');
                                    tabPanes[savedTabIndex].classList.add('show', 'active');

                                    console.log(`🔄 保持标签页状态: 索引 ${savedTabIndex}`);
                                }
                            }, 100);
                        }, 1500);
                    } else {
                        showError('指令发送失败');
                    }
                });
            }
        }

        // 获取指令显示名称
        function getActionDisplayName(commandCode) {
            const actionNames = {
                'dining_start': '用餐开始',
                'serve_dish': '上菜',
                'rush_dish': '催菜',
                'clean_table': '收脏餐',
                'add_staple': '上主食',
                'add_drink': '加酒水',
                'aolong_rice': '澳龙泡饭'
            };
            return actionNames[commandCode] || commandCode;
        }

        // 发送指令（原有函数，用于向所有包厢发送）
        function sendCommand(commandCode, content = '', roomNumber = '') {
            // 如果指定了包厢，使用特定包厢发送
            if (roomNumber) {
                sendCommandToSpecificRoom(commandCode, content, roomNumber);
                return;
            }

            {% if not user.assigned_tables %}
            showError('您还没有被分配包厢，请联系餐饮经理');
            return;
            {% endif %}

            const rooms = '{{ user.assigned_tables }}'.split(',');

            // 发送到所有分配的包厢
            let successCount = 0;
            rooms.forEach(room => {
                sendCommandToRoom(room.trim(), commandCode, content).then(success => {
                    if (success) successCount++;
                    if (successCount === rooms.length) {
                        showSuccess('指令发送成功！');
                        // 播放语音提示，包含包厢名称
                        const voiceText = getVoiceTextWithRoom(rooms, commandCode, content);
                        playVoice(voiceText);

                        // 上菜指令只发送到厨房，不自动检查结束用餐
                        // 服务员需要在菜品清单中逐个确认划菜/退菜后才能结束用餐
                    }
                });
            });
        }

        // 获取当前选中的包厢（用于向后兼容）
        function getCurrentRoomNumber() {
            // 如果是多包厢模式，获取当前激活的标签页
            const activeTab = document.querySelector('#roomTabs .nav-link.active');
            if (activeTab) {
                const tabText = activeTab.textContent.trim();
                return tabText.replace(/^\s*\S+\s*/, ''); // 移除图标，保留包厢名称
            }

            // 如果是单包厢模式，从页面中提取包厢名称
            const roomHeader = document.querySelector('.card-header h5, .card-header h6');
            if (roomHeader) {
                const headerText = roomHeader.textContent;
                const match = headerText.match(/(\S+包厢)/);
                if (match) {
                    return match[1].replace('包厢', '');
                }
            }

            // 最后尝试从用户分配的包厢中获取第一个
            {% if user.assigned_tables %}
            const rooms = '{{ user.assigned_tables }}'.split(',');
            return rooms[0].trim();
            {% endif %}

            return '';
        }

        // 检查所有菜品是否已上齐
        function checkAllServed(rooms) {
            rooms.forEach(room => {
                fetch(`/waiter/check-all-served/${room.trim()}`)
                .then(response => response.json())
                .then(data => {
                    if (data.all_served) {
                        if (confirm(`${room}包厢所有菜品已上齐，是否结束用餐？`)) {
                            finishDining(room.trim());
                        }
                    }
                });
            });
        }

        // 结束用餐（修复版本）
        function finishDining(room) {
            fetch(`/waiter/finish-dining/${encodeURIComponent(room)}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            }).then(response => {
                // 修复：先检查HTTP状态码，再解析JSON
                console.log(`结束用餐API响应状态: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                // 修复：更详细的成功状态检查
                console.log('结束用餐API响应数据:', data);

                if (data.success === true) {
                    showSuccess(`${room}包厢用餐已结束！正在退出登录...`);
                    // 结束用餐后自动退出登录
                    setTimeout(() => {
                        window.location.href = '/logout';
                    }, 2000);
                } else {
                    // 修复：显示具体的错误信息
                    const errorMessage = data.message || data.detail || '结束用餐失败';
                    console.error('结束用餐业务逻辑错误:', errorMessage);
                    showError(errorMessage);
                }
            }).catch(error => {
                console.error('结束用餐请求失败:', error);
                // 修复：更友好的错误提示
                showError(`操作失败: ${error.message || '网络错误，请重试'}`);
            });
        }

        // 发送菜品相关指令
        function sendDishCommand(itemId, type, content) {
            fetch('/waiter/dish-command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId,
                    action_type: type,
                    action_content: content
                })
            }).then(response => {
                if (response.ok) {
                    showSuccess('指令发送成功！');
                    // 不刷新页面，保持当前标签状态
                } else {
                    showError('指令发送失败');
                }
            });
        }

        // 发送包厢指令
        function sendCommandToRoom(room, commandCode, content) {
            return fetch('/waiter/send-command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    room_number: room,
                    action_type: commandCode,
                    action_content: content
                })
            }).then(response => {
                if (response.ok) {
                    return true;
                } else {
                    // 处理错误响应
                    return response.json().then(errorData => {
                        if (errorData.detail) {
                            alert(errorData.detail);
                        } else {
                            alert('指令发送失败，请重试');
                        }
                        return false;
                    }).catch(() => {
                        alert('指令发送失败，请重试');
                        return false;
                    });
                }
            }).catch(error => {
                console.error('发送指令失败:', error);
                alert('网络错误，请重试');
                return false;
            });
        }

        // 获取语音文本
        function getVoiceText(commandCode, content) {
            const voiceMap = {
                'serve_dish': '上菜',
                'rush_dish': '催菜',
                'clean_table': '收脏餐',
                'add_staple': `上主食：${content}`,
                'add_drink': `加酒水：${content}`,
                'aolong_rice': '澳龙泡饭',
                'dining_start': `请上菜，用餐人数${content}人`
            };
            return voiceMap[commandCode] || commandCode;
        }

        // 获取包含包厢名称的语音文本
        function getVoiceTextWithRoom(rooms, commandCode, content) {
            const baseText = getVoiceText(commandCode, content);
            if (rooms.length === 1) {
                return `${rooms[0]}包厢${baseText}`;
            } else {
                const roomList = rooms.join('、');
                return `${roomList}包厢${baseText}`;
            }
        }

        // 播放语音
        function playVoice(text) {
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                speechSynthesis.speak(utterance);
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            const toast = document.createElement('div');
            toast.className = 'alert alert-success position-fixed';
            toast.style.cssText = 'top: 70px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; text-align: center;';
            toast.innerHTML = '✓ ' + message;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }

        // 显示错误消息
        function showError(message) {
            const toast = document.createElement('div');
            toast.className = 'alert alert-danger position-fixed';
            toast.style.cssText = 'top: 70px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px; text-align: center;';
            toast.innerHTML = '✗ ' + message;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 3000);
        }

        // 划菜操作（仅支持划菜，不支持撤销）
        function markDish(itemId, status, roomNumber = '') {
            // 只允许划菜操作，不允许撤销
            if (status !== 'served') {
                alert('不支持撤销操作，菜品一旦划菜无法撤销');
                return;
            }

            let action = '划菜';
            let roomInfo = roomNumber ? `${roomNumber}包厢` : '';
            let confirmMsg = `确认${roomInfo}此菜品已送达客人？`;

            if (confirm(confirmMsg)) {
                fetch('/waiter/mark-dish/' + itemId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        status: status,
                        room_number: roomNumber
                    })
                }).then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess(`${roomInfo}${action}成功！`);
                        // 只更新对应包厢的菜品显示
                        updateRoomDishDisplay(roomNumber);
                        // 立即更新进度显示，但不刷新页面以保持当前标签状态
                        setTimeout(() => {
                            updateDishProgress();
                            // 局部更新菜品状态而不是整页刷新
                            updateDishItemStatus(itemId, status, roomNumber);
                        }, 100);
                    } else {
                        showError(data.message || action + '失败');
                    }
                }).catch(error => {
                    showError('操作失败，请重试');
                });
            }
        }

        // 确认菜品已上菜
        function confirmDishServed(itemId, roomNumber = '') {
            let roomInfo = roomNumber ? `${roomNumber}包厢` : '';
            if (confirm(`确认${roomInfo}此菜品已送达客人？`)) {
                fetch('/waiter/confirm-dish-served/' + itemId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        room_number: roomNumber
                    })
                }).then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSuccess(`${roomInfo}确认上菜成功！`);
                        // 只更新对应包厢的菜品显示
                        updateRoomDishDisplay(roomNumber);
                        // 立即更新进度显示，但不刷新页面
                        setTimeout(() => {
                            updateDishProgress();
                            updateDishItemStatus(itemId, 'confirmed', roomNumber);
                        }, 100);
                    } else {
                        showError(data.message || '确认失败');
                    }
                }).catch(error => {
                    showError('操作失败，请重试');
                });
            }
        }

        // 结束包厢用餐（修复版本）
        function finishRoomDining(roomNumber) {
            if (confirm(`确认${roomNumber}包厢用餐结束？\n\n此操作将：\n- 清空包厢状态\n- 重置包厢为空闲\n- 从您的服务列表中移除该包厢`)) {

                // 修复：更安全的按钮获取方式
                const button = event && event.target ? event.target : document.querySelector(`button[onclick*="finishRoomDining('${roomNumber}')"]`);
                const originalText = button ? button.innerHTML : '';

                if (button) {
                    button.disabled = true;
                    button.innerHTML = '<i class="bi bi-hourglass-split"></i> 处理中...';
                }

                fetch('/waiter/finish-room-dining/' + encodeURIComponent(roomNumber), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                }).then(response => {
                    // 修复：先检查HTTP状态码，再解析JSON
                    console.log(`结束用餐API响应状态: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // 修复：更详细的成功状态检查和日志
                    console.log('结束用餐API响应数据:', data);

                    if (data.success === true) {
                        showSuccess(`${roomNumber}包厢用餐已结束！`);

                        // 移除该包厢的标签页（如果是多包厢模式）
                        const tabElement = document.querySelector(`#roomTabs .nav-link[data-room="${roomNumber}"]`);
                        if (tabElement) {
                            const tabPane = document.querySelector(`#room-${roomNumber}`);
                            if (tabPane) {
                                tabPane.remove();
                            }
                            tabElement.closest('.nav-item').remove();

                            // 如果还有其他标签页，激活第一个
                            const remainingTabs = document.querySelectorAll('#roomTabs .nav-link');
                            if (remainingTabs.length > 0) {
                                remainingTabs[0].click();
                            } else {
                                // 没有其他包厢了，显示提示并可能退出登录
                                showSuccess('所有包厢服务已结束，正在返回登录页面...');
                                setTimeout(() => {
                                    window.location.href = '/logout';
                                }, 3000);
                            }
                        } else {
                            // 单包厢模式，重新加载页面
                            setTimeout(() => location.reload(), 2000);
                        }

                        // 恢复按钮状态
                        if (button) {
                            button.disabled = false;
                            button.innerHTML = originalText;
                        }
                    } else {
                        // 修复：显示具体的错误信息
                        const errorMessage = data.message || data.detail || '结束用餐失败';
                        console.error('结束用餐业务逻辑错误:', errorMessage);
                        showError(errorMessage);

                        // 恢复按钮状态
                        if (button) {
                            button.disabled = false;
                            button.innerHTML = originalText;
                        }
                    }
                }).catch(error => {
                    console.error('结束用餐请求失败:', error);
                    // 修复：更友好的错误提示
                    showError(`操作失败: ${error.message || '网络错误，请重试'}`);

                    // 恢复按钮状态
                    if (button) {
                        button.disabled = false;
                        button.innerHTML = originalText;
                    }
                });
            }
        }

        // 检查所有包厢的用餐状态
        function checkDiningStatusForAllRooms() {
            {% if user.assigned_tables %}
            const rooms = '{{ user.assigned_tables }}'.split(',');
            rooms.forEach(room => {
                checkDiningStatusForRoom(room.trim());
            });
            {% endif %}
        }

        // 检查特定包厢的用餐状态
        function checkDiningStatusForRoom(roomNumber) {
            fetch(`/waiter/check-dining-status/${encodeURIComponent(roomNumber)}`)
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById(`commandButtons-${roomNumber}`) ||
                                   document.getElementById('commandButtons');

                    if (container) {
                        // 如果用餐未开始，禁用除"开始用餐"外的所有按钮
                        if (!data.success || !data.dining_started) {
                            disableCommandButtonsInContainer(container, roomNumber);
                        } else {
                            enableCommandButtonsInContainer(container, roomNumber);
                        }
                    }
                })
                .catch(error => {
                    console.error('检查用餐状态失败:', error);
                });
        }



        // 启用指令按钮（旧版本，保持向后兼容）
        function enableCommandButtons(roomNumber) {
            const container = document.getElementById(`commandButtons-${roomNumber}`) ||
                            document.getElementById('commandButtons');

            if (container) {
                enableCommandButtonsInContainer(container, roomNumber);
            }
        }

        // 在指定容器中启用指令按钮
        function enableCommandButtonsInContainer(container, roomNumber) {
            const buttons = container.querySelectorAll('button');
            buttons.forEach(button => {
                button.disabled = false;
                button.classList.remove('disabled');
                button.style.opacity = '';
                button.style.cursor = '';
                button.title = '';

                // 恢复原始的onclick属性
                const originalOnclick = button.getAttribute('data-original-onclick');
                if (originalOnclick) {
                    button.setAttribute('onclick', originalOnclick);
                    button.removeAttribute('data-original-onclick');
                }

                // 移除禁用时添加的点击事件监听器
                if (button._preventClickHandler) {
                    button.removeEventListener('click', button._preventClickHandler, true);
                    button._preventClickHandler = null;
                }
            });

            // 移除提示信息
            const warning = container.querySelector('.dining-status-warning');
            if (warning) {
                warning.remove();
            }
        }

        // 自动刷新页面（使用系统配置）
        let pageRefreshInterval = null; // 存储刷新定时器

        function initAutoRefresh() {
            fetch('/api/system-config')
                .then(response => response.json())
                .then(config => {
                    const refreshEnabled = config.waiter_auto_refresh_enabled !== false;
                    const refreshIntervalSeconds = config.waiter_auto_refresh_interval || 15;

                    // 清除之前的定时器
                    if (pageRefreshInterval) {
                        clearInterval(pageRefreshInterval);
                        pageRefreshInterval = null;
                    }

                    // 检查是否禁用自动刷新（设置为0）
                    if (!refreshEnabled || refreshIntervalSeconds === 0) {
                        console.log('🔄 自动刷新已禁用（设置为0或disabled）');
                        return;
                    }

                    const refreshInterval = refreshIntervalSeconds * 1000;
                    console.log(`🔄 启用自动刷新，间隔: ${refreshIntervalSeconds}秒`);

                    pageRefreshInterval = setInterval(function() {
                        console.log('🔄 执行自动刷新...');
                        location.reload();
                    }, refreshInterval);
                })
                .catch(error => {
                    console.error('获取系统配置失败，禁用自动刷新:', error);
                    console.log('🔄 配置获取失败，自动刷新已禁用');
                    // 配置获取失败时不启用自动刷新，避免频繁刷新
                    // 清除可能存在的定时器
                    if (pageRefreshInterval) {
                        clearInterval(pageRefreshInterval);
                        pageRefreshInterval = null;
                    }
                });
        }

        // 动态更新自动刷新设置（供外部调用）
        function updateAutoRefreshSettings(enabled, intervalSeconds) {
            // 清除之前的定时器
            if (pageRefreshInterval) {
                clearInterval(pageRefreshInterval);
                pageRefreshInterval = null;
            }

            // 检查是否禁用自动刷新
            if (!enabled || intervalSeconds === 0) {
                console.log('🔄 自动刷新已禁用');
                return;
            }

            const refreshInterval = intervalSeconds * 1000;
            console.log(`🔄 更新自动刷新设置，间隔: ${intervalSeconds}秒`);

            pageRefreshInterval = setInterval(function() {
                console.log('🔄 执行自动刷新...');
                location.reload();
            }, refreshInterval);
        }

        // 初始化自动刷新
        initAutoRefresh();

        // 全局登录状态检查函数
        function checkLoginStatus() {
            fetch('/api/check-auth-status')
                .then(response => {
                    if (response.status === 401) {
                        console.warn('检测到登录状态过期');
                        if (confirm('登录状态已过期，是否刷新页面重新登录？')) {
                            window.location.reload();
                        }
                    }
                })
                .catch(error => {
                    console.error('登录状态检查失败:', error);
                });
        }

        // 定期检查登录状态（每5分钟检查一次）
        setInterval(checkLoginStatus, 5 * 60 * 1000);

        // 同步人数到所有界面
        function syncGuestCountToAllInterfaces(roomNumber, guestCount) {
            // 通过API更新订单的人数信息
            fetch('/api/sync-guest-count', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    room_number: roomNumber,
                    guest_count: guestCount
                })
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('人数信息已同步到所有界面');
                } else {
                    console.error('人数同步失败:', data.message);
                }
            })
            .catch(error => {
                console.error('人数同步请求失败:', error);
            });
        }

        // 更新菜品进度显示
        function updateDishProgress() {
            const dishItems = document.querySelectorAll('.dish-card');  // 修复：使用正确的CSS类名
            let totalDishes = 0;
            let processedDishes = 0;

            dishItems.forEach(item => {
                totalDishes++;
                // 检查菜品状态 - 检查已划菜和已确认上菜状态
                const hasServedBadge = item.querySelector('.badge.bg-success') &&
                                     item.textContent.includes('已划菜');
                const hasConfirmedStatus = item.textContent.includes('已确认上菜');
                const isServedClass = item.classList.contains('dish-served');

                if (hasServedBadge || hasConfirmedStatus || isServedClass) {
                    processedDishes++;
                }
            });

            const remainingDishes = totalDishes - processedDishes;

            // 更新进度显示
            const progressElement = document.getElementById('dishProgress');
            if (progressElement) {
                const progressText = progressElement.querySelector('.progress-text');
                const remainingText = progressElement.querySelector('.remaining-text');

                if (progressText) {
                    progressText.textContent = `菜品进度：${processedDishes}/${totalDishes}`;
                }

                if (remainingText) {
                    remainingText.textContent = `还有${remainingDishes}道菜未处理`;
                }

                // 根据进度调整颜色
                if (remainingDishes === 0 && totalDishes > 0) {
                    progressText.style.color = '#28a745'; // 绿色，全部完成
                } else if (remainingDishes <= 2) {
                    progressText.style.color = '#ffc107'; // 黄色，接近完成
                } else {
                    progressText.style.color = '#ffc107'; // 默认黄色
                }
            }
        }

        // 局部更新菜品项状态
        function updateDishItemStatus(itemId, status, roomNumber) {
            // 查找对应的菜品元素
            const dishElement = document.querySelector(`[data-dish-id="${itemId}"]`);
            if (dishElement) {
                // 更新菜品状态显示
                const statusElement = dishElement.querySelector('.dish-status');
                const actionsElement = dishElement.querySelector('.dish-actions');

                if (status === 'served') {
                    // 更新状态文本
                    if (statusElement) {
                        statusElement.innerHTML = '<span class="badge bg-info">已划菜</span>';
                    }

                    // 更新操作按钮
                    if (actionsElement) {
                        actionsElement.innerHTML = '<span class="badge bg-info">已划菜</span>';
                    }

                    // 添加已划菜的样式
                    dishElement.classList.add('dish-served');
                } else if (status === 'confirmed') {
                    // 更新状态文本
                    if (statusElement) {
                        statusElement.innerHTML = '<span class="badge bg-success">已确认</span>';
                    }

                    // 更新操作按钮
                    if (actionsElement) {
                        actionsElement.innerHTML = '<span class="badge bg-success">已确认</span>';
                    }

                    // 添加已确认的样式
                    dishElement.classList.add('dish-confirmed');
                }
            }
        }

        // 在划菜操作后更新进度
        function markDishAndUpdateProgress(itemId, status) {
            markDish(itemId, status);
            // 延迟更新进度，等待页面刷新
            setTimeout(updateDishProgress, 500);
        }

        // 初始化按钮状态
        function initializeButtonStates() {
            // 获取当前包厢号
            const roomNumber = getCurrentRoomNumber();
            if (!roomNumber) {
                console.log('未找到包厢号，跳过按钮状态初始化');
                return;
            }

            // 获取按钮容器
            const container = document.getElementById(`commandButtons-${roomNumber}`) ||
                            document.getElementById('commandButtons');

            if (!container) {
                console.log('未找到按钮容器，跳过按钮状态初始化');
                return;
            }

            // 检查用餐状态并设置按钮状态
            fetch(`/waiter/check-dining-status/${encodeURIComponent(roomNumber)}`)
                .then(response => response.json())
                .then(data => {
                    console.log('用餐状态检查结果:', data);

                    if (data.dining_started) {
                        // 用餐已开始，启用所有按钮
                        enableCommandButtonsInContainer(container, roomNumber);
                        console.log('用餐已开始，启用所有操作按钮');
                    } else {
                        // 用餐未开始，禁用除开始用餐外的所有按钮
                        disableCommandButtonsInContainer(container, roomNumber);
                        console.log('用餐未开始，禁用操作按钮');
                    }
                })
                .catch(error => {
                    console.error('检查用餐状态失败:', error);
                    // 出错时默认禁用按钮
                    disableCommandButtonsInContainer(container, roomNumber);
                });
        }

        // 获取当前包厢号
        function getCurrentRoomNumber() {
            // 方法1：从用户分配的包厢中获取第一个
            {% if user.assigned_tables %}
            const assignedRooms = '{{ user.assigned_tables }}'.split(',');
            if (assignedRooms.length > 0) {
                return assignedRooms[0].trim();
            }
            {% endif %}

            // 方法2：从页面中提取包厢号
            const roomElements = document.querySelectorAll('[data-room]');
            if (roomElements.length > 0) {
                return roomElements[0].getAttribute('data-room');
            }

            // 方法3：从页面标题或其他元素获取
            const titleElement = document.querySelector('.card-header h5, .card-header h6');
            if (titleElement) {
                const titleText = titleElement.textContent;
                const match = titleText.match(/(\S+)包厢/);
                if (match) {
                    return match[1];
                }
            }

            // 方法4：从URL参数获取
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get('room') || null;
        }

        // 防止页面缩放
        document.addEventListener('touchstart', function(event) {
            if (event.touches.length > 1) {
                event.preventDefault();
            }
        });

        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>
</html>
