<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打荷员菜品完成通知调试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .log-area { background: #f8f9fa; padding: 10px; border-radius: 3px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>🔧 打荷员菜品完成通知调试工具</h1>
    
    <div class="test-section">
        <h3>1. 测试发送菜品完成通知</h3>
        <button class="test-button" onclick="testSendNotification()">发送测试通知</button>
        <button class="test-button" onclick="testSendMultipleNotifications()">发送多个通知</button>
    </div>
    
    <div class="test-section">
        <h3>2. 测试获取通知列表</h3>
        <button class="test-button" onclick="testGetNotifications()">获取通知列表</button>
        <button class="test-button" onclick="startPolling()">开始轮询</button>
        <button class="test-button" onclick="stopPolling()">停止轮询</button>
    </div>
    
    <div class="test-section">
        <h3>3. 测试WebSocket通知</h3>
        <button class="test-button" onclick="testWebSocketMessage()">模拟WebSocket消息</button>
    </div>
    
    <div class="test-section">
        <h3>调试日志</h3>
        <div id="logArea" class="log-area"></div>
        <button class="test-button" onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let pollingInterval = null;
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logArea.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }
        
        // 测试发送菜品完成通知
        async function testSendNotification() {
            try {
                log('🧪 开始测试发送菜品完成通知...');
                
                const testData = {
                    type: 'dish_completion',
                    message: '测试包厢测试菜品，跑菜',
                    roomName: '测试包厢',
                    dishName: '测试菜品',
                    timestamp: new Date().toISOString()
                };
                
                log(`📤 发送数据: ${JSON.stringify(testData)}`);
                
                const response = await fetch('/api/kitchen/dish-completion-notification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify(testData)
                });
                
                log(`📡 响应状态: ${response.status}`);
                
                if (response.ok) {
                    const result = await response.json();
                    log(`✅ 通知发送成功: ${JSON.stringify(result)}`, 'success');
                } else {
                    const error = await response.text();
                    log(`❌ 通知发送失败: ${error}`, 'error');
                }
            } catch (error) {
                log(`❌ 发送通知异常: ${error.message}`, 'error');
            }
        }
        
        // 测试发送多个通知
        async function testSendMultipleNotifications() {
            const rooms = ['A包厢', 'B包厢', 'C包厢'];
            const dishes = ['宫保鸡丁', '麻婆豆腐', '红烧肉'];
            
            for (let i = 0; i < 3; i++) {
                await testSendSingleNotification(rooms[i], dishes[i]);
                await new Promise(resolve => setTimeout(resolve, 1000)); // 延迟1秒
            }
        }
        
        async function testSendSingleNotification(roomName, dishName) {
            try {
                const testData = {
                    type: 'dish_completion',
                    message: `${roomName}${dishName}，跑菜`,
                    roomName: roomName,
                    dishName: dishName,
                    timestamp: new Date().toISOString()
                };
                
                const response = await fetch('/api/kitchen/dish-completion-notification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify(testData)
                });
                
                if (response.ok) {
                    log(`✅ ${roomName}${dishName} 通知发送成功`, 'success');
                } else {
                    log(`❌ ${roomName}${dishName} 通知发送失败`, 'error');
                }
            } catch (error) {
                log(`❌ ${roomName}${dishName} 发送异常: ${error.message}`, 'error');
            }
        }
        
        // 测试获取通知列表
        async function testGetNotifications() {
            try {
                log('🔍 开始获取通知列表...');
                
                const response = await fetch('/api/kitchen/dish-completion-notifications', {
                    credentials: 'include'
                });
                
                log(`📡 响应状态: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`📋 获取到 ${data.notifications ? data.notifications.length : 0} 条通知`, 'success');
                    
                    if (data.notifications && data.notifications.length > 0) {
                        data.notifications.forEach((notification, index) => {
                            log(`  ${index + 1}. ID:${notification.id} - ${notification.message} (${notification.timestamp})`);
                        });
                    } else {
                        log('📭 暂无通知');
                    }
                } else {
                    const error = await response.text();
                    log(`❌ 获取通知失败: ${error}`, 'error');
                }
            } catch (error) {
                log(`❌ 获取通知异常: ${error.message}`, 'error');
            }
        }
        
        // 开始轮询
        function startPolling() {
            if (pollingInterval) {
                log('⚠️ 轮询已在运行中');
                return;
            }
            
            log('🔄 开始轮询通知...');
            pollingInterval = setInterval(async () => {
                try {
                    const response = await fetch('/api/kitchen/dish-completion-notifications', {
                        credentials: 'include'
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        if (data.notifications && data.notifications.length > 0) {
                            log(`🔔 轮询发现 ${data.notifications.length} 条通知`);
                        }
                    }
                } catch (error) {
                    log(`❌ 轮询异常: ${error.message}`, 'error');
                }
            }, 3000);
        }
        
        // 停止轮询
        function stopPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
                log('⏹️ 轮询已停止');
            } else {
                log('⚠️ 轮询未在运行');
            }
        }
        
        // 测试WebSocket消息
        function testWebSocketMessage() {
            log('🌐 模拟WebSocket消息处理...');
            
            const mockMessage = {
                type: 'dish_completion_from_helper',
                room_number: '测试包厢',
                dish_name: '测试菜品',
                message: '测试包厢测试菜品，跑菜',
                timestamp: Date.now()
            };
            
            log(`📨 模拟消息: ${JSON.stringify(mockMessage)}`);
            
            // 如果厨房大屏页面的handleWebSocketMessage函数存在，调用它
            if (typeof handleWebSocketMessage === 'function') {
                handleWebSocketMessage(mockMessage);
                log('✅ WebSocket消息处理完成', 'success');
            } else {
                log('⚠️ handleWebSocketMessage函数不存在，请在厨房大屏页面测试');
            }
        }
        
        // 页面加载时的初始化
        window.onload = function() {
            log('🚀 调试工具已加载');
            log('💡 使用说明：');
            log('1. 点击"发送测试通知"测试通知发送功能');
            log('2. 点击"获取通知列表"查看当前通知');
            log('3. 点击"开始轮询"模拟厨房大屏的轮询机制');
            log('4. 在厨房大屏页面打开此工具进行完整测试');
        };
        
        // 页面卸载时清理
        window.onbeforeunload = function() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
            }
        };
    </script>
</body>
</html>
