<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端语音播报测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #e9ecef;
        }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 移动端语音播报测试</h1>
        
        <div id="deviceInfo" class="status">
            <h3>设备信息</h3>
            <p id="deviceDetails">检测中...</p>
        </div>
        
        <div id="voiceStatus" class="status">
            <h3>语音API状态</h3>
            <p id="voiceDetails">检测中...</p>
        </div>
        
        <button class="test-button" onclick="initVoice()">
            🎵 初始化语音API（需要用户交互）
        </button>
        
        <button class="test-button" onclick="testBasicVoice()">
            🔊 测试基础语音播报
        </button>
        
        <button class="test-button" onclick="testKitchenVoice()">
            🍽️ 测试厨房指令语音
        </button>
        
        <button class="test-button" onclick="testQueueVoice()">
            📋 测试语音队列播报
        </button>
        
        <div id="testResults" class="status">
            <h3>测试结果</h3>
            <div id="results">等待测试...</div>
        </div>
    </div>

    <script>
        let isMobileDevice = false;
        let voiceInitialized = false;
        let testResults = [];

        // 检测设备信息
        function detectDevice() {
            isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const hasVoiceSupport = 'speechSynthesis' in window;
            
            document.getElementById('deviceDetails').innerHTML = `
                移动设备: ${isMobileDevice ? '是' : '否'}<br>
                用户代理: ${navigator.userAgent}<br>
                语音API支持: ${hasVoiceSupport ? '是' : '否'}
            `;
            
            updateVoiceStatus();
        }

        // 更新语音状态
        function updateVoiceStatus() {
            if ('speechSynthesis' in window) {
                document.getElementById('voiceDetails').innerHTML = `
                    语音初始化: ${voiceInitialized ? '是' : '否'}<br>
                    语音合成器暂停: ${speechSynthesis.paused}<br>
                    正在播报: ${speechSynthesis.speaking}<br>
                    待播报队列: ${speechSynthesis.pending}
                `;
            } else {
                document.getElementById('voiceDetails').innerHTML = '❌ 浏览器不支持语音合成API';
            }
        }

        // 初始化语音API
        function initVoice() {
            addResult('🎵 开始初始化语音API...');
            
            if (!('speechSynthesis' in window)) {
                addResult('❌ 浏览器不支持语音合成API', 'error');
                return;
            }

            try {
                // 播放一个静音的语音来激活API
                const utterance = new SpeechSynthesisUtterance('');
                utterance.volume = 0;
                utterance.onstart = function() {
                    voiceInitialized = true;
                    addResult('✅ 语音API初始化成功', 'success');
                    updateVoiceStatus();
                };
                utterance.onerror = function(event) {
                    addResult(`❌ 语音API初始化失败: ${event.error}`, 'error');
                };
                speechSynthesis.speak(utterance);
            } catch (error) {
                addResult(`❌ 语音API初始化异常: ${error.message}`, 'error');
            }
        }

        // 测试基础语音播报
        function testBasicVoice() {
            addResult('🔊 测试基础语音播报...');
            
            if (!voiceInitialized && isMobileDevice) {
                addResult('⚠️ 移动设备需要先初始化语音API', 'warning');
                return;
            }

            try {
                const utterance = new SpeechSynthesisUtterance('这是一个基础语音播报测试');
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                utterance.volume = 1.0;
                
                utterance.onstart = function() {
                    addResult('✅ 基础语音播报开始', 'success');
                };
                utterance.onend = function() {
                    addResult('✅ 基础语音播报完成', 'success');
                };
                utterance.onerror = function(event) {
                    addResult(`❌ 基础语音播报错误: ${event.error}`, 'error');
                };
                
                speechSynthesis.speak(utterance);
            } catch (error) {
                addResult(`❌ 基础语音播报异常: ${error.message}`, 'error');
            }
        }

        // 测试厨房指令语音
        function testKitchenVoice() {
            addResult('🍽️ 测试厨房指令语音...');
            
            const messages = [
                '1号包厢开始用餐',
                '2号包厢宫保鸡丁跑菜',
                '3号包厢催菜',
                '4号包厢用餐结束'
            ];
            
            messages.forEach((message, index) => {
                setTimeout(() => {
                    testSingleVoice(message, `厨房指令${index + 1}`);
                }, index * 1000);
            });
        }

        // 测试语音队列播报
        function testQueueVoice() {
            addResult('📋 测试语音队列播报...');
            
            const queueMessages = [
                '队列测试第一条',
                '队列测试第二条',
                '队列测试第三条'
            ];
            
            queueMessages.forEach((message, index) => {
                testSingleVoice(message, `队列${index + 1}`);
            });
        }

        // 测试单个语音播报
        function testSingleVoice(text, label) {
            try {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                utterance.volume = 1.0;
                
                utterance.onstart = function() {
                    addResult(`🔊 ${label}播报开始: ${text}`);
                };
                utterance.onend = function() {
                    addResult(`✅ ${label}播报完成: ${text}`, 'success');
                };
                utterance.onerror = function(event) {
                    addResult(`❌ ${label}播报错误: ${event.error}`, 'error');
                };
                
                speechSynthesis.speak(utterance);
            } catch (error) {
                addResult(`❌ ${label}播报异常: ${error.message}`, 'error');
            }
        }

        // 添加测试结果
        function addResult(message, type = '') {
            const timestamp = new Date().toLocaleTimeString();
            const resultDiv = document.getElementById('results');
            const resultItem = document.createElement('div');
            resultItem.className = type;
            resultItem.innerHTML = `[${timestamp}] ${message}`;
            resultDiv.appendChild(resultItem);
            resultDiv.scrollTop = resultDiv.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            detectDevice();
            addResult('📱 移动端语音播报测试页面已加载');
            
            // 定期更新语音状态
            setInterval(updateVoiceStatus, 2000);
        });
    </script>
</body>
</html>
