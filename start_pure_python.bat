@echo off
chcp 65001 >nul
title 暨阳湖大酒店传菜管理系统 (纯Python版本)

echo ==========================================
echo 暨阳湖大酒店传菜管理系统 (纯Python版本)
echo ==========================================

REM 检查 Python 版本
echo 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装 Python 3.9+
    pause
    exit /b 1
)

echo.
echo 选择操作：
echo 1. 完整启动 (初始化数据库 + 启动服务)
echo 2. 仅启动服务
echo 3. 仅初始化数据库
echo 4. 安装依赖
echo 5. 重置数据库
set /p choice=请选择 (1-5): 

if "%choice%"=="1" goto full_start
if "%choice%"=="2" goto start_only
if "%choice%"=="3" goto init_db_only
if "%choice%"=="4" goto install_deps
if "%choice%"=="5" goto reset_db
echo 无效选择
pause
exit /b 1

:full_start
echo 完整启动系统...

REM 进入纯Python版本目录
cd backend_pure_python

REM 创建虚拟环境
echo 创建Python虚拟环境...
python -m venv venv 2>nul
call venv\Scripts\activate.bat

REM 升级pip
echo 升级pip...
pip install --upgrade pip

REM 安装依赖
echo 安装Python依赖...
pip install fastapi uvicorn sqlalchemy python-jose passlib python-multipart pydantic pydantic-settings jinja2 aiofiles bcrypt cryptography

REM 初始化数据库
echo 初始化数据库...
python init_db.py

REM 启动服务
echo 启动服务...
echo.
echo ✅ 系统启动成功！
echo 🌐 访问地址: http://localhost:8000
echo 📚 API 文档: http://localhost:8000/docs
echo.
echo 默认登录账号:
echo 管理员: admin / admin123
echo 经理: manager01 / manager123
echo 服务员: waiter01 / waiter123
echo 厨师长: chef01 / chef123
echo.
echo 按 Ctrl+C 停止服务
echo.

python main.py
goto :eof

:start_only
echo 启动服务...
cd backend_pure_python
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python main.py
goto :eof

:init_db_only
echo 初始化数据库...
cd backend_pure_python
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python init_db.py
echo ✅ 数据库初始化完成
pause
goto :eof

:install_deps
echo 安装依赖...
cd backend_pure_python
python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
echo ✅ 依赖安装完成
pause
goto :eof

:reset_db
echo 重置数据库...
cd backend_pure_python

REM 删除数据库文件
if exist "paocai.db" (
    del paocai.db
    echo 删除旧数据库文件
)

python -m venv venv 2>nul
call venv\Scripts\activate.bat
pip install -r requirements.txt
python init_db.py
echo ✅ 数据库重置完成
pause
goto :eof
