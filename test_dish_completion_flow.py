#!/usr/bin/env python3
"""
测试打荷员菜品完成通知流程
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_DATA = [
    {"roomName": "A包厢", "dishName": "宫保鸡丁"},
    {"roomName": "B包厢", "dishName": "麻婆豆腐"},
    {"roomName": "C包厢", "dishName": "红烧肉"},
]

async def test_send_notification(session, room_name, dish_name):
    """测试发送菜品完成通知"""
    url = f"{BASE_URL}/api/kitchen/dish-completion-notification"
    
    data = {
        "type": "dish_completion",
        "message": f"{room_name}{dish_name}，跑菜",
        "roomName": room_name,
        "dishName": dish_name,
        "timestamp": datetime.now().isoformat()
    }
    
    print(f"🧪 测试发送通知: {room_name}{dish_name}")
    print(f"📤 发送数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        async with session.post(url, json=data) as response:
            print(f"📡 响应状态: {response.status}")
            
            if response.status == 200:
                result = await response.json()
                print(f"✅ 发送成功: {result}")
                return True
            else:
                error_text = await response.text()
                print(f"❌ 发送失败: {error_text}")
                return False
                
    except Exception as e:
        print(f"❌ 发送异常: {e}")
        return False

async def test_get_notifications(session):
    """测试获取菜品完成通知"""
    url = f"{BASE_URL}/api/kitchen/dish-completion-notifications"
    
    print(f"🔍 测试获取通知列表")
    
    try:
        async with session.get(url) as response:
            print(f"📡 响应状态: {response.status}")
            
            if response.status == 200:
                result = await response.json()
                print(f"📋 获取成功:")
                print(f"   - success: {result.get('success')}")
                print(f"   - 通知数量: {len(result.get('notifications', []))}")
                
                for i, notification in enumerate(result.get('notifications', [])):
                    print(f"   - 通知{i+1}: ID={notification.get('id')}, "
                          f"包厢={notification.get('room_number')}, "
                          f"菜品={notification.get('dish_name')}, "
                          f"消息={notification.get('message')}")
                
                return result.get('notifications', [])
            else:
                error_text = await response.text()
                print(f"❌ 获取失败: {error_text}")
                return []
                
    except Exception as e:
        print(f"❌ 获取异常: {e}")
        return []

async def test_complete_flow():
    """测试完整的通知流程"""
    print("🚀 开始测试打荷员菜品完成通知流程")
    print("=" * 60)
    
    async with aiohttp.ClientSession() as session:
        # 1. 先获取当前通知列表（基线）
        print("\n📋 步骤1: 获取当前通知列表（基线）")
        baseline_notifications = await test_get_notifications(session)
        baseline_count = len(baseline_notifications)
        print(f"📊 基线通知数量: {baseline_count}")
        
        # 2. 发送测试通知
        print("\n📤 步骤2: 发送测试通知")
        success_count = 0
        for test_item in TEST_DATA:
            success = await test_send_notification(session, test_item["roomName"], test_item["dishName"])
            if success:
                success_count += 1
            await asyncio.sleep(1)  # 延迟1秒
        
        print(f"📊 发送结果: {success_count}/{len(TEST_DATA)} 成功")
        
        # 3. 等待一段时间，然后获取通知列表
        print("\n⏳ 步骤3: 等待3秒后获取通知列表")
        await asyncio.sleep(3)
        
        new_notifications = await test_get_notifications(session)
        new_count = len(new_notifications)
        print(f"📊 新通知数量: {new_count}")
        
        # 4. 验证结果
        print("\n✅ 步骤4: 验证结果")
        expected_count = baseline_count + success_count
        if new_count >= expected_count:
            print(f"🎉 测试成功! 通知数量从 {baseline_count} 增加到 {new_count}")
            
            # 显示新增的通知
            if new_count > baseline_count:
                print("📋 新增通知:")
                for notification in new_notifications[-success_count:]:
                    print(f"   - {notification.get('room_number')}{notification.get('dish_name')}: {notification.get('message')}")
        else:
            print(f"❌ 测试失败! 期望通知数量 >= {expected_count}, 实际 {new_count}")
        
        # 5. 测试轮询机制
        print("\n🔄 步骤5: 测试轮询机制（模拟厨房大屏）")
        for i in range(3):
            print(f"   轮询 {i+1}/3:")
            notifications = await test_get_notifications(session)
            print(f"   获取到 {len(notifications)} 条通知")
            await asyncio.sleep(2)

async def test_single_notification():
    """测试单个通知"""
    print("🧪 测试单个通知")
    
    async with aiohttp.ClientSession() as session:
        # 发送一个测试通知
        room_name = f"测试包厢{int(time.time())}"
        dish_name = "测试菜品"
        
        success = await test_send_notification(session, room_name, dish_name)
        
        if success:
            print("⏳ 等待2秒后检查...")
            await asyncio.sleep(2)
            
            notifications = await test_get_notifications(session)
            
            # 查找我们刚发送的通知
            found = False
            for notification in notifications:
                if (notification.get('room_number') == room_name and 
                    notification.get('dish_name') == dish_name):
                    print(f"✅ 找到通知: {notification}")
                    found = True
                    break
            
            if not found:
                print(f"❌ 未找到通知: {room_name}{dish_name}")
        else:
            print("❌ 发送通知失败")

def main():
    """主函数"""
    print("🔧 打荷员菜品完成通知测试工具")
    print("请确保后端服务正在运行 (http://localhost:8000)")
    print()
    
    choice = input("选择测试类型:\n1. 完整流程测试\n2. 单个通知测试\n请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        asyncio.run(test_complete_flow())
    elif choice == "2":
        asyncio.run(test_single_notification())
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
