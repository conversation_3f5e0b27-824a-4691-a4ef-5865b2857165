#!/usr/bin/env python3
"""
退出登录功能和厨房大屏权限测试脚本
测试内容：
1. 退出登录功能是否正常工作
2. 厨房大屏权限重构是否生效
3. 新的KITCHEN_DISPLAY角色权限验证
"""

import requests
import json
import time
from datetime import datetime

# 测试服务器配置
BASE_URL = "http://localhost:5109"
TEST_USERS = {
    "admin": {"username": "admin", "password": "admin123"},
    "manager": {"username": "manager", "password": "manager123"},
    "chef_manager": {"username": "chef01", "password": "chef123"},
    "kitchen_helper": {"username": "helper01", "password": "helper123"},
    "business_center": {"username": "business01", "password": "business123"},
    "kitchen_display": {"username": "kitchen_display", "password": "kitchen123"}
}

class LogoutPermissionTester:
    def __init__(self):
        self.session = requests.Session()
        self.results = []
    
    def log(self, message, level="INFO"):
        """记录测试日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
        self.results.append(f"{level}: {message}")
    
    def test_login(self, username, password):
        """测试登录功能"""
        try:
            response = self.session.post(
                f"{BASE_URL}/login",
                data={"username": username, "password": password},
                allow_redirects=False
            )
            
            if response.status_code in [200, 302]:
                self.log(f"✅ 用户 {username} 登录成功")
                return True
            else:
                self.log(f"❌ 用户 {username} 登录失败: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"❌ 登录请求失败: {e}", "ERROR")
            return False
    
    def test_logout(self):
        """测试退出登录功能"""
        try:
            # 测试POST /logout
            response = self.session.post(f"{BASE_URL}/logout")
            if response.status_code == 200:
                self.log("✅ POST /logout 响应正常")
            else:
                self.log(f"⚠️ POST /logout 响应异常: {response.status_code}", "WARN")
            
            # 测试GET /logout
            response = self.session.get(f"{BASE_URL}/logout")
            if response.status_code in [200, 302]:
                self.log("✅ GET /logout 响应正常")
            else:
                self.log(f"⚠️ GET /logout 响应异常: {response.status_code}", "WARN")
            
            # 清除所有cookies
            self.session.cookies.clear()
            self.log("✅ 会话cookies已清除")
            
            return True
        except Exception as e:
            self.log(f"❌ 退出登录测试失败: {e}", "ERROR")
            return False
    
    def test_kitchen_display_access(self, username, should_have_access=False):
        """测试厨房大屏访问权限"""
        try:
            # 测试厨房大屏页面访问
            response = self.session.get(f"{BASE_URL}/kitchen/display", allow_redirects=False)
            
            if should_have_access:
                if response.status_code == 200:
                    self.log(f"✅ {username} 可以访问厨房大屏页面")
                    return True
                else:
                    self.log(f"❌ {username} 应该能访问厨房大屏但被拒绝: {response.status_code}", "ERROR")
                    return False
            else:
                if response.status_code == 403:
                    self.log(f"✅ {username} 正确被拒绝访问厨房大屏")
                    return True
                elif response.status_code == 302:
                    self.log(f"✅ {username} 被重定向（可能是登录页面）")
                    return True
                else:
                    self.log(f"❌ {username} 不应该能访问厨房大屏但获得了访问: {response.status_code}", "ERROR")
                    return False
        except Exception as e:
            self.log(f"❌ 厨房大屏访问测试失败: {e}", "ERROR")
            return False
    
    def test_kitchen_display_api(self, username, should_have_access=False):
        """测试厨房大屏API权限"""
        apis_to_test = [
            "/api/kitchen-display-config",
            "/api/kitchen-display/waiter-actions",
            "/api/dish-ready/latest"
        ]
        
        success_count = 0
        for api in apis_to_test:
            try:
                response = self.session.get(f"{BASE_URL}{api}")
                
                if should_have_access:
                    if response.status_code == 200:
                        self.log(f"✅ {username} 可以访问 {api}")
                        success_count += 1
                    else:
                        self.log(f"❌ {username} 应该能访问 {api} 但被拒绝: {response.status_code}", "ERROR")
                else:
                    if response.status_code in [401, 403]:
                        self.log(f"✅ {username} 正确被拒绝访问 {api}")
                        success_count += 1
                    else:
                        self.log(f"❌ {username} 不应该能访问 {api} 但获得了访问: {response.status_code}", "ERROR")
            except Exception as e:
                self.log(f"❌ API {api} 测试失败: {e}", "ERROR")
        
        return success_count == len(apis_to_test)
    
    def test_auto_relogin_prevention(self, username, password):
        """测试自动重新登录防护"""
        try:
            # 1. 登录
            if not self.test_login(username, password):
                return False

            # 2. 访问主页，应该重定向到dashboard
            response = self.session.get(f"{BASE_URL}/", allow_redirects=False)
            if response.status_code == 302:
                self.log(f"✅ 登录后访问主页正确重定向")

            # 3. 退出登录
            self.test_logout()

            # 4. 再次访问主页，应该重定向到登录页面
            response = self.session.get(f"{BASE_URL}/", allow_redirects=False)
            if response.status_code == 302 and 'login' in response.headers.get('Location', ''):
                self.log(f"✅ 退出登录后访问主页正确重定向到登录页面")
                return True
            else:
                self.log(f"❌ 退出登录后仍然可以访问主页: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"❌ 自动重新登录防护测试失败: {e}", "ERROR")
            return False

    def test_user_management_roles(self):
        """测试用户管理界面是否包含厨房大屏角色选项"""
        try:
            response = self.session.get(f"{BASE_URL}/users")
            if response.status_code == 200:
                content = response.text
                if 'kitchen_display' in content and '厨房大屏用户' in content:
                    self.log("✅ 用户管理界面包含厨房大屏角色选项")
                    return True
                else:
                    self.log("❌ 用户管理界面缺少厨房大屏角色选项", "ERROR")
                    return False
            else:
                self.log(f"❌ 无法访问用户管理页面: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"❌ 用户管理界面测试失败: {e}", "ERROR")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("🚀 开始退出登录和权限测试...")
        
        # 测试1: 退出登录功能
        self.log("\n📋 测试1: 退出登录功能")
        admin_login = self.test_login("admin", "admin123")
        if admin_login:
            logout_success = self.test_logout()
            auto_relogin_prevention = self.test_auto_relogin_prevention("admin", "admin123")
        
        # 测试2: 厨房大屏权限重构
        self.log("\n📋 测试2: 厨房大屏权限重构")

        # 定义权限期望
        permission_expectations = {
            "admin": True,  # 管理员应该有权限
            "kitchen_display": True,  # 厨房大屏用户应该有权限
            "manager": False,  # 餐饮经理不应该有权限
            "chef_manager": False,  # 厨师长不应该有权限
            "kitchen_helper": False,  # 打荷员不应该有权限
            "business_center": False  # 商务中心不应该有权限
        }

        # 测试3: 用户管理界面厨房大屏角色选项
        self.log("\n📋 测试3: 用户管理界面角色选项")
        admin_login_for_mgmt = self.test_login("admin", "admin123")
        if admin_login_for_mgmt:
            self.user_mgmt_test_result = self.test_user_management_roles()
            self.test_logout()
        else:
            self.user_mgmt_test_result = False
        
        permission_test_results = {}
        
        for role, should_have_access in permission_expectations.items():
            if role in TEST_USERS:
                user_info = TEST_USERS[role]
                self.log(f"\n🔍 测试角色: {role}")
                
                # 登录
                login_success = self.test_login(user_info["username"], user_info["password"])
                if login_success:
                    # 测试页面访问
                    page_access = self.test_kitchen_display_access(role, should_have_access)
                    # 测试API访问
                    api_access = self.test_kitchen_display_api(role, should_have_access)
                    
                    permission_test_results[role] = page_access and api_access
                    
                    # 退出登录
                    self.test_logout()
                else:
                    permission_test_results[role] = False
        
        # 测试总结
        self.log("\n📊 测试总结:")
        self.log("=" * 50)

        # 退出登录测试结果
        logout_test_passed = admin_login and logout_success and auto_relogin_prevention
        if logout_test_passed:
            self.log("✅ 退出登录功能测试: 通过")
        else:
            self.log("❌ 退出登录功能测试: 失败")

        # 权限测试结果
        passed_permission_tests = sum(1 for result in permission_test_results.values() if result)
        total_permission_tests = len(permission_test_results)

        permission_test_passed = passed_permission_tests == total_permission_tests
        if permission_test_passed:
            self.log(f"✅ 厨房大屏权限测试: {passed_permission_tests}/{total_permission_tests} 通过")
        else:
            self.log(f"❌ 厨房大屏权限测试: {passed_permission_tests}/{total_permission_tests} 通过")

        for role, result in permission_test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            expected = "有权限" if permission_expectations[role] else "无权限"
            self.log(f"  - {role} ({expected}): {status}")

        # 用户管理界面测试结果
        user_mgmt_test_passed = True  # 假设通过，实际结果在run_all_tests中设置
        if hasattr(self, 'user_mgmt_test_result'):
            user_mgmt_test_passed = self.user_mgmt_test_result

        if user_mgmt_test_passed:
            self.log("✅ 用户管理界面角色选项测试: 通过")
        else:
            self.log("❌ 用户管理界面角色选项测试: 失败")

        # 整体测试结果
        overall_success = (
            logout_test_passed and permission_test_passed and user_mgmt_test_passed
        )

        if overall_success:
            self.log("\n🎉 所有测试通过！")
        else:
            self.log("\n⚠️ 部分测试失败，请检查相关功能")

        return overall_success

def main():
    """主函数"""
    print("🔧 退出登录功能和厨房大屏权限测试")
    print("=" * 60)
    
    tester = LogoutPermissionTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ 测试完成：所有功能正常")
    else:
        print("\n❌ 测试完成：发现问题需要修复")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
