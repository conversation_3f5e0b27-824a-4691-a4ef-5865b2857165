<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        .log-area {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
        }
        
        /* 通知样式 */
        .notification-center {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            pointer-events: none;
            width: 80%;
            max-width: 800px;
        }

        .notification-item {
            background: rgba(0, 0, 0, 0.9);
            border: 3px solid #ffc107;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 20px;
            text-align: center;
            font-size: 2.5rem;
            font-weight: bold;
            color: #ffc107;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            box-shadow: 0 8px 32px rgba(255, 193, 7, 0.3);
            animation: slideIn 0.5s ease-out;
            position: relative;
            overflow: hidden;
        }

        .notification-item.dish-completion {
            border-color: #ff6b35;
            color: #ff6b35;
            box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);
        }

        .notification-content {
            position: relative;
            z-index: 2;
        }

        .notification-room {
            font-size: 1.8rem;
            margin-bottom: 10px;
            opacity: 0.8;
        }

        .notification-message {
            font-size: 3rem;
            line-height: 1.2;
        }

        .notification-type {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 1rem;
            opacity: 0.7;
            background: rgba(255,255,255,0.1);
            padding: 5px 10px;
            border-radius: 20px;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .notification-item.fade-out {
            animation: fadeOut 0.5s ease-in forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: scale(1);
            }
            to {
                opacity: 0;
                transform: scale(0.8);
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 厨房大屏通知显示测试</h1>
        
        <div>
            <button class="test-button" onclick="testNotificationDisplay()">测试通知显示</button>
            <button class="test-button" onclick="testApiPolling()">测试API轮询</button>
            <button class="test-button" onclick="clearLogs()">清空日志</button>
        </div>
        
        <div class="log-area" id="logArea"></div>
    </div>

    <script>
        let processedNotifications = new Set();
        let currentNotificationElement = null;
        
        function log(message) {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            logArea.innerHTML += `[${timestamp}] ${message}\n`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLogs() {
            document.getElementById('logArea').innerHTML = '';
        }
        
        // 测试通知显示功能
        function testNotificationDisplay() {
            log('🧪 开始测试通知显示...');
            
            const testNotification = {
                id: Date.now(),
                room_number: '测试包厢',
                dish_name: '测试菜品',
                message: '测试包厢测试菜品，跑菜'
            };
            
            showDishCompletionNotification(testNotification);
            log(`✅ 测试通知已显示: ${testNotification.message}`);
        }
        
        // 测试API轮询功能
        function testApiPolling() {
            log('🔍 开始测试API轮询...');
            checkDishCompletionNotifications();
        }
        
        // 显示菜品完成通知
        function showDishCompletionNotification(notification) {
            log(`📢 显示通知: ${notification.message}`);
            
            // 创建通知容器（如果不存在）
            let notificationCenter = document.getElementById('notificationCenter');
            if (!notificationCenter) {
                notificationCenter = document.createElement('div');
                notificationCenter.id = 'notificationCenter';
                notificationCenter.className = 'notification-center';
                document.body.appendChild(notificationCenter);
                log('📦 创建通知中心容器');
            }
            
            // 清除之前的通知
            if (currentNotificationElement && currentNotificationElement.parentNode) {
                currentNotificationElement.parentNode.removeChild(currentNotificationElement);
                log('🗑️ 清除之前的通知');
            }
            
            // 创建通知元素
            const notificationElement = document.createElement('div');
            notificationElement.className = 'notification-item dish-completion';
            currentNotificationElement = notificationElement;
            
            notificationElement.innerHTML = `
                <div class="notification-content">
                    <div class="notification-room">${notification.room_number}</div>
                    <div class="notification-message">${notification.dish_name}跑菜</div>
                    <div class="notification-type">打荷完成</div>
                </div>
            `;
            
            // 添加到通知中心
            notificationCenter.appendChild(notificationElement);
            log('📌 通知已添加到DOM');
            
            // 自动移除通知
            setTimeout(() => {
                log('⏰ 开始淡出通知');
                notificationElement.classList.add('fade-out');
                setTimeout(() => {
                    if (notificationElement.parentNode) {
                        notificationElement.parentNode.removeChild(notificationElement);
                        log('🗑️ 通知已从DOM移除');
                        if (currentNotificationElement === notificationElement) {
                            currentNotificationElement = null;
                        }
                    }
                }, 500);
            }, 4000);
        }
        
        // 检查打荷员菜品完成通知
        function checkDishCompletionNotifications() {
            fetch('/api/kitchen/dish-completion-notifications', {
                credentials: 'include'
            })
                .then(response => {
                    log(`🔍 API响应状态: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    log(`📡 API返回数据: ${JSON.stringify(data)}`);
                    
                    if (data.success && data.notifications && data.notifications.length > 0) {
                        log(`📋 收到 ${data.notifications.length} 条菜品完成通知`);
                        
                        data.notifications.forEach(notification => {
                            log(`🔍 检查通知: ID=${notification.id}, 已处理=${processedNotifications.has(notification.id)}`);
                            
                            if (!processedNotifications.has(notification.id)) {
                                log(`🆕 处理新通知: ${notification.message}`);
                                showDishCompletionNotification(notification);
                                processedNotifications.add(notification.id);
                                log(`✅ 通知已处理: ${notification.message}`);
                            } else {
                                log(`⏭️ 跳过已处理通知: ID=${notification.id}`);
                            }
                        });
                    } else {
                        log(`📭 暂无新的菜品完成通知`);
                    }
                })
                .catch(error => {
                    log(`❌ 检查菜品完成通知失败: ${error.message}`);
                });
        }
        
        // 页面加载完成后开始测试
        window.addEventListener('load', function() {
            log('🚀 测试页面已加载');
            log('💡 点击"测试通知显示"按钮测试通知显示功能');
            log('💡 点击"测试API轮询"按钮测试API轮询功能');
        });
    </script>
</body>
</html>
