#!/usr/bin/env python3
"""
订单权限管理测试脚本
测试新实现的订单编辑和删除权限规则

测试场景：
1. 商务中心和餐饮经理只能编辑已预订状态的订单
2. 商务中心和餐饮经理只能删除已预订和已完成状态的订单
3. 进行中的订单不能被编辑或删除
4. 管理员拥有所有权限
5. 其他角色无权限
"""

import sys
import os

# Add the backend_pure_python directory to the path
backend_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backend_pure_python')
sys.path.insert(0, backend_path)

from models.user import User, UserRole
from models.order import Order, OrderStatus
from core.database import SessionLocal

def create_test_users():
    """创建测试用户"""
    db = SessionLocal()
    try:
        # 清理现有测试用户
        db.query(User).filter(User.username.like('test_%')).delete()
        
        # 创建测试用户
        users = [
            User(username='test_admin', full_name='测试管理员', role=UserRole.ADMIN, hashed_password='test'),
            User(username='test_manager', full_name='测试餐饮经理', role=UserRole.MANAGER, hashed_password='test'),
            User(username='test_business', full_name='测试商务中心', role=UserRole.BUSINESS_CENTER, hashed_password='test'),
            User(username='test_chef', full_name='测试厨师长', role=UserRole.CHEF_MANAGER, hashed_password='test'),
            User(username='test_waiter', full_name='测试服务员', role=UserRole.WAITER, hashed_password='test'),
        ]
        
        for user in users:
            db.add(user)
        
        db.commit()
        print("✅ 测试用户创建成功")
        return {user.role: user for user in users}
        
    except Exception as e:
        db.rollback()
        print(f"❌ 创建测试用户失败: {e}")
        return {}
    finally:
        db.close()

def create_test_orders():
    """创建测试订单"""
    db = SessionLocal()
    try:
        # 清理现有测试订单
        db.query(Order).filter(Order.order_number.like('TEST%')).delete()
        
        # 创建不同状态的测试订单
        orders = [
            Order(order_number='TEST001', status=OrderStatus.RESERVED, customer_name='测试客户1'),
            Order(order_number='TEST002', status=OrderStatus.PENDING_START, customer_name='测试客户2'),
            Order(order_number='TEST003', status=OrderStatus.SERVING, customer_name='测试客户3'),
            Order(order_number='TEST004', status=OrderStatus.COMPLETED, customer_name='测试客户4'),
            Order(order_number='TEST005', status=OrderStatus.CANCELLED, customer_name='测试客户5'),
        ]
        
        for order in orders:
            db.add(order)
        
        db.commit()
        print("✅ 测试订单创建成功")
        return {order.status: order for order in orders}
        
    except Exception as e:
        db.rollback()
        print(f"❌ 创建测试订单失败: {e}")
        return {}
    finally:
        db.close()

def test_edit_permissions(users, orders):
    """测试编辑权限"""
    print("\n🔍 测试订单编辑权限...")
    
    test_cases = [
        # (用户角色, 订单状态, 期望结果, 描述)
        (UserRole.ADMIN, OrderStatus.RESERVED, True, "管理员可以编辑任何订单"),
        (UserRole.ADMIN, OrderStatus.SERVING, True, "管理员可以编辑进行中订单"),
        (UserRole.MANAGER, OrderStatus.RESERVED, True, "餐饮经理可以编辑已预订订单"),
        (UserRole.MANAGER, OrderStatus.SERVING, False, "餐饮经理不能编辑进行中订单"),
        (UserRole.MANAGER, OrderStatus.COMPLETED, False, "餐饮经理不能编辑已完成订单"),
        (UserRole.BUSINESS_CENTER, OrderStatus.RESERVED, True, "商务中心可以编辑已预订订单"),
        (UserRole.BUSINESS_CENTER, OrderStatus.SERVING, False, "商务中心不能编辑进行中订单"),
        (UserRole.BUSINESS_CENTER, OrderStatus.COMPLETED, False, "商务中心不能编辑已完成订单"),
        (UserRole.CHEF_MANAGER, OrderStatus.RESERVED, True, "厨师长可以编辑已预订订单"),
        (UserRole.CHEF_MANAGER, OrderStatus.SERVING, True, "厨师长可以编辑进行中订单"),
        (UserRole.CHEF_MANAGER, OrderStatus.COMPLETED, False, "厨师长不能编辑已完成订单"),
        (UserRole.WAITER, OrderStatus.RESERVED, False, "服务员不能编辑订单"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for user_role, order_status, expected, description in test_cases:
        user = users.get(user_role)
        order = orders.get(order_status)
        
        if user and order:
            result = order.can_be_edited_by_user(user)
            status = "✅" if result == expected else "❌"
            print(f"  {status} {description}: {result} (期望: {expected})")
            if result == expected:
                passed += 1
        else:
            print(f"  ⚠️ 跳过测试: 用户或订单不存在 - {description}")
    
    print(f"\n编辑权限测试结果: {passed}/{total} 通过")
    return passed == total

def test_delete_permissions(users, orders):
    """测试删除权限"""
    print("\n🔍 测试订单删除权限...")
    
    test_cases = [
        # (用户角色, 订单状态, 期望结果, 描述)
        (UserRole.ADMIN, OrderStatus.RESERVED, True, "管理员可以删除已预订订单"),
        (UserRole.ADMIN, OrderStatus.COMPLETED, True, "管理员可以删除已完成订单"),
        (UserRole.ADMIN, OrderStatus.SERVING, False, "管理员不能删除进行中订单"),
        (UserRole.MANAGER, OrderStatus.RESERVED, True, "餐饮经理可以删除已预订订单"),
        (UserRole.MANAGER, OrderStatus.COMPLETED, True, "餐饮经理可以删除已完成订单"),
        (UserRole.MANAGER, OrderStatus.SERVING, False, "餐饮经理不能删除进行中订单"),
        (UserRole.BUSINESS_CENTER, OrderStatus.RESERVED, True, "商务中心可以删除已预订订单"),
        (UserRole.BUSINESS_CENTER, OrderStatus.COMPLETED, True, "商务中心可以删除已完成订单"),
        (UserRole.BUSINESS_CENTER, OrderStatus.SERVING, False, "商务中心不能删除进行中订单"),
        (UserRole.CHEF_MANAGER, OrderStatus.RESERVED, False, "厨师长不能删除订单"),
        (UserRole.WAITER, OrderStatus.RESERVED, False, "服务员不能删除订单"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for user_role, order_status, expected, description in test_cases:
        user = users.get(user_role)
        order = orders.get(order_status)
        
        if user and order:
            result = order.can_be_deleted_by_user(user)
            status = "✅" if result == expected else "❌"
            print(f"  {status} {description}: {result} (期望: {expected})")
            if result == expected:
                passed += 1
        else:
            print(f"  ⚠️ 跳过测试: 用户或订单不存在 - {description}")
    
    print(f"\n删除权限测试结果: {passed}/{total} 通过")
    return passed == total

def test_user_permissions(users):
    """测试用户权限模型"""
    print("\n🔍 测试用户权限模型...")
    
    test_cases = [
        # (用户角色, 权限, 期望结果, 描述)
        (UserRole.MANAGER, "order.edit_reserved", True, "餐饮经理有编辑已预订订单权限"),
        (UserRole.MANAGER, "order.delete_completed", True, "餐饮经理有删除已完成订单权限"),
        (UserRole.BUSINESS_CENTER, "order.edit_reserved", True, "商务中心有编辑已预订订单权限"),
        (UserRole.BUSINESS_CENTER, "order.delete_completed", True, "商务中心有删除已完成订单权限"),
        (UserRole.WAITER, "order.edit_reserved", False, "服务员没有编辑订单权限"),
        (UserRole.CHEF_MANAGER, "order.edit_reserved", False, "厨师长没有编辑已预订订单权限"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for user_role, permission, expected, description in test_cases:
        user = users.get(user_role)
        
        if user:
            result = user.has_permission(permission)
            status = "✅" if result == expected else "❌"
            print(f"  {status} {description}: {result} (期望: {expected})")
            if result == expected:
                passed += 1
        else:
            print(f"  ⚠️ 跳过测试: 用户不存在 - {description}")
    
    print(f"\n用户权限测试结果: {passed}/{total} 通过")
    return passed == total

def cleanup_test_data():
    """清理测试数据"""
    db = SessionLocal()
    try:
        # 删除测试订单
        db.query(Order).filter(Order.order_number.like('TEST%')).delete()
        # 删除测试用户
        db.query(User).filter(User.username.like('test_%')).delete()
        db.commit()
        print("✅ 测试数据清理完成")
    except Exception as e:
        db.rollback()
        print(f"❌ 清理测试数据失败: {e}")
    finally:
        db.close()

def main():
    """主测试函数"""
    print("🚀 开始订单权限管理测试...")
    
    # 创建测试数据
    users = create_test_users()
    orders = create_test_orders()
    
    if not users or not orders:
        print("❌ 测试数据创建失败，退出测试")
        return False
    
    # 执行测试
    edit_test_passed = test_edit_permissions(users, orders)
    delete_test_passed = test_delete_permissions(users, orders)
    permission_test_passed = test_user_permissions(users)
    
    # 清理测试数据
    cleanup_test_data()
    
    # 输出总结
    all_passed = edit_test_passed and delete_test_passed and permission_test_passed
    print(f"\n{'='*50}")
    print(f"📊 测试总结:")
    print(f"  编辑权限测试: {'✅ 通过' if edit_test_passed else '❌ 失败'}")
    print(f"  删除权限测试: {'✅ 通过' if delete_test_passed else '❌ 失败'}")
    print(f"  用户权限测试: {'✅ 通过' if permission_test_passed else '❌ 失败'}")
    print(f"  总体结果: {'🎉 所有测试通过' if all_passed else '⚠️ 部分测试失败'}")
    print(f"{'='*50}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
