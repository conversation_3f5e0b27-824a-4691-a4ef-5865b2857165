#!/usr/bin/env python3
"""
测试用餐开始实时更新功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend_pure_python'))

from websocket_manager import get_websocket_manager

async def test_dining_start_broadcast():
    """测试用餐开始广播功能"""
    print("🧪 开始测试用餐开始实时更新功能...")
    
    # 获取WebSocket管理器
    websocket_manager = get_websocket_manager()
    
    # 测试用餐开始广播
    test_cases = [
        {
            'room_number': '101',
            'waiter_name': '张三',
            'guest_count': 4
        },
        {
            'room_number': '102', 
            'waiter_name': '李四',
            'guest_count': 6
        },
        {
            'room_number': '103',
            'waiter_name': '王五',
            'guest_count': 2
        }
    ]
    
    print("\n📡 测试WebSocket广播功能:")
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i} ---")
        
        # 测试用餐开始广播
        result = await websocket_manager.broadcast_dining_started(
            room_number=test_case['room_number'],
            waiter_name=test_case['waiter_name'],
            guest_count=test_case['guest_count']
        )
        
        if result:
            print(f"✅ 用餐开始广播成功: {test_case['room_number']}包厢")
        else:
            print(f"❌ 用餐开始广播失败: {test_case['room_number']}包厢")
        
        # 等待1秒
        await asyncio.sleep(1)
    
    print("\n🔄 测试厨房刷新广播:")
    refresh_result = await websocket_manager.broadcast_kitchen_refresh()
    if refresh_result:
        print("✅ 厨房刷新广播成功")
    else:
        print("❌ 厨房刷新广播失败")
    
    print("\n🧪 测试完成！")

def test_websocket_manager_initialization():
    """测试WebSocket管理器初始化"""
    print("🔧 测试WebSocket管理器初始化...")
    
    # 测试单例模式
    manager1 = get_websocket_manager()
    manager2 = get_websocket_manager()
    
    if manager1 is manager2:
        print("✅ WebSocket管理器单例模式正常")
    else:
        print("❌ WebSocket管理器单例模式异常")
    
    # 测试连接字典初始化
    expected_keys = {'waiters', 'kitchen', 'management'}
    actual_keys = set(manager1.connections.keys())
    
    if expected_keys == actual_keys:
        print("✅ WebSocket连接字典初始化正常")
    else:
        print(f"❌ WebSocket连接字典初始化异常: 期望 {expected_keys}, 实际 {actual_keys}")

async def main():
    """主测试函数"""
    print("🚀 开始实时用餐开始更新功能测试")
    print("=" * 50)
    
    # 测试WebSocket管理器初始化
    test_websocket_manager_initialization()
    
    print("\n" + "=" * 50)
    
    # 测试用餐开始广播
    await test_dining_start_broadcast()
    
    print("\n" + "=" * 50)
    print("🎉 所有测试完成！")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
