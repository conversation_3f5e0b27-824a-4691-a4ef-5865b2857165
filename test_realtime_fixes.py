#!/usr/bin/env python3
"""
测试实时更新修复功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend_pure_python'))

from websocket_manager import get_websocket_manager

async def test_dining_end_broadcast():
    """测试结束用餐广播功能"""
    print("🧪 测试结束用餐实时更新...")
    
    websocket_manager = get_websocket_manager()
    
    test_cases = [
        {'room_number': '101', 'waiter_name': '张三'},
        {'room_number': '102', 'waiter_name': '李四'},
        {'room_number': '103', 'waiter_name': '王五'}
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i}: 结束用餐 ---")
        
        result = await websocket_manager.broadcast_dining_ended(
            room_number=test_case['room_number'],
            waiter_name=test_case['waiter_name']
        )
        
        if result:
            print(f"✅ 结束用餐广播成功: {test_case['room_number']}包厢")
        else:
            print(f"❌ 结束用餐广播失败: {test_case['room_number']}包厢")
        
        await asyncio.sleep(1)

async def test_dish_ready_broadcast():
    """测试菜品完成广播功能"""
    print("\n🧪 测试菜品完成实时更新...")
    
    websocket_manager = get_websocket_manager()
    
    test_dishes = [
        {'dish_id': 1, 'room_number': '101', 'dish_name': '宫保鸡丁'},
        {'dish_id': 2, 'room_number': '102', 'dish_name': '麻婆豆腐'},
        {'dish_id': 3, 'room_number': '103', 'dish_name': '红烧肉'}
    ]
    
    for i, dish in enumerate(test_dishes, 1):
        print(f"\n--- 测试用例 {i}: 菜品完成 ---")
        
        result = await websocket_manager.broadcast_dish_ready(
            dish_id=dish['dish_id'],
            room_number=dish['room_number'],
            dish_name=dish['dish_name']
        )
        
        if result:
            print(f"✅ 菜品完成广播成功: {dish['room_number']}包厢 - {dish['dish_name']}")
        else:
            print(f"❌ 菜品完成广播失败: {dish['room_number']}包厢 - {dish['dish_name']}")
        
        await asyncio.sleep(1)

async def test_kitchen_refresh():
    """测试厨房刷新广播"""
    print("\n🧪 测试厨房刷新广播...")
    
    websocket_manager = get_websocket_manager()
    
    result = await websocket_manager.broadcast_kitchen_refresh()
    
    if result:
        print("✅ 厨房刷新广播成功")
    else:
        print("❌ 厨房刷新广播失败")

async def test_all_broadcasts():
    """测试所有广播功能"""
    print("🚀 开始测试所有实时更新修复功能")
    print("=" * 60)
    
    # 测试结束用餐广播
    await test_dining_end_broadcast()
    
    print("\n" + "=" * 60)
    
    # 测试菜品完成广播
    await test_dish_ready_broadcast()
    
    print("\n" + "=" * 60)
    
    # 测试厨房刷新
    await test_kitchen_refresh()
    
    print("\n" + "=" * 60)
    print("🎉 所有测试完成！")

def test_websocket_manager():
    """测试WebSocket管理器基本功能"""
    print("🔧 测试WebSocket管理器...")
    
    manager = get_websocket_manager()
    
    # 检查连接字典
    expected_keys = {'waiters', 'kitchen', 'management'}
    actual_keys = set(manager.connections.keys())
    
    if expected_keys == actual_keys:
        print("✅ WebSocket连接字典正常")
    else:
        print(f"❌ WebSocket连接字典异常: 期望 {expected_keys}, 实际 {actual_keys}")
    
    # 检查方法存在性
    methods = ['broadcast_dining_ended', 'broadcast_dish_ready', 'broadcast_kitchen_refresh']
    for method in methods:
        if hasattr(manager, method):
            print(f"✅ 方法 {method} 存在")
        else:
            print(f"❌ 方法 {method} 不存在")

if __name__ == "__main__":
    print("🧪 实时更新修复功能测试")
    print("=" * 60)
    
    # 测试WebSocket管理器
    test_websocket_manager()
    
    print("\n" + "=" * 60)
    
    # 运行异步测试
    asyncio.run(test_all_broadcasts())
