<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>餐厅语音播报一致性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .button-group {
            margin: 10px 0;
        }
        .btn {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-warning { background-color: #ffc107; color: black; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            margin: 5px 0;
            font-weight: bold;
        }
        .status.success { background-color: #d4edda; color: #155724; }
        .status.error { background-color: #f8d7da; color: #721c24; }
        .status.warning { background-color: #fff3cd; color: #856404; }
        .status.info { background-color: #d1ecf1; color: #0c5460; }
        .device-info {
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 餐厅语音播报一致性测试工具</h1>
        
        <div class="device-info">
            <h4>设备信息</h4>
            <p><strong>设备类型:</strong> <span id="deviceType"></span></p>
            <p><strong>浏览器:</strong> <span id="browserInfo"></span></p>
            <p><strong>语音API支持:</strong> <span id="speechSupport"></span></p>
            <p><strong>当前时间:</strong> <span id="currentTime"></span></p>
        </div>

        <div class="test-section">
            <h3>🔧 语音配置测试</h3>
            <div class="button-group">
                <button class="btn btn-info" onclick="testVoiceConfig()">获取语音配置</button>
                <button class="btn btn-warning" onclick="testConfigSync()">测试配置同步</button>
            </div>
            <div class="status" id="configStatus">等待测试...</div>
            <div class="log-area" id="configLog"></div>
        </div>

        <div class="test-section">
            <h3>🔊 基础语音播报测试</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testBasicVoice()">基础播报测试</button>
                <button class="btn btn-success" onclick="testRepeatCount()">播报次数测试</button>
                <button class="btn btn-warning" onclick="testDuplicatePrevention()">去重机制测试</button>
            </div>
            <div class="status" id="voiceStatus">等待测试...</div>
            <div class="log-area" id="voiceLog"></div>
        </div>

        <div class="test-section">
            <h3>📱 移动端兼容性测试</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testMobileCompatibility()">移动端兼容性</button>
                <button class="btn btn-success" onclick="testUserInteraction()">用户交互激活</button>
                <button class="btn btn-info" onclick="testBackgroundPlayback()">后台播放测试</button>
            </div>
            <div class="status" id="mobileStatus">等待测试...</div>
            <div class="log-area" id="mobileLog"></div>
        </div>

        <div class="test-section">
            <h3>🍽️ 餐厅指令模拟测试</h3>
            <div class="button-group">
                <button class="btn btn-primary" onclick="testWaiterInstructions()">服务员指令测试</button>
                <button class="btn btn-success" onclick="testKitchenNotifications()">厨房通知测试</button>
                <button class="btn btn-warning" onclick="testDiningStatus()">用餐状态测试</button>
                <button class="btn btn-danger" onclick="testStressTest()">压力测试</button>
            </div>
            <div class="status" id="restaurantStatus">等待测试...</div>
            <div class="log-area" id="restaurantLog"></div>
        </div>

        <div class="test-section">
            <h3>📊 测试结果汇总</h3>
            <div class="button-group">
                <button class="btn btn-success" onclick="generateReport()">生成测试报告</button>
                <button class="btn btn-warning" onclick="clearAllLogs()">清除所有日志</button>
            </div>
            <div class="log-area" id="reportLog"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let voiceConfig = null;
        let testResults = {
            config: [],
            voice: [],
            mobile: [],
            restaurant: []
        };
        let isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            updateDeviceInfo();
            updateTime();
            setInterval(updateTime, 1000);
        });

        // 更新设备信息
        function updateDeviceInfo() {
            document.getElementById('deviceType').textContent = isMobileDevice ? '移动设备' : '桌面设备';
            document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').slice(-2).join(' ');
            document.getElementById('speechSupport').textContent = 'speechSynthesis' in window ? '✅ 支持' : '❌ 不支持';
        }

        // 更新时间
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }

        // 日志记录函数
        function logToArea(areaId, message, type = 'info') {
            const area = document.getElementById(areaId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            area.textContent += logEntry;
            area.scrollTop = area.scrollHeight;
        }

        // 更新状态
        function updateStatus(statusId, message, type = 'info') {
            const status = document.getElementById(statusId);
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // 获取语音配置
        async function getVoiceConfig() {
            try {
                const response = await fetch('/api/voice-config');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return await response.json();
            } catch (error) {
                console.error('获取语音配置失败:', error);
                return {
                    voice_enabled: true,
                    voice_repeat_count: 2,
                    voice_repeat_interval: 3,
                    voice_rate: 0.8,
                    voice_volume: 1.0,
                    voice_pitch: 1.0
                };
            }
        }

        // 测试语音配置
        async function testVoiceConfig() {
            updateStatus('configStatus', '正在测试语音配置...', 'info');
            logToArea('configLog', '开始语音配置测试');

            try {
                voiceConfig = await getVoiceConfig();
                logToArea('configLog', `✅ 语音配置获取成功: ${JSON.stringify(voiceConfig, null, 2)}`);
                
                // 验证配置项
                const requiredFields = ['voice_enabled', 'voice_repeat_count', 'voice_repeat_interval'];
                const missingFields = requiredFields.filter(field => !(field in voiceConfig));
                
                if (missingFields.length > 0) {
                    logToArea('configLog', `⚠️ 缺少配置项: ${missingFields.join(', ')}`);
                    updateStatus('configStatus', '配置不完整', 'warning');
                } else {
                    logToArea('configLog', '✅ 所有必需配置项都存在');
                    updateStatus('configStatus', '配置测试通过', 'success');
                }

                testResults.config.push({
                    test: 'getVoiceConfig',
                    result: 'success',
                    config: voiceConfig
                });

            } catch (error) {
                logToArea('configLog', `❌ 语音配置测试失败: ${error.message}`);
                updateStatus('configStatus', '配置测试失败', 'error');
                testResults.config.push({
                    test: 'getVoiceConfig',
                    result: 'error',
                    error: error.message
                });
            }
        }

        // 测试配置同步
        async function testConfigSync() {
            logToArea('configLog', '开始配置同步测试');

            try {
                // 多次获取配置，检查一致性
                const configs = [];
                for (let i = 0; i < 3; i++) {
                    const config = await getVoiceConfig();
                    configs.push(config);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // 检查配置一致性
                const firstConfig = JSON.stringify(configs[0]);
                const allSame = configs.every(config => JSON.stringify(config) === firstConfig);

                if (allSame) {
                    logToArea('configLog', '✅ 配置同步测试通过，所有获取的配置一致');
                    updateStatus('configStatus', '配置同步正常', 'success');
                } else {
                    logToArea('configLog', '⚠️ 配置同步异常，多次获取的配置不一致');
                    updateStatus('configStatus', '配置同步异常', 'warning');
                }

                testResults.config.push({
                    test: 'configSync',
                    result: allSame ? 'success' : 'warning',
                    configs: configs
                });

            } catch (error) {
                logToArea('configLog', `❌ 配置同步测试失败: ${error.message}`);
                updateStatus('configStatus', '同步测试失败', 'error');
            }
        }

        // 基础语音播报测试
        async function testBasicVoice() {
            updateStatus('voiceStatus', '正在测试基础语音播报...', 'info');
            logToArea('voiceLog', '开始基础语音播报测试');

            if (!('speechSynthesis' in window)) {
                logToArea('voiceLog', '❌ 浏览器不支持语音合成API');
                updateStatus('voiceStatus', '不支持语音API', 'error');
                return;
            }

            try {
                const testText = '这是一个基础语音播报测试';
                logToArea('voiceLog', `🔊 开始播报: ${testText}`);

                const utterance = new SpeechSynthesisUtterance(testText);
                utterance.lang = 'zh-CN';
                utterance.rate = 0.8;
                utterance.volume = 1.0;
                utterance.pitch = 1.0;

                const startTime = Date.now();

                utterance.onstart = function() {
                    logToArea('voiceLog', '✅ 语音播报开始');
                };

                utterance.onend = function() {
                    const duration = Date.now() - startTime;
                    logToArea('voiceLog', `✅ 语音播报完成，耗时: ${duration}ms`);
                    updateStatus('voiceStatus', '基础播报测试通过', 'success');

                    testResults.voice.push({
                        test: 'basicVoice',
                        result: 'success',
                        duration: duration,
                        text: testText
                    });
                };

                utterance.onerror = function(event) {
                    logToArea('voiceLog', `❌ 语音播报错误: ${event.error}`);
                    updateStatus('voiceStatus', '基础播报测试失败', 'error');

                    testResults.voice.push({
                        test: 'basicVoice',
                        result: 'error',
                        error: event.error
                    });
                };

                speechSynthesis.speak(utterance);

            } catch (error) {
                logToArea('voiceLog', `❌ 基础语音测试异常: ${error.message}`);
                updateStatus('voiceStatus', '测试异常', 'error');
            }
        }

        // 播报次数测试
        async function testRepeatCount() {
            logToArea('voiceLog', '开始播报次数测试');

            if (!voiceConfig) {
                await testVoiceConfig();
            }

            try {
                const testText = '播报次数测试';
                const expectedRepeats = voiceConfig.voice_repeat_count || 2;
                const interval = (voiceConfig.voice_repeat_interval || 3) * 1000;

                logToArea('voiceLog', `🔊 开始${expectedRepeats}次播报测试，间隔${interval}ms`);

                let actualRepeats = 0;
                const startTime = Date.now();

                for (let i = 0; i < expectedRepeats; i++) {
                    await new Promise((resolve) => {
                        const utterance = new SpeechSynthesisUtterance(testText);
                        utterance.lang = 'zh-CN';
                        utterance.rate = voiceConfig.voice_rate || 0.8;
                        utterance.volume = voiceConfig.voice_volume || 1.0;
                        utterance.pitch = voiceConfig.voice_pitch || 1.0;

                        utterance.onstart = function() {
                            actualRepeats++;
                            logToArea('voiceLog', `🔊 第${actualRepeats}次播报开始`);
                        };

                        utterance.onend = function() {
                            logToArea('voiceLog', `✅ 第${actualRepeats}次播报完成`);
                            resolve();
                        };

                        utterance.onerror = function(event) {
                            logToArea('voiceLog', `❌ 第${i + 1}次播报错误: ${event.error}`);
                            resolve();
                        };

                        speechSynthesis.speak(utterance);
                    });

                    // 播报间隔（最后一次播报后不需要等待）
                    if (i < expectedRepeats - 1) {
                        logToArea('voiceLog', `⏳ 等待间隔: ${interval}ms`);
                        await new Promise(resolve => setTimeout(resolve, interval));
                    }
                }

                const totalDuration = Date.now() - startTime;

                if (actualRepeats === expectedRepeats) {
                    logToArea('voiceLog', `✅ 播报次数测试通过: 期望${expectedRepeats}次，实际${actualRepeats}次，总耗时${totalDuration}ms`);
                    updateStatus('voiceStatus', '播报次数测试通过', 'success');

                    testResults.voice.push({
                        test: 'repeatCount',
                        result: 'success',
                        expected: expectedRepeats,
                        actual: actualRepeats,
                        duration: totalDuration
                    });
                } else {
                    logToArea('voiceLog', `⚠️ 播报次数不匹配: 期望${expectedRepeats}次，实际${actualRepeats}次`);
                    updateStatus('voiceStatus', '播报次数不匹配', 'warning');

                    testResults.voice.push({
                        test: 'repeatCount',
                        result: 'warning',
                        expected: expectedRepeats,
                        actual: actualRepeats
                    });
                }

            } catch (error) {
                logToArea('voiceLog', `❌ 播报次数测试异常: ${error.message}`);
                updateStatus('voiceStatus', '次数测试异常', 'error');
            }
        }

        // 去重机制测试
        async function testDuplicatePrevention() {
            logToArea('voiceLog', '开始去重机制测试');

            try {
                const testText = '重复播报测试内容';
                logToArea('voiceLog', `🔊 测试重复播报防护: ${testText}`);

                // 模拟快速重复调用
                let playCount = 0;
                const promises = [];

                for (let i = 0; i < 5; i++) {
                    promises.push(new Promise((resolve) => {
                        const utterance = new SpeechSynthesisUtterance(testText);
                        utterance.lang = 'zh-CN';

                        utterance.onstart = function() {
                            playCount++;
                            logToArea('voiceLog', `🔊 播报开始 (第${playCount}次)`);
                        };

                        utterance.onend = function() {
                            logToArea('voiceLog', `✅ 播报结束 (第${playCount}次)`);
                            resolve();
                        };

                        utterance.onerror = function(event) {
                            logToArea('voiceLog', `❌ 播报错误: ${event.error}`);
                            resolve();
                        };

                        // 快速连续调用
                        setTimeout(() => {
                            speechSynthesis.speak(utterance);
                        }, i * 100);
                    }));
                }

                await Promise.all(promises);

                if (playCount <= 2) {
                    logToArea('voiceLog', `✅ 去重机制有效: 5次调用只播报了${playCount}次`);
                    updateStatus('voiceStatus', '去重测试通过', 'success');

                    testResults.voice.push({
                        test: 'duplicatePrevention',
                        result: 'success',
                        attempts: 5,
                        actual: playCount
                    });
                } else {
                    logToArea('voiceLog', `⚠️ 去重机制可能失效: 5次调用播报了${playCount}次`);
                    updateStatus('voiceStatus', '去重可能失效', 'warning');

                    testResults.voice.push({
                        test: 'duplicatePrevention',
                        result: 'warning',
                        attempts: 5,
                        actual: playCount
                    });
                }

            } catch (error) {
                logToArea('voiceLog', `❌ 去重测试异常: ${error.message}`);
                updateStatus('voiceStatus', '去重测试异常', 'error');
            }
        }

        // 移动端兼容性测试
        async function testMobileCompatibility() {
            updateStatus('mobileStatus', '正在测试移动端兼容性...', 'info');
            logToArea('mobileLog', '开始移动端兼容性测试');

            logToArea('mobileLog', `📱 设备类型: ${isMobileDevice ? '移动设备' : '桌面设备'}`);
            logToArea('mobileLog', `🌐 用户代理: ${navigator.userAgent}`);

            // 检查语音API支持
            if (!('speechSynthesis' in window)) {
                logToArea('mobileLog', '❌ 不支持语音合成API');
                updateStatus('mobileStatus', '不支持语音API', 'error');
                return;
            }

            try {
                // 检查语音列表
                const voices = speechSynthesis.getVoices();
                logToArea('mobileLog', `🔊 可用语音数量: ${voices.length}`);

                const chineseVoices = voices.filter(voice => voice.lang.includes('zh'));
                logToArea('mobileLog', `🇨🇳 中文语音数量: ${chineseVoices.length}`);

                if (chineseVoices.length > 0) {
                    logToArea('mobileLog', `✅ 支持中文语音: ${chineseVoices[0].name}`);
                } else {
                    logToArea('mobileLog', '⚠️ 未找到中文语音，将使用默认语音');
                }

                // 移动端特殊检查
                if (isMobileDevice) {
                    logToArea('mobileLog', '📱 执行移动端特殊检查...');

                    // 检查是否需要用户交互
                    const testUtterance = new SpeechSynthesisUtterance('移动端测试');
                    testUtterance.volume = 0; // 静音测试

                    let interactionRequired = false;
                    testUtterance.onerror = function(event) {
                        if (event.error === 'not-allowed') {
                            interactionRequired = true;
                            logToArea('mobileLog', '⚠️ 移动端需要用户交互才能播放语音');
                        }
                    };

                    speechSynthesis.speak(testUtterance);

                    await new Promise(resolve => setTimeout(resolve, 1000));

                    if (!interactionRequired) {
                        logToArea('mobileLog', '✅ 移动端语音API可直接使用');
                    }
                }

                updateStatus('mobileStatus', '移动端兼容性测试完成', 'success');

                testResults.mobile.push({
                    test: 'compatibility',
                    result: 'success',
                    isMobile: isMobileDevice,
                    voicesCount: voices.length,
                    chineseVoicesCount: chineseVoices.length
                });

            } catch (error) {
                logToArea('mobileLog', `❌ 移动端兼容性测试异常: ${error.message}`);
                updateStatus('mobileStatus', '兼容性测试异常', 'error');
            }
        }

        // 用户交互激活测试
        async function testUserInteraction() {
            logToArea('mobileLog', '开始用户交互激活测试');

            if (!isMobileDevice) {
                logToArea('mobileLog', '⚠️ 当前为桌面设备，跳过用户交互测试');
                return;
            }

            try {
                logToArea('mobileLog', '📱 模拟用户交互激活语音API...');

                // 创建一个静音的语音来激活API
                const activationUtterance = new SpeechSynthesisUtterance('激活');
                activationUtterance.volume = 0.1; // 很小的音量
                activationUtterance.rate = 2.0; // 快速播放

                let activated = false;
                activationUtterance.onstart = function() {
                    activated = true;
                    logToArea('mobileLog', '✅ 语音API已激活');
                };

                activationUtterance.onerror = function(event) {
                    logToArea('mobileLog', `❌ 激活失败: ${event.error}`);
                };

                speechSynthesis.speak(activationUtterance);

                await new Promise(resolve => setTimeout(resolve, 2000));

                if (activated) {
                    updateStatus('mobileStatus', '用户交互激活成功', 'success');
                    testResults.mobile.push({
                        test: 'userInteraction',
                        result: 'success'
                    });
                } else {
                    updateStatus('mobileStatus', '用户交互激活失败', 'warning');
                    testResults.mobile.push({
                        test: 'userInteraction',
                        result: 'warning'
                    });
                }

            } catch (error) {
                logToArea('mobileLog', `❌ 用户交互测试异常: ${error.message}`);
                updateStatus('mobileStatus', '交互测试异常', 'error');
            }
        }

        // 后台播放测试
        async function testBackgroundPlayback() {
            logToArea('mobileLog', '开始后台播放测试');

            try {
                logToArea('mobileLog', '🔊 测试页面失焦时的语音播放...');

                const testText = '后台播放测试';
                let playbackSuccessful = false;

                // 模拟页面失焦
                if (document.hidden !== undefined) {
                    logToArea('mobileLog', `📄 当前页面可见性: ${document.hidden ? '隐藏' : '可见'}`);
                }

                const utterance = new SpeechSynthesisUtterance(testText);
                utterance.lang = 'zh-CN';

                utterance.onstart = function() {
                    playbackSuccessful = true;
                    logToArea('mobileLog', '✅ 后台播放开始');
                };

                utterance.onend = function() {
                    logToArea('mobileLog', '✅ 后台播放完成');
                };

                utterance.onerror = function(event) {
                    logToArea('mobileLog', `❌ 后台播放错误: ${event.error}`);
                };

                speechSynthesis.speak(utterance);

                await new Promise(resolve => setTimeout(resolve, 3000));

                if (playbackSuccessful) {
                    updateStatus('mobileStatus', '后台播放测试通过', 'success');
                    testResults.mobile.push({
                        test: 'backgroundPlayback',
                        result: 'success'
                    });
                } else {
                    updateStatus('mobileStatus', '后台播放可能受限', 'warning');
                    testResults.mobile.push({
                        test: 'backgroundPlayback',
                        result: 'warning'
                    });
                }

            } catch (error) {
                logToArea('mobileLog', `❌ 后台播放测试异常: ${error.message}`);
                updateStatus('mobileStatus', '后台测试异常', 'error');
            }
        }

        // 服务员指令测试
        async function testWaiterInstructions() {
            updateStatus('restaurantStatus', '正在测试服务员指令...', 'info');
            logToArea('restaurantLog', '开始服务员指令测试');

            const instructions = [
                '1号桌客人需要餐具',
                '3号桌要求加菜',
                '5号桌客人投诉菜品温度',
                '2号桌需要结账',
                '4号桌要求换座位'
            ];

            try {
                if (!voiceConfig) {
                    await testVoiceConfig();
                }

                let successCount = 0;
                const startTime = Date.now();

                for (let i = 0; i < instructions.length; i++) {
                    const instruction = instructions[i];
                    logToArea('restaurantLog', `🔊 播报服务员指令 ${i + 1}: ${instruction}`);

                    await new Promise((resolve) => {
                        const utterance = new SpeechSynthesisUtterance(instruction);
                        utterance.lang = 'zh-CN';
                        utterance.rate = voiceConfig.voice_rate || 0.8;

                        utterance.onstart = function() {
                            logToArea('restaurantLog', `✅ 指令 ${i + 1} 播报开始`);
                        };

                        utterance.onend = function() {
                            successCount++;
                            logToArea('restaurantLog', `✅ 指令 ${i + 1} 播报完成`);
                            resolve();
                        };

                        utterance.onerror = function(event) {
                            logToArea('restaurantLog', `❌ 指令 ${i + 1} 播报错误: ${event.error}`);
                            resolve();
                        };

                        speechSynthesis.speak(utterance);
                    });

                    // 指令间隔
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                const totalDuration = Date.now() - startTime;

                if (successCount === instructions.length) {
                    logToArea('restaurantLog', `✅ 服务员指令测试完成: ${successCount}/${instructions.length} 成功，耗时 ${totalDuration}ms`);
                    updateStatus('restaurantStatus', '服务员指令测试通过', 'success');
                } else {
                    logToArea('restaurantLog', `⚠️ 部分指令播报失败: ${successCount}/${instructions.length} 成功`);
                    updateStatus('restaurantStatus', '部分指令失败', 'warning');
                }

                testResults.restaurant.push({
                    test: 'waiterInstructions',
                    result: successCount === instructions.length ? 'success' : 'warning',
                    total: instructions.length,
                    success: successCount,
                    duration: totalDuration
                });

            } catch (error) {
                logToArea('restaurantLog', `❌ 服务员指令测试异常: ${error.message}`);
                updateStatus('restaurantStatus', '指令测试异常', 'error');
            }
        }

        // 厨房通知测试
        async function testKitchenNotifications() {
            logToArea('restaurantLog', '开始厨房通知测试');

            const notifications = [
                '1号桌宫保鸡丁已完成',
                '3号桌麻婆豆腐需要加辣',
                '5号桌鱼香肉丝准备中',
                '2号桌汤品已上桌',
                '4号桌甜品制作完成'
            ];

            try {
                let successCount = 0;

                for (let i = 0; i < notifications.length; i++) {
                    const notification = notifications[i];
                    logToArea('restaurantLog', `🔔 播报厨房通知 ${i + 1}: ${notification}`);

                    await new Promise((resolve) => {
                        const utterance = new SpeechSynthesisUtterance(notification);
                        utterance.lang = 'zh-CN';
                        utterance.rate = 0.9; // 稍快一些

                        utterance.onstart = function() {
                            logToArea('restaurantLog', `✅ 通知 ${i + 1} 播报开始`);
                        };

                        utterance.onend = function() {
                            successCount++;
                            logToArea('restaurantLog', `✅ 通知 ${i + 1} 播报完成`);
                            resolve();
                        };

                        utterance.onerror = function(event) {
                            logToArea('restaurantLog', `❌ 通知 ${i + 1} 播报错误: ${event.error}`);
                            resolve();
                        };

                        speechSynthesis.speak(utterance);
                    });

                    await new Promise(resolve => setTimeout(resolve, 800));
                }

                if (successCount === notifications.length) {
                    logToArea('restaurantLog', `✅ 厨房通知测试完成: ${successCount}/${notifications.length} 成功`);
                    updateStatus('restaurantStatus', '厨房通知测试通过', 'success');
                } else {
                    logToArea('restaurantLog', `⚠️ 部分通知播报失败: ${successCount}/${notifications.length} 成功`);
                    updateStatus('restaurantStatus', '部分通知失败', 'warning');
                }

                testResults.restaurant.push({
                    test: 'kitchenNotifications',
                    result: successCount === notifications.length ? 'success' : 'warning',
                    total: notifications.length,
                    success: successCount
                });

            } catch (error) {
                logToArea('restaurantLog', `❌ 厨房通知测试异常: ${error.message}`);
                updateStatus('restaurantStatus', '通知测试异常', 'error');
            }
        }

        // 用餐状态测试
        async function testDiningStatus() {
            logToArea('restaurantLog', '开始用餐状态测试');

            const statusMessages = [
                '1号桌开始用餐',
                '3号桌用餐结束',
                '5号桌客人离席',
                '2号桌需要清理',
                '4号桌预约确认'
            ];

            try {
                let successCount = 0;

                for (let i = 0; i < statusMessages.length; i++) {
                    const message = statusMessages[i];
                    logToArea('restaurantLog', `📢 播报状态消息 ${i + 1}: ${message}`);

                    await new Promise((resolve) => {
                        const utterance = new SpeechSynthesisUtterance(message);
                        utterance.lang = 'zh-CN';
                        utterance.rate = 0.8;

                        utterance.onend = function() {
                            successCount++;
                            logToArea('restaurantLog', `✅ 状态消息 ${i + 1} 播报完成`);
                            resolve();
                        };

                        utterance.onerror = function(event) {
                            logToArea('restaurantLog', `❌ 状态消息 ${i + 1} 播报错误: ${event.error}`);
                            resolve();
                        };

                        speechSynthesis.speak(utterance);
                    });

                    await new Promise(resolve => setTimeout(resolve, 600));
                }

                testResults.restaurant.push({
                    test: 'diningStatus',
                    result: successCount === statusMessages.length ? 'success' : 'warning',
                    total: statusMessages.length,
                    success: successCount
                });

                logToArea('restaurantLog', `✅ 用餐状态测试完成: ${successCount}/${statusMessages.length} 成功`);

            } catch (error) {
                logToArea('restaurantLog', `❌ 用餐状态测试异常: ${error.message}`);
            }
        }

        // 压力测试
        async function testStressTest() {
            logToArea('restaurantLog', '开始压力测试');

            try {
                const testMessages = [];
                for (let i = 1; i <= 10; i++) {
                    testMessages.push(`压力测试消息 ${i}`);
                }

                logToArea('restaurantLog', `🚀 开始播报 ${testMessages.length} 条消息的压力测试`);

                let successCount = 0;
                const startTime = Date.now();
                const promises = [];

                // 并发播报测试
                for (let i = 0; i < testMessages.length; i++) {
                    promises.push(new Promise((resolve) => {
                        setTimeout(() => {
                            const utterance = new SpeechSynthesisUtterance(testMessages[i]);
                            utterance.lang = 'zh-CN';
                            utterance.rate = 1.2; // 快速播报

                            utterance.onstart = function() {
                                logToArea('restaurantLog', `🔊 消息 ${i + 1} 开始播报`);
                            };

                            utterance.onend = function() {
                                successCount++;
                                logToArea('restaurantLog', `✅ 消息 ${i + 1} 播报完成`);
                                resolve();
                            };

                            utterance.onerror = function(event) {
                                logToArea('restaurantLog', `❌ 消息 ${i + 1} 播报错误: ${event.error}`);
                                resolve();
                            };

                            speechSynthesis.speak(utterance);
                        }, i * 200); // 200ms间隔
                    }));
                }

                await Promise.all(promises);

                const totalDuration = Date.now() - startTime;
                const successRate = (successCount / testMessages.length * 100).toFixed(1);

                logToArea('restaurantLog', `🏁 压力测试完成: ${successCount}/${testMessages.length} 成功 (${successRate}%), 耗时 ${totalDuration}ms`);

                testResults.restaurant.push({
                    test: 'stressTest',
                    result: successCount >= testMessages.length * 0.8 ? 'success' : 'warning',
                    total: testMessages.length,
                    success: successCount,
                    successRate: successRate,
                    duration: totalDuration
                });

                if (successCount >= testMessages.length * 0.8) {
                    updateStatus('restaurantStatus', '压力测试通过', 'success');
                } else {
                    updateStatus('restaurantStatus', '压力测试部分失败', 'warning');
                }

            } catch (error) {
                logToArea('restaurantLog', `❌ 压力测试异常: ${error.message}`);
                updateStatus('restaurantStatus', '压力测试异常', 'error');
            }
        }

        // 生成测试报告
        function generateReport() {
            logToArea('reportLog', '正在生成测试报告...');

            const report = {
                timestamp: new Date().toISOString(),
                deviceInfo: {
                    type: isMobileDevice ? 'mobile' : 'desktop',
                    userAgent: navigator.userAgent,
                    speechSupport: 'speechSynthesis' in window
                },
                testResults: testResults,
                summary: {
                    totalTests: 0,
                    passedTests: 0,
                    failedTests: 0,
                    warningTests: 0
                }
            };

            // 统计测试结果
            Object.values(testResults).forEach(category => {
                category.forEach(test => {
                    report.summary.totalTests++;
                    switch (test.result) {
                        case 'success':
                            report.summary.passedTests++;
                            break;
                        case 'error':
                            report.summary.failedTests++;
                            break;
                        case 'warning':
                            report.summary.warningTests++;
                            break;
                    }
                });
            });

            // 显示报告
            const reportText = JSON.stringify(report, null, 2);
            logToArea('reportLog', '📊 测试报告生成完成:\n' + reportText);

            // 计算通过率
            const passRate = report.summary.totalTests > 0
                ? (report.summary.passedTests / report.summary.totalTests * 100).toFixed(1)
                : 0;

            logToArea('reportLog', `\n📈 测试汇总:`);
            logToArea('reportLog', `总测试数: ${report.summary.totalTests}`);
            logToArea('reportLog', `通过: ${report.summary.passedTests}`);
            logToArea('reportLog', `警告: ${report.summary.warningTests}`);
            logToArea('reportLog', `失败: ${report.summary.failedTests}`);
            logToArea('reportLog', `通过率: ${passRate}%`);

            // 保存到本地存储
            try {
                localStorage.setItem('voiceBroadcastTestReport', reportText);
                logToArea('reportLog', '\n💾 报告已保存到本地存储');
            } catch (error) {
                logToArea('reportLog', `\n❌ 保存报告失败: ${error.message}`);
            }
        }

        // 清除所有日志
        function clearAllLogs() {
            const logAreas = ['configLog', 'voiceLog', 'mobileLog', 'restaurantLog', 'reportLog'];
            logAreas.forEach(areaId => {
                document.getElementById(areaId).textContent = '';
            });

            // 重置状态
            const statusElements = ['configStatus', 'voiceStatus', 'mobileStatus', 'restaurantStatus'];
            statusElements.forEach(statusId => {
                updateStatus(statusId, '等待测试...', 'info');
            });

            // 清除测试结果
            testResults = {
                config: [],
                voice: [],
                mobile: [],
                restaurant: []
            };

            logToArea('reportLog', '🧹 所有日志和测试结果已清除');
        }
    </script>
</body>
</html>
    </script>
</body>
</html>
