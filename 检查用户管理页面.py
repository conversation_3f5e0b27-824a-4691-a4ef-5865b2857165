#!/usr/bin/env python3
"""
检查用户管理页面是否包含厨房大屏角色选项
"""

import requests
import re
from bs4 import BeautifulSoup

def test_user_management_page():
    """测试用户管理页面"""
    session = requests.Session()
    
    # 1. 登录管理员账户
    print("🔐 登录管理员账户...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    login_response = session.post("http://localhost:5109/login", data=login_data)
    if login_response.status_code not in [200, 302]:
        print(f"❌ 登录失败: {login_response.status_code}")
        return False
    
    print("✅ 登录成功")
    
    # 2. 访问用户管理页面
    print("📄 访问用户管理页面...")
    users_response = session.get("http://localhost:5109/users")
    if users_response.status_code != 200:
        print(f"❌ 无法访问用户管理页面: {users_response.status_code}")
        return False
    
    print("✅ 成功访问用户管理页面")
    
    # 3. 解析HTML内容
    html_content = users_response.text
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 4. 检查添加用户模态框中的角色选项
    print("\n🔍 检查添加用户表单中的角色选项...")
    add_user_form = soup.find('form', {'id': 'addUserForm'})
    if not add_user_form:
        print("❌ 未找到添加用户表单")
        return False
    
    role_select = add_user_form.find('select', {'id': 'role'})
    if not role_select:
        print("❌ 未找到角色选择下拉框")
        return False
    
    # 获取所有角色选项
    role_options = role_select.find_all('option')
    roles_found = []
    kitchen_display_found = False
    
    for option in role_options:
        value = option.get('value', '')
        text = option.get_text(strip=True)
        if value:  # 跳过空选项
            roles_found.append(f"{value} -> {text}")
            if value == 'kitchen_display':
                kitchen_display_found = True
    
    print("📋 发现的角色选项:")
    for role in roles_found:
        print(f"  - {role}")
    
    if kitchen_display_found:
        print("✅ 找到厨房大屏角色选项")
    else:
        print("❌ 未找到厨房大屏角色选项")
    
    # 5. 检查编辑用户模态框中的角色选项
    print("\n🔍 检查编辑用户表单中的角色选项...")
    edit_user_form = soup.find('form', {'id': 'editUserForm'})
    if not edit_user_form:
        print("❌ 未找到编辑用户表单")
        return False
    
    edit_role_select = edit_user_form.find('select', {'id': 'editRole'})
    if not edit_role_select:
        print("❌ 未找到编辑角色选择下拉框")
        return False
    
    edit_role_options = edit_role_select.find_all('option')
    edit_roles_found = []
    edit_kitchen_display_found = False
    
    for option in edit_role_options:
        value = option.get('value', '')
        text = option.get_text(strip=True)
        if value:  # 跳过空选项
            edit_roles_found.append(f"{value} -> {text}")
            if value == 'kitchen_display':
                edit_kitchen_display_found = True
    
    print("📋 编辑表单中的角色选项:")
    for role in edit_roles_found:
        print(f"  - {role}")
    
    if edit_kitchen_display_found:
        print("✅ 编辑表单中找到厨房大屏角色选项")
    else:
        print("❌ 编辑表单中未找到厨房大屏角色选项")
    
    # 6. 检查manageable_roles变量传递
    print("\n🔍 检查manageable_roles变量...")
    if 'kitchen_display' in html_content:
        print("✅ HTML中包含kitchen_display")
    else:
        print("❌ HTML中不包含kitchen_display")
    
    if '厨房大屏用户' in html_content:
        print("✅ HTML中包含'厨房大屏用户'文本")
    else:
        print("❌ HTML中不包含'厨房大屏用户'文本")
    
    # 7. 检查当前用户角色
    print("\n🔍 检查当前用户角色...")
    # 查找用户角色信息
    user_role_pattern = r"user\.role\.value.*?==.*?['\"](\w+)['\"]"
    role_matches = re.findall(user_role_pattern, html_content)
    if role_matches:
        current_role = role_matches[0]
        print(f"📋 当前用户角色: {current_role}")
        
        if current_role == 'admin':
            print("✅ 当前用户是管理员，应该能看到所有角色")
        elif current_role == 'manager':
            print("✅ 当前用户是餐饮经理，应该能看到除admin外的所有角色")
        else:
            print(f"⚠️ 当前用户角色是{current_role}，可能没有用户管理权限")
    
    # 8. 保存HTML内容用于调试
    with open('用户管理页面内容.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    print("\n💾 HTML内容已保存到 '用户管理页面内容.html'")
    
    # 总结
    success = kitchen_display_found and edit_kitchen_display_found
    if success:
        print("\n🎉 检查通过：用户管理页面包含厨房大屏角色选项")
    else:
        print("\n❌ 检查失败：用户管理页面缺少厨房大屏角色选项")
        print("\n🔧 可能的原因:")
        print("1. 模板文件未正确更新")
        print("2. 服务器未重启，使用的是旧版本")
        print("3. manageable_roles列表未正确传递")
        print("4. 当前用户权限不足")
    
    return success

if __name__ == "__main__":
    try:
        success = test_user_management_page()
        exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        exit(1)
