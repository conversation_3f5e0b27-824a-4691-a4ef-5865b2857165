#!/usr/bin/env python3
"""
测试角色列表生成
"""

def test_manageable_roles():
    """测试可管理角色列表"""
    
    print("🧪 测试角色列表生成...")
    
    # 模拟admin用户
    print("\n👑 Admin用户可管理的角色:")
    admin_manageable_roles = ['admin', 'manager', 'waiter', 'kitchen_helper', 'chef_manager', 'business_center', 'kitchen_display']
    for role in admin_manageable_roles:
        role_display = {
            'admin': '系统管理员',
            'manager': '餐饮经理',
            'waiter': '服务员',
            'kitchen_helper': '厨房打荷',
            'chef_manager': '厨师长',
            'business_center': '商务中心',
            'kitchen_display': '厨房大屏用户'
        }.get(role, role)
        print(f"  - {role} -> {role_display}")
    
    # 模拟manager用户
    print("\n👔 Manager用户可管理的角色:")
    manager_manageable_roles = ['manager', 'waiter', 'kitchen_helper', 'chef_manager', 'business_center', 'kitchen_display']
    for role in manager_manageable_roles:
        role_display = {
            'manager': '餐饮经理',
            'waiter': '服务员',
            'kitchen_helper': '厨房打荷',
            'chef_manager': '厨师长',
            'business_center': '商务中心',
            'kitchen_display': '厨房大屏用户'
        }.get(role, role)
        print(f"  - {role} -> {role_display}")
    
    # 检查kitchen_display是否在列表中
    print("\n🔍 检查结果:")
    if 'kitchen_display' in admin_manageable_roles:
        print("✅ Admin用户列表包含kitchen_display")
    else:
        print("❌ Admin用户列表不包含kitchen_display")
        
    if 'kitchen_display' in manager_manageable_roles:
        print("✅ Manager用户列表包含kitchen_display")
    else:
        print("❌ Manager用户列表不包含kitchen_display")

if __name__ == "__main__":
    test_manageable_roles()
