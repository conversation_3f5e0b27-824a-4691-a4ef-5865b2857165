#!/usr/bin/env python3
"""
简单检查用户管理页面内容
"""

import urllib.request
import urllib.parse
import http.cookiejar
import re

def check_user_page():
    """检查用户管理页面"""

    # 创建cookie jar
    cookie_jar = http.cookiejar.CookieJar()
    opener = urllib.request.build_opener(
        urllib.request.HTTPCookieProcessor(cookie_jar),
        urllib.request.HTTPRedirectHandler()
    )

    try:
        # 1. 登录
        print("🔐 登录管理员账户...")
        login_data = urllib.parse.urlencode({
            'username': 'admin',
            'password': 'admin123'
        }).encode('utf-8')

        login_req = urllib.request.Request('http://localhost:5109/login', data=login_data)
        login_response = opener.open(login_req)
        print(f"✅ 登录响应: {login_response.status}")
        print(f"✅ 登录后URL: {login_response.url}")

        # 检查cookies
        print("🍪 当前cookies:")
        for cookie in cookie_jar:
            print(f"  - {cookie.name}={cookie.value}")

        # 2. 访问用户管理页面
        print("📄 访问用户管理页面...")
        users_req = urllib.request.Request('http://localhost:5109/users')
        users_response = opener.open(users_req)
        
        if users_response.status == 200:
            content = users_response.read().decode('utf-8')
            print("✅ 成功获取用户管理页面")
            
            # 3. 检查关键内容
            print("\n🔍 检查关键内容...")
            
            # 检查是否包含kitchen_display
            if 'kitchen_display' in content:
                print("✅ 页面包含 'kitchen_display'")
            else:
                print("❌ 页面不包含 'kitchen_display'")
            
            # 检查是否包含厨房大屏用户
            if '厨房大屏用户' in content:
                print("✅ 页面包含 '厨房大屏用户'")
            else:
                print("❌ 页面不包含 '厨房大屏用户'")
            
            # 查找添加用户表单中的角色选项
            print("\n📋 查找添加用户表单中的角色选项...")
            add_form_pattern = r'<form[^>]*id="addUserForm"[^>]*>.*?</form>'
            add_form_match = re.search(add_form_pattern, content, re.DOTALL)
            
            if add_form_match:
                add_form_content = add_form_match.group(0)
                print("✅ 找到添加用户表单")
                
                # 查找角色选择下拉框
                role_select_pattern = r'<select[^>]*name="role"[^>]*>.*?</select>'
                role_select_match = re.search(role_select_pattern, add_form_content, re.DOTALL)
                
                if role_select_match:
                    role_select_content = role_select_match.group(0)
                    print("✅ 找到角色选择下拉框")
                    
                    # 查找所有option
                    option_pattern = r'<option[^>]*value="([^"]*)"[^>]*>([^<]*)</option>'
                    options = re.findall(option_pattern, role_select_content)
                    
                    print("📋 发现的角色选项:")
                    kitchen_display_found = False
                    for value, text in options:
                        if value:  # 跳过空值
                            print(f"  - {value} -> {text.strip()}")
                            if value == 'kitchen_display':
                                kitchen_display_found = True
                    
                    if kitchen_display_found:
                        print("✅ 在添加用户表单中找到厨房大屏角色选项")
                    else:
                        print("❌ 在添加用户表单中未找到厨房大屏角色选项")
                        
                        # 输出角色选择部分用于调试
                        print("\n🔧 角色选择部分内容:")
                        print(role_select_content[:500] + "..." if len(role_select_content) > 500 else role_select_content)
                else:
                    print("❌ 未找到角色选择下拉框")
            else:
                print("❌ 未找到添加用户表单")
            
            # 保存完整内容用于调试
            with open('用户管理页面完整内容.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print("\n💾 完整页面内容已保存到 '用户管理页面完整内容.html'")
            
        else:
            print(f"❌ 无法访问用户管理页面: {users_response.status}")
            
    except Exception as e:
        print(f"❌ 检查过程中发生错误: {e}")

if __name__ == "__main__":
    check_user_page()
