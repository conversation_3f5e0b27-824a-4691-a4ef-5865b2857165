#!/usr/bin/env python3
"""
部署验证脚本
快速检查退出登录功能修复和厨房大屏权限重构是否正确部署
"""

import os
import sys
import re
from pathlib import Path

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False

def check_file_content(file_path, patterns, description):
    """检查文件内容是否包含指定模式"""
    if not os.path.exists(file_path):
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        missing_patterns = []
        for pattern_name, pattern in patterns.items():
            if not re.search(pattern, content, re.MULTILINE | re.DOTALL):
                missing_patterns.append(pattern_name)
        
        if missing_patterns:
            print(f"❌ {description}: 缺少以下内容: {', '.join(missing_patterns)}")
            return False
        else:
            print(f"✅ {description}: 所有必要内容已包含")
            return True
    except Exception as e:
        print(f"❌ {description}: 读取文件失败 - {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始验证部署...")
    print("=" * 60)
    
    # 检查工作目录
    if not os.path.exists("backend_pure_python"):
        print("❌ 错误: 请在项目根目录运行此脚本")
        return False
    
    success_count = 0
    total_checks = 0
    
    # 1. 检查核心修改文件是否存在
    print("\n📁 检查核心文件...")
    files_to_check = [
        ("backend_pure_python/templates/base.html", "基础模板文件"),
        ("backend_pure_python/models/user.py", "用户模型文件"),
        ("backend_pure_python/main.py", "主应用文件"),
        ("backend_pure_python/templates/users.html", "用户管理模板"),
        ("backend_pure_python/docs/系统功能说明.md", "系统文档")
    ]
    
    for file_path, description in files_to_check:
        total_checks += 1
        if check_file_exists(file_path, description):
            success_count += 1
    
    # 2. 检查退出登录功能修复
    print("\n🚪 检查退出登录功能修复...")
    
    logout_patterns = {
        "增强退出按钮": r"cookies=\['access_token','session_id','user_id','user_role','user_info'\]",
        "清除localStorage": r"localStorage\.clear\(\)",
        "清除sessionStorage": r"sessionStorage\.clear\(\)",
        "服务器端logout": r"fetch\('/logout'",
        "强制退出函数": r"function forceLogout\(\)"
    }
    
    total_checks += 1
    if check_file_content("backend_pure_python/templates/base.html", logout_patterns, "退出登录功能"):
        success_count += 1
    
    # 3. 检查厨房大屏权限重构
    print("\n🏠 检查厨房大屏权限重构...")
    
    # 检查用户模型
    user_model_patterns = {
        "新角色定义": r"KITCHEN_DISPLAY\s*=\s*[\"']kitchen_display[\"']",
        "权限重构": r"UserRole\.KITCHEN_DISPLAY:\s*\[.*kitchen\.display.*\]"
    }
    
    total_checks += 1
    if check_file_content("backend_pure_python/models/user.py", user_model_patterns, "用户模型权限重构"):
        success_count += 1
    
    # 检查主应用文件
    main_app_patterns = {
        "可管理角色列表": r"manageable_roles.*kitchen_display",
        "厨房大屏路由权限": r"kitchen\.display.*权限重构",
        "API权限检查": r"has_permission\('kitchen\.display'\)"
    }
    
    total_checks += 1
    if check_file_content("backend_pure_python/main.py", main_app_patterns, "主应用权限检查"):
        success_count += 1
    
    # 检查用户管理模板
    user_template_patterns = {
        "角色选项": r"kitchen_display.*厨房大屏用户",
        "角色显示": r"kitchen_display.*厨房大屏用户",
        "自定义样式": r"bg-purple"
    }
    
    total_checks += 1
    if check_file_content("backend_pure_python/templates/users.html", user_template_patterns, "用户管理模板"):
        success_count += 1
    
    # 4. 检查新增文件
    print("\n📄 检查新增文件...")
    
    new_files = [
        ("backend_pure_python/migrations/add_kitchen_display_role.py", "数据库迁移脚本"),
        ("test_logout_and_permissions.py", "功能测试脚本"),
        ("退出登录和权限重构修复报告.md", "修复报告")
    ]
    
    for file_path, description in new_files:
        total_checks += 1
        if check_file_exists(file_path, description):
            success_count += 1
    
    # 5. 检查数据库迁移脚本内容
    print("\n🗄️ 检查数据库迁移脚本...")
    
    migration_patterns = {
        "创建厨房大屏用户": r"kitchen_display_user\s*=\s*User",
        "默认密码设置": r"kitchen123",
        "角色分析": r"role_counts",
        "迁移建议": r"迁移建议"
    }
    
    total_checks += 1
    if check_file_content("backend_pure_python/migrations/add_kitchen_display_role.py", migration_patterns, "数据库迁移脚本"):
        success_count += 1
    
    # 6. 检查测试脚本内容
    print("\n🧪 检查测试脚本...")
    
    test_patterns = {
        "退出登录测试": r"test_logout",
        "权限测试": r"test_kitchen_display_access",
        "用户管理测试": r"test_user_management_roles",
        "自动重登录防护": r"test_auto_relogin_prevention"
    }
    
    total_checks += 1
    if check_file_content("test_logout_and_permissions.py", test_patterns, "功能测试脚本"):
        success_count += 1
    
    # 总结
    print("\n" + "=" * 60)
    print(f"📊 验证结果: {success_count}/{total_checks} 项检查通过")
    
    if success_count == total_checks:
        print("🎉 所有检查通过！部署验证成功")
        print("\n📋 下一步操作:")
        print("1. 备份数据库: cp backend_pure_python/restaurant.db backend_pure_python/restaurant.db.backup")
        print("2. 执行迁移: python backend_pure_python/migrations/add_kitchen_display_role.py")
        print("3. 重启服务")
        print("4. 运行测试: python test_logout_and_permissions.py")
        return True
    else:
        print("⚠️ 部分检查失败，请检查相关文件")
        print("\n🔧 可能的问题:")
        print("- 文件未正确保存或部署")
        print("- 代码修改不完整")
        print("- 文件路径错误")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
